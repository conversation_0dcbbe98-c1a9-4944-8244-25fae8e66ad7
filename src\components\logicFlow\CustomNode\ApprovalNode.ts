import { RectNode, RectNodeModel, h } from '@logicflow/core';
import { v4 as uuidv4 } from 'uuid';
import { permissions, emailNotices } from '/@/components/logicFlow/config.js';
class ApprovalNode extends RectNode {}
class ApprovalModel extends RectNodeModel {
	constructor(data, graphModel) {
		super(data, graphModel);
	}
	// 自定义节点形状属性
	initNodeData(data) {
		if (!data.properties) {
			data.properties = {
				name: this.findNodeByName(),
				entity: '',
				approveNoticeData: this.initEmailNotices(),
				permissions,
			};
		}
		super.initNodeData(data);
		this.width = 150;
		this.height = 60;
	}
	findNodeByName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `node${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
	setTriggerName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.execute.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `SignalReceived${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}

	initEmailNotices() {
		const approveNoticeData = {};
		emailNotices.forEach((opt) => {
			approveNoticeData[opt.noticeType] = { value: 0 };
		});
		return approveNoticeData;
	}
}

export default {
	type: 'ApprovalNode',
	view: ApprovalNode,
	model: ApprovalModel,
};
