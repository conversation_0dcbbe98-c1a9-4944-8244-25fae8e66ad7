<template>
	<div class="scTable" style="height: 100%" ref="scTableMain">
		<div class="scTable-table">
			<el-table
				:data="tableData.data"
				v-loading="tableData.loading"
				@row-dblclick="onDetail"
				height="calc(100%)"
				table-layout="fixed"
				lazy
				highlight-current-row
				scrollbar-always-on
				row-key="id"
				@selection-change="selectionChange"
				:row-style="{ height: '40px' }"
				:cell-style="{ padding: '0px' }"
				stripe border
			>
				<template #empty>
					<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"> </el-empty>
				</template>
				<el-table-column type="selection"></el-table-column>
				<el-table-column :label="$t('message.userFields.userName')" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.userName }}</span>
					</template>
				</el-table-column>

				<el-table-column :label="$t('message.userFields.realName')" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.realName }}</span>
					</template>
				</el-table-column>

				<el-table-column :label="$t('message.userFields.mobile')" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.mobile }}</span>
					</template>
				</el-table-column>

				<el-table-column :label="$t('message.userFields.status')">
					<template #default="{ row }">
						<el-tag type="danger" v-if="row.status == '1'">{{ $t('message.userFields.Inactive') }}</el-tag>
						<el-tag type="success" v-if="row.status == '0'">{{ $t('message.userFields.Active') }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column :label="$t('message.userFields.orgName')" v-if="false" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.orgName }}</span>
					</template>
				</el-table-column>

				<el-table-column :label="$t('message.userFields.createTime')">
					<template #default="{ row }">
						<span>{{ row.createTime }}</span>
					</template>
				</el-table-column>
				<el-table-column fixed="right" align="left" :label="$t('message.page.actions')">
					<template #default="{ row }">
						<el-button v-auth="'systemRole.Delete'" size="mini" type="text" style="color: #ff3a3a" @click="onDelete(row)">
							{{ $t('message.page.actionsDelete') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="scTable-page">
			<el-pagination
				@size-change="onSizechange"
				@current-change="onCurrentChange"
				:pager-count="5"
				:page-sizes="[10, 20, 30]"
				v-model:current-page="tableData.param.pageIndex"
				background
				v-model:page-size="tableData.param.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="tableData.total"
			>
			</el-pagination>
		</div>
	</div>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate, formatDateTime } from '/@/utils/formatTime';

import userApi from '/@/api/user/index';
import userRoleApi from '/@/api/userRole';
import { useI18n } from 'vue-i18n';

export default defineComponent({
	name: 'userIndex',
	components: {},
	setup() {
		const { t } = useI18n();
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					roleId: 0,
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
				},
			},
			detailObj: {},
		});

		//初始化
		const onInit = (roleId: number) => {
			state.tableData.loading = true;
			state.tableData.param.roleId = roleId;
			userRoleApi
				.GetRoleUsers(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			//onInit();
		};

		// 添加
		const onAdd = () => {
			createOrEditRef.value.openDialog({ action: 'Create' });
			//router.push('/user/Create');
		};
		// 修改
		const onEdit = (row: Object) => {
			createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			//router.push('/user/edit/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				userRoleApi
					.RemoveRoleUser({ roleId: state.tableData.param.roleId, userId: row.id })
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit(state.tableData.param.roleId);
					})
					.catch((rs) => {});
			});
		};

		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			userApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
			});
		};

		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.tableData.param.ids = [];
			} else {
				state.tableData.param.ids = ids_arr;
			}

			userRoleApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'RoleUser_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 页面加载时
		onMounted(() => {});
		return {
			printMain,
			formatStrDate,
			formatDateTime,
			createOrEditRef,
			onExportAllRecord,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.aside {
	margin: 10px;
	background-color: #fff;
}

.dep-tree {
	margin-top: 10px;
}
</style>
