.el-card-header {
	background: #fff;
	border-bottom: 1px solid #e6e6e6;
	padding: 13px 15px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.left-panel {
	display: flex;
	align-items: center;
}
.right-panel {
	display: flex;
	align-items: center;
}

.el-scrollbar__view {
	height: 100%;
}

.scTable {
}

.scTable-table {
	height: calc(100% - 50px);
}

.scTable-page {
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
}

.scTable-do {
	white-space: nowrap;
}

.scTable:deep(.el-table__footer) .cell {
	font-weight: bold;
}
// .el-table__header-wrapper tr th{
// 	background-color: #F2F2F2 !important;
// }

.el-table th.is-sortable {
	transition: 0.1s;
}
.el-table th.is-sortable:hover {
	background: #f2f2f2;
}
.el-table .el-table__body-wrapper {
	background: #f6f8f9;
}

.el-header {
	background: #fff;
	padding: 13px 15px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.el-header .left-panel {
	display: flex;
	align-items: center;
}
.el-header .right-panel {
	display: flex;
	align-items: center;
}
.el-header .right-panel > * + * {
	margin-left: 10px;
}

.tablelist .el-main {
	overflow: hidden;
}
.el-main.nopadding {
	padding: 0;
	background: #fff;
}

/*日志管理页面*/
.logs-index-container .scTable-table {
	height: calc(100% - 90px);
}

.logs-index-container .el-tabs__content,
.logs-index-container .el-main,
.logs-index-container .el-tabs,
.logs-index-container .el-tab-pane {
	height: 100%;
}

.list-search-container {
	margin-top: 20px;
}
.list-search-header {
	margin-bottom: 10px;
}

/*dialog*/
.full-dialog > .el-dialog__header {
	padding: 0;
}

.el-dialog.is-fullscreen {
	padding: 0 !important;
}

.full-dialog > .el-dialog__body {
	padding: 0px;
	height: 100%;
	overflow: hidden;
}
.full-dialog .bd {
	height: calc(100vh - 60px);
	overflow: hidden;
	background: #ebeef5;
	padding: 10px;
}

.full-dialog-header {
	height: 60px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 0 20px;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}
.full-dialog-header .header-title {
	width: 410px;
	height: 60px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.full-dialog-header .header-logo {
	width: auto;
	height: 60px;
	vertical-align: top;
	margin-right: 3px;
	font-size: 30px;
	color: #fff;
}
.full-dialog-header .header-txt {
	display: inline-block;
	margin: 0;
	color: #333;
	font-size: 18px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 150px;
	cursor: pointer;
}
.full-dialog-header .steps {
	width: 400px;
	padding: 6px 20px;
	background: #fff;
	justify-items: flex-start;
	-ms-flex-negative: 0;
	flex-shrink: 0;
}
.full-dialog-header .options {
	width: 410px;
	text-align: right;
}

.full-dialog .bd .basic-box {
	width: 800px;
	height: 100%;
	margin: 0px auto;
}

.full-dialog .bd .basicForm {
	background: #fff;
	padding: 20px;
	border-radius: 4px;
	height: 100%;
	overflow: auto;
	overflow-x: hidden;
}

.designlf {
	height: 100%;
}

.approved-table .el-alert {
	border-width: 0px;
}

.role-tree-right {
	.el-card__body {
		height: 100%;
	}

	.full-height-card {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.el-card__header {
		flex: 0 0 auto; /* 使 header 占据其内容所需的最小空间 */
	}

	.el-card__body {
		flex: 1 1 auto; /* 使 body 占据剩余空间 */
	}

	.full-tabs {
		height: 100%;
		.el-tab-pane {
			height: 100%;
		}
		.scTable-table {
			height: calc(100% - 80px);
		}
	}
}

.dic-tree-left{
	.el-card__body {
		height: 90%;
		overflow-y: auto;
		overflow-x: hidden;
	}
}

.dic-tree-right{
	.el-card__body {
		height: 100%;
	}
	.scTable-table {
		height: calc(100% - 80px);
	}
}