﻿<template>
	<div class="list-page-layout">
		<el-container style="height: 100%; background-color: #ebeef5">
			<el-header style="height: auto">
				<el-card class="list-search-card mb1" shadow="never" :body-style="{ paddingTop: '6px' }">
					<div class="list-index-search">
						<el-form label-width="120px" @keyup.enter="onSearch">
							<el-row :gutter="35">
								<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.emailFields.emailSubject')">
										<el-input v-model="tableData.param.subject" clearable class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.emailFields.toEmail')">
										<el-input v-model="tableData.param.toAddrs" clearable class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.emailFields.ccEmail')">
										<el-input v-model="tableData.param.ccAddrs" clearable class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
									<el-form-item :label="$t('message.emailFields.emailStatus')">
										<el-select v-model="tableData.param.status"
											:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
											class="w-20">
											<el-option label="Draft" :value="0"></el-option>
											<el-option label="Sending" :value="1"></el-option>
											<el-option label="Send" :value="2"></el-option>
											<el-option label="Failed" :value="3"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<!-- </el-row> -->
								<!-- <el-row :gutter="35"> -->
								<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch"> {{
											$t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet"> {{
											$t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>

			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'sendEmail.Visit'" size="small" type="success" class="ml10" @click="onAdd">
						<el-icon>
							<ele-Plus />
						</el-icon>
						{{ $t('message.emailButtons.newEmail') }}
					</el-button>

					<el-tag class="ml-2 ml10" type="danger">*Note: It keeps the email records for one year
						only.</el-tag>
				</div>
				<div class="right-panel">
					<el-button v-auth="'emailList.BitchDelete'" type="primary" size="small" icon="ele-Delete"
						:disabled="tableData.selection.length == 0" @click="onDeleteByList">
						{{ $t('message.page.buttonDelete') }}
					</el-button>

					<el-dropdown>
						<el-button v-auth="'emailList.Export'" type="primary" size="small" class="ml10">
							<el-icon>
								<ele-ArrowDownBold />
							</el-icon>
							{{ $t('message.page.buttonExport') }}
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onExportAllRecord(0)">{{
									$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
								<el-dropdown-item @click="onExportAllRecord(1)">{{
									$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</el-header>

			<el-main ref="printMain" style="flex: 1">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)"
							@selection-change="selectionChange" :row-style="{ height: '40px' }"
							:cell-style="{ padding: '0px' }" stripe border>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column :label="$t('message.emailFields.emailSubject')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.subject }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.toEmail')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.toAddrs }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.emailFields.emailStatus')">
								<template #default="{ row }">
									<el-tag type="info" v-if="row.status == '0'">{{ $t('message.emailStatus.draft')
									}}</el-tag>
									<el-tag v-if="row.status == '1'">{{ $t('message.emailStatus.sending') }}</el-tag>
									<el-tag type="success" v-if="row.status == '2'">{{ $t('message.emailStatus.sended')
									}}</el-tag>
									<el-tag type="danger" v-if="row.status == '3'">{{ $t('message.emailStatus.failed')
									}}</el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createBy')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.createdByName }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.createAt')">
								<template #default="{ row }">
									<span>{{ row.createdAt }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" align="left" :label="$t('message.page.Action')">
								<template #default="{ row }">
									<el-button v-auth="'viewEmail.Visit'" size="mini" type="text" @click="onEdit(row)">
										{{ $t('message.page.buttonVisit') }}
									</el-button>
									<el-button v-auth="'emailList.Delete'" size="mini" type="text"
										@click="onDelete(row)">
										{{ $t('message.page.buttonDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="tableData.total" small>
						</el-pagination>
					</div>
				</div>

				<CreateOrEdit ref="createOrEditRef" @fetchData="onInit"></CreateOrEdit>
				<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
					<Detail ref="detailRef" :info="detailObj"></Detail>
				</el-drawer>
			</el-main>
		</el-container>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';
import { useI18n } from 'vue-i18n';

import emailsApi from '/@/api/emails/index';
import Detail from './components/detail.vue';
import CreateOrEdit from './components/createOrEdit.vue';

export default defineComponent({
	name: 'emailsList',
	components: { Detail, CreateOrEdit },
	setup() {
		const { t } = useI18n();
		const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			emailsApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		const onSearchReSet = () => {
			state.tableData.param = {
				pageIndex: 1,
				pageSize: 20,
				searchKey: '',
				order: 'id',
				sort: 'desc',
				subject: '',
				ccAddrs: '',
				toAddrs: '',
				status: '',
			};
			onInit();
		};

		// 添加
		const onAdd = () => {
			//createOrEditRef.value.openDialog({ action: 'Create' });
			router.push('/emails/sendEmail');
		};
		// 修改
		const onEdit = (row: Object) => {
			//createOrEditRef.value.openDialog({ action: 'Edit', id: row.id });
			router.push('/emails/sendEmail/' + row.id);
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				emailsApi
					.DeleteByKey(row.id)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => { });
			});
		};
		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal: false,
				}
			).then(() => {
				var items = state.tableData.selection.map((a: { id: any }) => {
					return a.id;
				});

				emailsApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};
		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			emailsApi.Detail(row.id).then((rs) => {
				state.detailObj = rs.data;
				state.infoDrawer = true;
			});
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.id;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			state.tableData.param.ids = ids_arr;

			emailsApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = new Date().getTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			printMain,
			formatStrDate,
			onPrint,
			createOrEditRef,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			onSearchReSet,
			onExportAllRecord,
			...toRefs(state),
		};
	},
});
</script>
