﻿<template>
	<div class="list-page-layout">
		<el-container style="height: 100%">
			<!-- <el-header class="mb2" style="height: 40px">
			<el-form style="width: 500px">
				<el-form-item :label="$t('message.companyFields.name')">
					<el-input v-model="tableData.param.name" 
						clearable style="max-width: 230px">
					</el-input>
					<el-button size="small" type="primary" class="ml10" @click="onSearch">
						<el-icon>
							<ele-Search />
						</el-icon>
					</el-button>
				</el-form-item>
			</el-form>
		</el-header> -->
			<el-header style="height: auto">
				<el-card shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search mb5">
						<el-form @keyup.enter="onSearch" label-width="150px">
							<el-row :gutter="35">
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.name')">
										<el-input v-model="tableData.param.name" clearable class="w-20"> </el-input>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.code')">
										<el-input v-model="tableData.param.code" clearable class="w-20"> </el-input>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.companyType')">
										<el-select v-model="tableData.param.companyType" filterable clearable
											:placeholder="$t('message.page.selectKeyPlaceholder')" class="w-20">
											<el-option v-for="item in companyTypeData" :label="item.itemValue"
												:value="item.itemValue" :key="item.itemId"
												:description="item.description" />
										</el-select>
									</el-form-item>
								</el-col>


								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.userType')" prop="userPermission">
										<el-select v-model="tableData.param.userPermission"
											:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
											class="w-20">
											<el-option v-for="item in userTypeData" :label="item.itemName"
												:value="item.itemName" :key="item.itemId"></el-option></el-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.businessUnit')" prop="businessUnit">
										<el-select v-model="tableData.param.businessUnit"
											:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
											class="w-20">
											<el-option v-for="item in businessUnitData"
												:label="item.description ? `${item.itemValue} - ${item.description}` : item.itemValue"
												:value="item.itemValue" :key="item.itemId"
												:description="item.description" />
										</el-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.status')">
										<el-select :placeholder="$t('message.page.selectKeyPlaceholder')"
											v-model="tableData.param.statusSearch" clearable class="w-20">
											<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
											<el-option :label="$t('message.userFields.Inactive')"
												:value="1"></el-option>
										</el-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.createAt')" class="date-form-item">
										<div class="date-container">
											<MyDate v-model:input="tableData.param.createAtStart"
												style="width: 130px !important;" />
											<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
											<MyDate v-model:input="tableData.param.createAtEnd"
												style="width: 130px !important;" />
										</div>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch" class="mt6"> {{
											$t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet" class="mt6"> {{
											$t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>

			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'createCompany.Visit'" size="small" type="primary" class="ml10" @click="onAdd">
						{{ $t('message.organizationButton.createCompany') }}
					</el-button>

					<el-button type="danger" size="small" :disabled="tableData.selection.length == 0" v-if="false"
						@click="onDeleteByList">
						{{ $t('message.page.buttonDeleteBatch') }}
					</el-button>
				</div>
				<div class="right-panel">
					<el-dropdown v-auth="'systemCompany.Export'">
						<el-button type="primary" size="small" class="ml10">
							<el-icon>
								<ele-ArrowDownBold />
							</el-icon>
							{{ $t('message.page.buttonExport') }}
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onExportAllRecord(0)">{{
									$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
								<el-dropdown-item @click="onExportAllRecord(1)">{{
									$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</el-header>
			<el-main class="nopadding" id="printMain">
				<div class="scTable" ref="scTableMain">
					<div class="scTable-table">
						<el-table :data="tableData.data" v-loading="tableData.loading" stripe border
							@selection-change="selectionChange" :default-expand-all="true"
							:tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="orgId"
							:row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
							height="calc(100vh - 320px)">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" width="100" align="center" />

							<el-table-column :label="$t('message.organizationTable.name')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.name }}</span>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.organizationTable.code')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.code }}</span>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.companyFields.companyType')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.companyType }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.companyFields.businessUnit')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.businessUnit }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.companyFields.userType')" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.organizationType }}</span>
								</template>
							</el-table-column>
							<el-table-column v-if="false" :label="$t('message.organizationTable.description')"
								show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.description }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable label="Status" prop="status">
								<template #default="{ row }">
									<el-tag type="danger" v-if="row.status == '1'">{{ $t('message.userFields.Inactive')
									}}</el-tag>
									<el-tag type="success" v-if="row.status == '0'">{{ $t('message.userFields.Active')
									}}</el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.organizationTable.createdAt')">
								<template #default="{ row }">
									<span>{{ row.createAt }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" align="left" :label="$t('message.page.actions')">
								<template #default="{ row }">
									<el-button v-if="false" size="mini" type="text" @click="onDetail(row)">
										{{ $t('message.page.actionsView') }}
									</el-button>
									<el-button v-auth="'editCompany.Visit'" size="mini" type="text"
										@click="onEdit(row)">
										{{ $t('message.page.actionsEdit') }}
									</el-button>
									<el-button v-auth="'systemCompany.Delete'" size="mini" type="text"
										style="color: #ff3a3a" @click="onDelete(row)">
										{{ $t('message.page.actionsDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page" style="display: none">
						<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="tableData.total" small>
						</el-pagination>
					</div>
				</div>

				<el-drawer v-model="infoDrawer" :title="$t('message.page.detailTitle')" :size="600" destroy-on-close>
					<Detail ref="detailRef" :info="detailObj"></Detail>
				</el-drawer>
			</el-main>
		</el-container>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import { nextTick, toRefs, reactive, onMounted, ref, defineComponent, getCurrentInstance, onUnmounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatStrDate, formatDateTime } from '/@/utils/formatTime';
import dictItemApi from '/@/api/dictItem/index';
import organizationApi from '/@/api/organization/index';
import Detail from './components/detail.vue';
import { useI18n } from 'vue-i18n';
import mittBus from '/@/utils/mitt';
import MyDate from '/@/components/ticket/ticketDate.vue';

export default defineComponent({
	name: 'systemCompany',
	components: { Detail, MyDate },
	setup() {
		const router = useRouter();

		const { proxy } = <any>getCurrentInstance();
		const { t } = useI18n();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					name: '',
					code: '',
					statusSearch: '',
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'orgId',
					ids: [],
					ischeckType: 0,
					companyType: '',
					userPermission: '',
					businessUnit: '',
					listSearch: '',
					createAtStart: '',
					createAtEnd: ''
				},
			},
			infoDrawer: false,
			detailObj: {},
			userTypeData: [],
			companyTypeData: [],
			businessUnitData: [] as any,
		});

		//初始化
		const onInit = () => {
			var dictArr = ['UserType', 'Industry', 'BusinessUnit'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.userTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'UserType';
				})?.items;

				state.companyTypeData = rs.data?.find((a: any) => {
					return a.dictValue === 'Industry';
				})?.items;

				state.businessUnitData = rs.data?.find((a: any) => {
					return a.dictValue === 'BusinessUnit';
				})?.items;
			});

			state.tableData.loading = true;

			if (state.tableData.param.name
				|| state.tableData.param.code
				|| state.tableData.param.companyType
				|| state.tableData.param.userPermission
				|| state.tableData.param.businessUnit
				|| state.tableData.param.statusSearch
				|| state.tableData.param.createAtStart
				|| state.tableData.param.createAtEnd
			) {
				state.tableData.param.listSearch = 'Yes'
			}

			organizationApi
				.Tree(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					//state.tableData.total = rs.totalCount;
				})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			if (state.tableData.param.createAtEnd.length > 0 && state.tableData.param.createAtStart > state.tableData.param.createAtEnd) {
				ElMessage.warning(t('message.ticketSearch.searchDataUnvalid'));
				return;
			}

			onInit();
		};

		// 添加
		const onAdd = () => {
			//createOrEditRef.value.openDialog({ action: 'Create' });
			router.push('/permission/company/create');
		};
		// 修改
		const onEdit = (row: any) => {
			// console.log('row.orgTypeId', row.orgTypeId);
			//createOrEditRef.value.openDialog({ action: 'Edit', orgId: row.orgId });
			if (row.orgType === 2) {
				//OrgType=1时对应company.Id ;OrgType=2时对应 department.id
				//onCreateOrEditDep('Edit', row.orgTypeId);
				return;
			}
			//OrgType=1时对应company.Id ;OrgType=2时对应 department.id
			router.push('/permission/company/edit/' + row.orgTypeId);
		};

		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(t('message.userFields.dlgMasterUser'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				organizationApi
					.DeleteByKey(row.orgId)
					.then((rs) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						onInit();
					})
					.catch((rs) => {
						nextTick(() => {
							ElMessageBox.confirm(t('message.page.existCanNotDelete'), t('message.page.dlgTip'), {
								confirmButtonText: t('message.page.buttonOk'),
								cancelButtonText: t('message.page.cannel'),
								type: 'warning',
								closeOnClickModal: false,
							});
						});
					});
			});
		};

		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal: false,
				}
			).then(() => {
				var items = state.tableData.selection.map((a) => {
					return a.orgId;
				});
				organizationApi.DeleteMany(items).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					onInit();
				});
			});
		};

		//详细信息
		const onDetail = (row: any) => {
			state.detailObj = {};

			state.detailObj = row;
			state.infoDrawer = true;
		};

		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};

		//打印
		const onPrint = () => { };

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};

		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.orgId;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}

			if (selectAll == 0) {
				state.tableData.param.ids = [];
			} else {
				state.tableData.param.ids = ids_arr;
			}

			organizationApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Company_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		// 页面加载时
		onMounted(() => {
			onInit();

			//事件监听器
			mittBus.on('RefreshCompanyList', () => {
				onInit();
			});
		});

		// 页面销毁时
		onUnmounted(() => {
			mittBus.off('RefreshCompanyList');
		});
		const onSearchReSet = () => {
			initFormData();
			onInit();
		};
		const initFormData = () => {
			state.tableData.param = {
				name: '',
				code: '',
				statusSearch: '',
				pageIndex: 1,
				pageSize: 10,
				searchKey: '',
				order: 'orgId',
				ischeckType: 0,
				ids: [],
				companyType: '',
				userPermission: '',
				businessUnit: '',
				listSearch: '',
				createAtStart: '',
				createAtEnd: ''
			} as any;
		};
		return {
			formatStrDate,
			formatDateTime,
			onExportAllRecord,
			onPrint,
			selectionChange,
			onInit,
			onAdd,
			onEdit,
			onDetail,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
			onSearchReSet,
		};
	},
});
</script>


<style scoped lang="scss">
.list-index-search {
	.date-container {
		display: flex;
		align-items: center;
	}

	:deep(.el-text) {
		margin: 0 10px;
	}
}
</style>
