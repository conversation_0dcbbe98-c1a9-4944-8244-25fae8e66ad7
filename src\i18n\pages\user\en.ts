﻿export default {
	userSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
	},
	userButtons: {
		//非按钮
		Modify: 'Modify',
		Save: 'Save',
		Delegate: 'Delegate',
		buttonNew: 'New User',
		buttonAdd: 'Add',
		actionHistory: 'Action History',
	},
	userFields: {
		//table 列名
		id: 'Id',
		mobile: 'Phone',
		email: 'Email',
		orgName: 'Company',
		loginName: 'Login Name',
		loginPWD: 'Login PWD',
		fullName: 'Full Name',
		realName: 'First Name',
		lastName: 'Last Name',
		switchLanguage: 'Switch Language',
		status: 'Status',
		delegateto: 'Delegate to',
		departmentId: 'DepartmentId',
		remark: 'Remark',
		createTime: 'Created at',
		updateTime: 'Update Time',
		lastErrorTime: 'Last Error Time',
		errorCount: 'Error Count',
		userName: 'Username',
		sex: 'Sex',
		age: 'Age',
		birth: 'Birth',
		address: 'Address',
		isDeleted: 'Is Deleted',
		positionId: 'Position Id',
		lastLoginTime: 'Last Login Time',
		lastLoginIp: 'Last Login Ip',
		roleAssign: 'Roles',
		userType: 'User Type',
		sexSecret: 'Secret',
		sexMale: 'Male',
		sexWomen: 'Female',
		Active: 'Active',
		Inactive: 'Inactive',
		customerAssign: 'Projects',
		SelectTheDataYouWantToExport: 'Select the data you want to export',
		masterUser: 'Master User',
		dlgMasterUser: 'Are you sure you want to operate on this record ?',
		delegateTo: 'Delegate To',
		delegateDate: 'Delegate Date',
		to: 'To',
		approverType: 'Delegate Type',
		selectApprover: 'Delegate to',
		startDate: 'Start Date',
		endDate: 'End Date',
		canEmail: 'Can Email',
		modifiedTime: 'Modified Time',
		location: 'Location',
		timeZone: 'Time Zone',
		jobTitle: 'Job Title',
		escalation:'Escalation',
		projects: 'Projects',
		delegateBy: 'Delegate By',
		approve: 'On/Off',
		lockout: 'Account Lockout',
		privateTicket: 'Create Private Ticket',
		role: 'Role',
		groups: 'Groups',
		creationDate: 'Create Date',
		delegateSettings: 'Delegate Settings',
		password: 'Change My Password',
		emailChange: 'Change My Email',
		profile: 'My Profile',
		ticket: 'Ticket',
		IncidentEmailNotfications: 'Incident Email Notfications'
	},
	PersonalTitle: {
		PersonalInfo: 'Personal info',
		InfoSetting: 'Info Setting',
		SecuritySettings: 'Security Settings',
		PasswordUpdate: 'password update',
		MyEmailAddress: 'My Email Address',
	},
};
