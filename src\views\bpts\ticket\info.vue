<template>
	<div class="ticket-info_layout">
		<div class="ticket-info-left">
			<el-card shadow="always" style="height: 100%">
				<template #header>
					<div class="card-header">
						<span>{{ $t('message.ticketFields.information') }}</span>&nbsp;
						<template v-if="readonly_formData !== undefined">
							<span>
								<el-tag v-if="readonly_formData !== '' && !formData.onHold" class="ml-2" :type="readonly_formData === 'New'
									? 'success'
									: readonly_formData === 'Open'
										? 'info'
										: readonly_formData === 'Replied'
											? 'warning'
											: 'danger'
									">
									{{ readonly_formData }}
								</el-tag>

								<el-tag v-if="formData.onHold" class="ml-2">Scheduled</el-tag>
							</span>
							<el-button v-if="false" class="button" type="text">
								{{ $t('message.ticketButtons.detail') }}
							</el-button>
						</template>
						<span style="padding-left: 10px; vertical-align: -10px" v-if="ticketId > 0">Related
							Ticket</span>
						<span v-if="ticketId > 0" style="margin-top: -15px;">
							<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" clearable filterable
								@change="onRelateTicket" style="padding-left: 5px; width: 158px; margin-top: 17px">
								<el-option v-for="item in formData.relatedTicket" :label="+item['ticketNumber']"
									:value="item['ticketNumber']" :key="item['ticketNumber']" />
							</el-select>
						</span>
					</div>
				</template>

				<el-form ref="ruleFormRef1" :model="formData" :rules="rules">
					<template v-if="currentUser.userType == 'Staff Member' || currentUser.userType == 'End User'">
						<el-descriptions :column="2" border direction="vertical">
							<el-descriptions-item :span="2" v-if="ticketId > 0">
								<template #label>
									<div>{{ $t('message.ticketFields.caseNumber') }}</div>
								</template>
								<el-form-item>
									<el-input v-model="formData.ticket_Number" disabled />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item :span="2" v-if="route.name == 'ScheduleTicketCreate'">
								<template #label>
									<div>{{ $t('message.ticketFields.parentTicket') }}</div>
								</template>
								<el-form-item>
									<div style="display: flex; width: 100%">
										<div style="width: 95%">
											<el-input v-model="formData.parent_ticket_Number" disabled />
										</div>
										<div
											style="width: 5%; display: flex; flex-direction: column; justify-content: center; align-items: center">
											<img v-show="isNotEmptyOrNull(formData.parent_ticket_Number) ? false : true"
												src="../../../assets/SearchNew.gif"
												style="height: 21px; cursor: pointer" @click="SelectParentTicket()" />
											<img v-show="isNotEmptyOrNull(formData.parent_ticket_Number) ? true : false"
												src="../../../assets/close_inline.gif"
												style="height: 14px; cursor: pointer" @click="ClearParentTicket()" />
										</div>
									</div>
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.customer') }}</div>
								</template>
								<el-form-item prop="ticket_Customer">
									<el-tree-select ref="localCompanyTree"
										:disabled="disabledCustomer || formData.ticket_Parent_Id > 0 || (ticketId > 0 && !hasApprovedPermission)"
										v-model="formData.ticket_Customer" :data="customerOpts"
										:render-after-expand="false" node-key="value" show-checkbox
										:check-strictly="true" check-on-click-node style="width: 100%"
										:auto-expand-parent="true" :default-expand-all="true" filterable
										@check="checkChangeCustomer" @clear="clearCustomer" clearable
										:placeholder="$t('message.page.selectKeyPlaceholder')" />
								</el-form-item>
							</el-descriptions-item>


							<el-descriptions-item>
								<template #label>
									<span>{{ $t('message.ticketFields.BusinessUnit') }}</span>
								</template>
								<el-form-item prop="businessUnit">
									<el-select :disabled="disabledBusinessUnit" v-model="formData.businessUnit"
										filterable :placeholder="$t('message.page.selectKeyPlaceholder')">
										<el-option v-for="item in businessUnitData"
											:label="item.description ? `${item.itemValue} - ${item.description}` : item.itemValue"
											:value="item.itemValue" :key="item.itemId"
											:description="item.description" />
									</el-select>
								</el-form-item>
							</el-descriptions-item>


							<el-descriptions-item :span="2" v-if="showTicketProject">
								<template #label>
									<span>{{ $t('message.ticketFields.project') }}</span>
								</template>
								<el-form-item prop="ticket_Project">
									<el-tree-select ref="localProjectTree"
										:disabled="disabledProject || formData.ticket_Parent_Id > 0 || (ticketId > 0 && !hasApprovedPermission)"
										v-model="formData.ticket_Project" :data="projectOpts"
										:render-after-expand="false" node-key="value" show-checkbox
										:check-strictly="true" check-on-click-node style="width: 100%"
										:auto-expand-parent="true" :default-expand-all="true" filterable
										@current-change="checkChangeProject" @clear="clearProject" clearable
										:placeholder="$t('message.page.selectKeyPlaceholder')" />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.category') }}</div>
								</template>
								<el-form-item prop="ticket_Category">
									<el-select v-model="formData.ticket_Category"
										:placeholder="$t('message.page.selectKeyPlaceholder')" clearable filterable
										@change="SetCategory" class="w100"
										:disabled="ticketId > 0 && !hasApprovedPermission">
										<el-option v-for="item in categoryOpts"
											:label="item['category_Code'] + ' - ' + item['category_Description']"
											:value="item['category_Code']" :key="item['id']" />
									</el-select>
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.priority') }}</div>
								</template>
								<el-form-item prop="ticket_Priority">
									<el-select v-model="formData.ticket_Priority"
										:placeholder="$t('message.page.selectKeyPlaceholder')" filterable clearable
										class="w100" :disabled="ticketId > 0 && !hasApprovedPermission">
										<el-option v-for="item in priorityOpts" :label="item['itemName']"
											:value="item['itemValue']" :key="item['itemId']"></el-option>
									</el-select>
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.firstName') }}</div>
								</template>
								<el-form-item prop="ticket_First_Name">
									<el-input v-model="formData.ticket_First_Name"
										:disabled="currentUser.userType == 'End User' || (ticketId > 0 && !hasApprovedPermission)"
										clearable />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.lastName') }}</div>
								</template>
								<el-form-item prop="ticket_Last_Name">
									<el-input v-model="formData.ticket_Last_Name"
										:disabled="currentUser.userType == 'End User' || (ticketId > 0 && !hasApprovedPermission)"
										clearable />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.customerEmail') }}</div>
								</template>
								<el-form-item prop="ticket_Customer_Email">
									<el-input v-model="formData.ticket_Customer_Email"
										:disabled="currentUser.userType == 'End User' || (ticketId > 0 && !hasApprovedPermission)"
										clearable />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<span>{{ $t('message.ticketFields.telephone') }}</span>
								</template>
								<el-form-item prop="ticket_Customer_Phone">
									<el-input v-model="formData.ticket_Customer_Phone"
										:disabled="currentUser.userType == 'End User' || (ticketId > 0 && !hasApprovedPermission)"
										clearable />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item :span="2" v-if="userGrantShow">
								<template #label>
									<el-row>
										<el-col :span="6">
											{{ $t('message.ticketFields.privateTicket') }}
										</el-col>
										<el-col :span="8">
											<el-form-item prop="privateTicket">
												<el-switch v-model="formData.userGrantFlag" @change="onoff"
													:disabled="!currentUser.privateTicket" />
											</el-form-item>
										</el-col>
									</el-row>
								</template>

								<el-form-item prop="grantIds">
									<div style="display: flex; width: 100%">
										<div style="width: 25%">
											{{ $t('message.ticketFields.grantAccess') }}
										</div>
										<div
											style="width: 75%; display: flex; flex-direction: column; justify-content: center; align-items: center">
											<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')"
												:disabled="currentUser.userType == 'End User' || formData.ticket_Status === 'Solved' || !isChooseGrant || !currentUser.privateTicket
													" ref="localTreeGrant" v-model="grantIds" :data="usergrantOpts" :render-after-expand="false"
												node-key="value" show-checkbox check-strictly check-on-click-node
												style="width: 100%" multiple :auto-expand-parent="true"
												@check-change="checkChange" :default-expanded-keys="grantIds"
												:props="{ value: 'value', label: 'label' }" filterable clearable />
										</div>
									</div>
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.assignee') }}</div>
								</template>
								<el-form-item prop="nodeId">
									<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')"
										:disabled="currentUser.userType == 'End User' || formData.ticket_Status === 'Solved' || (ticketId > 0 && !hasApprovedPermission)"
										ref="localTree" v-model="formData.nodeId" :data="assigneeOpts"
										:render-after-expand="false" node-key="value" show-checkbox check-strictly
										check-on-click-node style="width: 100%" filterable clearable />
								</el-form-item>
							</el-descriptions-item>
							<el-descriptions-item>
								<template #label>
									<div>{{ $t('message.userFields.selectApprover') }}</div>
								</template>
								<el-form-item>
									{{ formData.delegateName }}
								</el-form-item>
							</el-descriptions-item>
							<template v-if="
								currentUser.roles.indexOf('Employee') > -1 ||
								currentUser.roles.indexOf('Super Admin') > -1 ||
								currentUser.roles.indexOf('Admin') > -1
							">
								<el-descriptions-item :span="2">
									<template #label>
										<div class="descriptions-item">
											{{ $t('message.ticketFields.ticketType') }}
											<p style="color: red">{{ ticketTypeChangeTip }}</p>
										</div>
									</template>
									<el-form-item prop="ticketContentType">
										<el-select v-model="formData.ticketContentType"
											:placeholder="$t('message.page.selectKeyPlaceholder')" filterable clearable
											style="width: 100%" :disabled="isTicketTypeDisabled()">
											<el-option label="Public Ticket" value="Public Ticket"></el-option>
											<el-option label="Internal Ticket" value="Internal Ticket"></el-option>
										</el-select>
									</el-form-item>
								</el-descriptions-item>
							</template>


							<el-descriptions-item :label="$t('message.ticketFields.openDate')" v-if="showOpenDate">
								<MyDate v-model:input="formData.ticket_Open_Date" disabled="disabled" />
							</el-descriptions-item>

							<el-descriptions-item :label="$t('message.ticketFields.closeDate')" v-if="showCloseDate">
								<MyDate v-model:input="formData.ticket_Close_Date" disabled="disabled" />
							</el-descriptions-item>
						</el-descriptions>
					</template>

					<template v-else>
						<el-descriptions :column="2" border direction="vertical">
							<el-descriptions-item :span="2" v-if="ticketId > 0">
								<template #label>
									<div>{{ $t('message.ticketFields.caseNumber') }}</div>
								</template>
								<el-form-item>
									<el-input v-model="formData.ticket_Number" disabled />
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.category') }}</div>
								</template>
								<el-form-item prop="ticket_Category">
									<el-select v-model="formData.ticket_Category"
										:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
										@change="loadAssigneesByCustomer">
										<el-option v-for="item in categoryOpts"
											:label="item['category_Code'] + ' - ' + item['category_Description']"
											:value="item['category_Code']" :key="item['id']" />
									</el-select>
								</el-form-item>
							</el-descriptions-item>

							<el-descriptions-item>
								<template #label>
									<div class="descriptions-item">{{ $t('message.ticketFields.priority') }}</div>
								</template>
								<el-form-item prop="ticket_Priority">
									<el-select v-model="formData.ticket_Priority"
										:placeholder="$t('message.page.selectKeyPlaceholder')" filterable clearable>
										<el-option v-for="item in priorityOpts" :label="item['itemName']"
											:value="item['itemValue']" :key="item['itemId']"></el-option>
									</el-select>
								</el-form-item>
							</el-descriptions-item>
						</el-descriptions>
					</template>
				</el-form>
			</el-card>
		</div>

		<div class="ticket-info-right">
			<el-collapse @change="commentBoxCollapse" v-model="activeNames" :class="{ full: ticketId === 0 }">
				<el-collapse-item :name="commentCollapseItemName">
					<template #title>
						<span class="comment-footer" v-if="ticketId > 0">
							<el-button type="primary" size="small">Show / Hide</el-button>
						</span>
					</template>

					<el-form ref="ruleFormRef2" :model="formData" :rules="rules" label-width="100px"
						@dragenter="handleDragEnter" @dragleave="handleDragLeave" @drop="handleDrop" @dragover.prevent>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
								<el-form-item :label="$t('message.ticketFields.subject')" prop="ticket_Subject">
									<el-input v-model="formData.ticket_Subject" :disabled="ticketId > 0" maxlength="50"
										show-word-limit clearable />
								</el-form-item>
							</el-col>

							<el-col :xs="2" :sm="2" :md="2" :lg="2" :xl="2" class="mb10" v-if="ticketId > 0">
								<el-form-item label="Edit Description">
									<el-switch v-model="isEditNote" @change="onoffNote" inline-prompt active-text="On"
										inactive-text="OFF"
										:disabled="readonly_formData == 'Cancelled' || readonly_formData == 'Solved'" />
								</el-form-item>
							</el-col>
							<el-col :xs="22" :sm="10" :md="10" :lg="10" :xl="10" class="mb10" v-if="ticketId == 0">
								<el-form-item :label="$t('message.ticketFields.subjectType')" prop="ticket_Content_Type"
									v-if="currentUser.roles.indexOf('End User') === -1 && currentUser.roles.indexOf('Master User') === -1">
									<el-select v-model="formData.ticket_Content_Type"
										:placeholder="$t('message.page.selectKeyPlaceholder')"
										:disabled="firstTicketContentType === 'Internal Ticket' || formData.ticketContentType === 'Internal Ticket'">
										<el-option v-for="item in contentTypeOpts" :label="item['itemName']"
											:value="item['itemValue']" :key="item['ItemId']"></el-option>
									</el-select>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22" class="mb10" v-if="ticketId == 0">
								<el-form-item label="" prop="assignToUserId">
									<el-upload v-model:file-list="fileList" class="upload-demo" drag :action="actionStr"
										accept="Image/jpg" :before-upload="beforeImageUpload"
										:on-success="onUploadSuccess" :limit="10" multiple @change="handleUploadChange"
										:http-request="customUpload">
										<el-row style="display: flex; justify-content: center; align-items: center">
											<el-icon class="uploader-icon">
												<ele-Paperclip />
											</el-icon>
											{{ isDropFileHere ? $t('message.ticketButtons.dropFilesHere') :
												$t('message.ticketButtons.dragUploadFilesLessThan') }}
										</el-row>
									</el-upload>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="24" :md="22" :lg="22" :xl="22" class="mb10">
								<el-form-item label="Description">
									<EditorNote v-model:modelValue="editorNote.htmlVal" v-if="ticketId > 0"
										@getisFocused="getEditorblur" v-model:getText="editorNote.textVal"
										:disable="editorNote.disable" :height="editorNote.height" :style="editorStyle"
										:mode="editorNote.mode" />

									<EditorNote v-model:modelValue="editorNoteCreate.htmlVal" v-if="ticketId == 0"
										v-model:getText="editorNoteCreate.textVal" :disable="editorNoteCreate.disable"
										:height="editorNoteCreate.height" :mode="editorNoteCreate.mode" />
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>

					<div class="button-group">
						<el-button v-if="ticketId > 0 || hasApprovedPermission || userGrantShowSave" size="small"
							type="primary" class="ml10" @click="onAddComment">
							{{ $t('message.ticketButtons.addComment') }}
						</el-button>

						<template
							v-if="formData.ticket_Parent_Id == 0 && editStatus != 'Solved' && editStatus != 'Cancelled' && isShowSub">
							<el-button v-auth="'CreateSubTicket.Visit'" size="small" type="primary"
								@click="onCreateSubTicket">
								{{ $t('message.ticketLabels.createSubTicket') }}
							</el-button>
						</template>

						<el-button v-if="hasApprovedPermission || userGrantShowSave" :loading="submitTicketLoading"
							size="small" type="primary" class="ml10" @click="onSubmitTicket(0)">
							{{ $t('message.ticketButtons.submitTicket') }}
						</el-button>

						<el-button v-if="ticketId <= 0" :loading="submitTicketLoading" size="small" type="primary"
							class="ml10" @click="onSubmitAsNew">
							{{ $t('message.ticketButtons.submitTicket') }}
						</el-button>

						<el-select v-if="hasApprovedPermission" v-model="formData.ticket_Status" @change="onStatus"
							:placeholder="$t('message.page.selectKeyPlaceholder')" class="ml10" size="small"
							style="width: 100px">
							<el-option v-for="item in actionOpts" :label="item.name" :value="item.name"
								:key="item.index"></el-option>
						</el-select>

						<template v-if="
							currentUser.roles.indexOf('Employee') > -1 || currentUser.roles.indexOf('Super Admin') > -1 || currentUser.roles.indexOf('Admin') > -1
						">
							<el-form-item v-if="showScheduleDate" class="ml10" size="small">
								<div class="descriptions-item"></div>
							</el-form-item>
						</template>

						<el-form ref="ruleFormRef3" :model="formData" :rules="rules">
							<template v-if="
								currentUser.roles.indexOf('Employee') > -1 ||
								currentUser.roles.indexOf('Super Admin') > -1 ||
								currentUser.roles.indexOf('Admin') > -1
							">
								<el-form-item prop="scheduledDate" v-if="showScheduleDate" style="width: 120px">
									<MyDate v-model:input="formData.scheduledDate" size="small" :disabled
										:disabled-date="disabledDateFn" />
								</el-form-item>
							</template>
						</el-form>

						<el-dropdown v-if="ticketId > 0" @command="onMoreDropdown" class="ml10">
							<el-button type="primary" size="small">
								{{ $t('message.ticketButtons.more') }} <el-icon class="el-icon--right">
									<ele-ArrowDown /></el-icon>
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item command="workflowRecords">{{
										$t('message.ticketButtons.workflowRecords') }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</div>
				</el-collapse-item>
			</el-collapse>

			<el-card shadow="always" class="list-card mt5" v-if="ticketId > 0">
				<el-timeline>
					<el-timeline-item :timestamp="item.createdAt" placement="top"
						v-for="item in formData.ticketContents" :key="item.ticketContentId">
						<div class="tl-item">
							<div class="timeline-avatar">
								<el-avatar :size="50"
									src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
									alt="noView" />
							</div>
							<div class="timeline-content">
								<div class="timeline-conte_name">
									<h5>Last Updated By: {{ item.createdByName }}</h5>
									<el-tag v-if="item.ticketContentType === 'Internal'" class="timeline-conte_type"
										size="small" type="warning">{{
											$t('message.ticketFields.internal')
										}}</el-tag>
									<el-tag v-if="item.ticketContentType === 'Comment'" class="timeline-conte_type"
										size="small">{{
											$t('message.ticketFields.comment')
										}}</el-tag>
									<el-tag v-if="item.ticketContentType === 'Public'" class="timeline-conte_type"
										size="small" type="success">{{
											$t('message.ticketFields.public')
										}}</el-tag>
									<el-tag v-if="item.ticketContentType === 'Emails'" class="timeline-conte_type"
										size="small" type="success">{{
											$t('message.ticketFields.Emails')
										}}</el-tag>

									<!-- <span class="timeline-conte_type" style="vertical-align:bottom;cursor: pointer;">
									 <el-icon  v-if="currentUser.userId === 1" size="large"  >
											<ele-Delete  @click="onDelComent(item.ticketContentId)"  />
										</el-icon>
									</span> -->
								</div>
								<!-- <div v-if="isNotEmptyOrNull(item.assginToName)">
										<label class="timeline-content_to">To</label><span>:</span>{{ item.assginToName
										}}
									</div> -->
								<div v-if="item.isAttachment === 'Y'" style="position: relative">
									<el-icon class="ele-icon">
										<ele-Link />
									</el-icon>
									<span>:</span>
									<el-popover :visible="item.popoverShow"
										@update:visible="(visible: boolean) => UpdateVisible(item, visible)"
										placement="right" :width="400" trigger="click">
										<!-- <el-popover placement="right" 
											:width="400" trigger="click"> -->
										<template #reference>
											<el-link type="primary">{{ $t('message.ticketButtons.attachments') }} ({{
												item.ticketAttachments.length }})</el-link>
										</template>
										<el-table :data="item.ticketAttachments">
											<el-table-column property="attachment_Original_File_Name" label="Name">
												<template #default="{ row }">
													<el-icon class="ele-icon"><ele-Download /></el-icon>
													<el-link class="link_text" @click="downloadFile(row)">
														{{ row.attachment_Original_File_Name }}
													</el-link>
													<el-tag v-if="previewFile(row.attachment_File_Ext)"
														class="ml-2 ml10" type="warning" style="cursor: pointer"
														@click="handleFilePreview(item, row)">{{
															$t('message.ticketLabels.preview') }}</el-tag>
												</template>
											</el-table-column>
										</el-table>
										<el-image-viewer v-if="previewVisibleRef" :url-list="previewSrcList"
											hide-on-click-modal :initial-index="previewIndexRef"
											@close="imagePreviewVisible" />
									</el-popover>
								</div>
								<el-image-viewer v-if="showPreview" :url-list="srcList" hide-on-click-modal
									:initial-index="0" @close="showPreview = false" />

								<div v-html="item.ticketContent" @click="handleImageClick"
									:class="{ 'internal-content': item.ticketContentType === 'Internal' }">
								</div>
								<el-divider />
							</div>

							<div v-if="false" class="timeline-time-description" style="width: 150px">
								<div class="time">
									<el-icon>
										<ele-Clock />
									</el-icon>
									{{ formatPast('2023-08-23 21:15:49') }}
								</div>
							</div>
						</div>
					</el-timeline-item>
				</el-timeline>
			</el-card>
		</div>

		<el-drawer v-model="workflowRecordsDrawer" :size="800" destroy-on-close direction="rtl">
			<div style="height: 100%; padding: 50px; width: 750px">
				<Operations :workflowInstanceId="elsaInstancesId" />
			</div>
		</el-drawer>

		<el-drawer v-model="ticketCommentDrawer" class="comment-dr" destroy-on-close direction="rtl">
			<div style="padding: 10px 0">
				<el-button size="small" type="primary" class="ml10" @click="onSubmitComment"> Save Comment </el-button>
				<el-button v-if="hasApprovedPermission || userGrantShowSave" size="small" type="primary" class="ml10"
					:loading="submitTicketLoading" @click="onSubmitTicket(1)">
					{{ $t('message.ticketButtons.submitTicket') }}
				</el-button>
				<div style="display: inline-flex">
					<template v-if="
						currentUser.roles.indexOf('Employee') > -1 || currentUser.roles.indexOf('Super Admin') > -1 || currentUser.roles.indexOf('Admin') > -1
					">
						<el-form-item v-if="showScheduleDate" class="ml10" size="small">
							<div class="descriptions-item"></div>
						</el-form-item>
					</template>

					<el-form ref="ruleFormRef3" :model="formData" :rules="rules">
						<template v-if="
							currentUser.roles.indexOf('Employee') > -1 || currentUser.roles.indexOf('Super Admin') > -1 || currentUser.roles.indexOf('Admin') > -1
						">
							<el-form-item prop="scheduledDate" v-if="showScheduleDate" style="width: 110px">
								<MyDate v-model:input="formData.scheduledDate" size="small"
									:disabled-date="disabledDateFn" />
							</el-form-item>
						</template>
					</el-form>
				</div>
				<el-select v-if="hasApprovedPermission" v-model="formData.ticket_Status" @change="onStatus"
					:placeholder="$t('message.page.selectKeyPlaceholder')" class="ml10" size="small"
					style="width: 100px">
					<el-option v-for="item in actionOpts" :label="item.name" :value="item.name"
						:key="item.index"></el-option>
				</el-select>

				<el-button size="small" type="info" class="ml10" @click="onCloseComment"> Cancel </el-button>
			</div>
			<div style="height: 100%; padding: 0 10px; width: 100%">
				<el-card shadow="never" class="card-footer">
					<el-form ref="ruleFormRefComment" :model="formData" :rules="rules" label-width="100px"
						@dragenter="handleDragEnter" @dragleave="handleDragLeave" @drop="handleDrop" @dragover.prevent>
						<el-row :gutter="35">
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
								<el-form-item :label="$t('message.ticketFields.subject')" prop="ticket_Subject">
									<el-input v-model="formData.ticket_Subject" :disabled="ticketId > 0" maxlength="50"
										show-word-limit />
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
								<el-form-item :label="$t('message.ticketFields.subjectType')" prop="ticket_Content_Type"
									v-if="currentUser.roles.indexOf('End User') === -1 && currentUser.roles.indexOf('Master User') === -1">
									<el-select v-model="formData.ticket_Content_Type"
										:placeholder="$t('message.page.selectKeyPlaceholder')"
										:disabled="firstTicketContentType === 'Internal Ticket' || formData.ticketContentType === 'Internal Ticket'">
										<el-option v-for="item in contentTypeOpts" :label="item['itemName']"
											:value="item['itemValue']" :key="item['ItemId']"></el-option>
									</el-select>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
								<el-form-item label="" prop="assignToUserId">
									<el-upload v-model:file-list="fileList" class="upload-demo" drag :action="actionStr"
										accept="Image/jpg" :before-upload="beforeImageUpload"
										:on-success="onUploadSuccess" :limit="10" multiple @change="handleUploadChange"
										:http-request="customUpload">
										<el-row style="display: flex; justify-content: center; align-items: center">
											<el-icon class="uploader-icon">
												<ele-Paperclip />
											</el-icon>
											{{ isDropFileHere ? $t('message.ticketButtons.dropFilesHere') :
												$t('message.ticketButtons.dragUploadFilesLessThan') }}
										</el-row>
									</el-upload>
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
								<el-form-item label="Comment">
									<EditorNote v-model:modelValue="editorComment.htmlVal"
										v-model:getText="editorComment.textVal" :disable="editorComment.disable"
										:height="editorComment.height" :mode="editorComment.mode" />
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-card>
			</div>
		</el-drawer>

		<TicketDialogList ref="ticketListDialogRef"></TicketDialogList>
		<FilePreview ref="ticketFileDialogRef"></FilePreview>
	</div>
</template>

<script lang="ts">
import {
	defineComponent,
	getCurrentInstance,
	onMounted,
	reactive,
	ref,
	toRefs,
	defineAsyncComponent,
	computed,
	watch,
	nextTick,
	readonly,
	watchEffect,
	provide,
} from 'vue';
import { formatPast, formatStrDate, formatDay } from '/@/utils/formatTime';
import { ElMessage, ElMessageBox } from 'element-plus';
import { CmTicketsAddOrUpdateDto } from '/@/types/ticketDtos';
import { onBeforeRouteLeave, useRoute } from 'vue-router';

import cmTicketsApi from '/@/api/cmTickets/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import dictItemApi from '/@/api/dictItem/index';
import organizationApi from '/@/api/organization/index';
import userInfoApi from '/@/api/user/index';
import workflowCrmApi from '/@/api/workflowCrm/index';
import cmTicketsContentApi from '/@/api/cmTicketContents/index';

import Operations from '/@/views/bpts/workFlow/components/operations.vue';
import { Local, Session } from '/@/utils/storage';
import { WorkflowActions } from '/@/types/workflow';

import { Download } from '@element-plus/icons-vue';
import router from '/@/router';

import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useI18n } from 'vue-i18n';

import MyDate from '/@/components/ticket/ticketDate.vue';

import TicketDialogList from '/@/views/bpts/ticket/dialogList.vue';
import ticket from '/@/api/ticket';
import mittBus from '/@/utils/mitt';
import request from '/@/utils/request';
import { encodeTicketNumber, decodeTicketNumber } from '/@/utils';
import FilePreview from './components/filePreview.vue';
import userDelegateApi from '/@/api/userDelegate';


//const EditorNote = defineAsyncComponent(() => import('/@/components/editor/index.vue'));
const EditorNote = defineAsyncComponent(() => import('/@/components/editor/noteV5.vue'));

export default defineComponent({
	name: 'TicketEdit',
	components: { Operations, MyDate, TicketDialogList, EditorNote, FilePreview },
	props: {
		itemParams: {
			type: Object,
		},
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;

		const showPreview = ref(false)

		let srcList = ref([]) as any;

		const { t } = useI18n();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const route = useRoute();
		const localTree = ref();
		const localTreeGrant = ref();

		const ticketListDialogRef = ref();

		const localCompanyTree = ref();
		const localProjectTree = ref();

		const acceptUploadType = ref('');
		const firstTicketContentType = ref('');
		const parentTicketConentType = ref(''); //根据主订单锁定子订单的Ticket Type

		const readonly_formData = ref('');
		let footerClass = ref(['']);
		const commentCollapseItemName = 'commentBox';
		let activeNames = ref([commentCollapseItemName]);

		let isChooseGrant = ref(false);

		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});

		const isUploading = ref(false);

		const previewVisibleRef = ref(false);
		const previewIndexRef = ref(0);
		const previewSrcList = ref();
		const ticketFileDialogRef = ref();
		const fileData = ref({});
		const isDropFileHere = ref(false);
		const dragCounter = ref(0);

		const state = reactive({
			editorNoteCreate: {
				placeholder: '',
				htmlVal: '',
				textVal: '',
				disable: false,
				height: '180px',
				mode: 'default', // 'default' or 'simple'
			},
			editorNote: {
				placeholder: '',
				htmlVal: '',
				textVal: '',
				disable: true,
				height: '180px',
				mode: 'default', // 'default' or 'simple'
			},

			editorComment: {
				placeholder: '',
				htmlVal: '',
				textVal: '',
				disable: false,
				height: '350px',
				mode: 'default', // 'default' or 'simple'
			},
			ticketId: 0 as any,
			parentId: 0 as any,
			formData: {
				ticket_Content_Type: 'Public',
				userDelegateData: {},
			} as any,
			createSubTicketLoading: false,
			submitTicketLoading: false,
			submitasNewLoading: false,
			rules: {
				ticket_Category: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Subject: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Priority: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_First_Name: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Last_Name: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Customer: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Customer_Email: [
					{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
					{ pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: 'Invalid email', trigger: ['blur', 'change'] },
				],
				ticket_Customer_Phone: [
					{ required: false, message: t('message.ticketValidates.required'), trigger: 'blur' },
					{
						validator: (rule: any, value: any, callback: any) => {
							if (/^(?:[0-9]\d*)$/.test(value) == false && isNotEmptyOrNull(value)) {
								callback(new Error('Phone must be number'));
							} else {
								callback();
							}
						},
						trigger: 'blur',
					},
				],
				nodeId: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticket_Content: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' }],
				ticket_Note: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				ticketContentType: [{ required: true, message: t('message.ticketValidates.required'), trigger: 'change' }],
				scheduledDate: [
					{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
					{
						validator: (rule: any, value: any, callback: any) => {
							const nowDay = new Date(new Date().toLocaleDateString());
							const scheduleDay = new Date(value);

							if (scheduleDay < nowDay) {
								state.formData.scheduledDate = '';
							} else {
								callback();
							}
						},
						trigger: 'change',
					},
				],
			},

			categoryOpts: [] as any,
			priorityOpts: [] as any,
			customerOpts: [] as any,
			businessUnitData: [] as any,
			projectOpts: [] as any,
			assigneeOpts: [] as any,
			usergrantOpts: [] as any,
			grantIds: [] as any,
			userGrantShow: false,
			userGrantShowSave: false,
			contentTypeOpts: [] as any,

			selectContentType: 'Pubilc reply',
			contentHmlt: '',
			//当前流程节点的相关信息
			currentNode: {
				permissions: [], //流程的操作权限
			} as any,
			actionOpts: [] as any, //流程操作选项，这里是操作状态
			elsaInstancesId: '', //流程的实例id
			workflowRecordsDrawer: false,
			hasApprovedPermission: false,
			ticketCommentDrawer: false,
			fileList: [],
			// 设置上传的请求头部
			headers: {
				Authorization: `Bearer ${Local.get('token')}`,
			},
			actionStr: `${BASE_URL.value}Api/Files/UpLoadV2`,
			contentPlaceholder:
				'Please enter any additional information, relevant details or updates, or share your thoughts and feedback about the ticket in the note field.',
			editStatus: '',
			eidtshow: true,
			isShowSub: true,
			oldNodeId: '',
			disabledCustomer: false,
			disabledProject: false,
			footerClass: footerClass,
			commentCollapseItemName: commentCollapseItemName,
			activeNames: activeNames,
			oldUserGrantFlag: false,
			isEditNote: false,
			showTicketProject: false,
			tableData: {
				params: {
					groupId: '' as any,
					ticket_Parent_Id: 0,
					ticket_Status: '' as any,
					ticket_Number: '',
					ticket_Subject: '',
					ticket_Priority: '',
					ticket_Status_List: ["New", "Open", "Replied", "Hold ", "Solved", "Cancelled", "Scheduled"],
					ticket_Priority_List: [],
					ticketContentType_List: [],
					ticket_Category_List: [],
					ticket_Customer_List: [],
					BusinessUnitList: [],
					ticket_Customer_ListV2: [],
					assignToSearchList: [],
					userDelegateName: '',
					ticket_From: '',
					pageIndex: 1,
					pageSize: 20,
					searchKey: 's',
					startTime: '',
					endTime: '',
					startTimeModified: '',
					endTimeModified: '',
					assigToId: '' as any,
					order: 'Ticket_Number',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
					onHold: false,
					startScheduled: '',
					endScheduled: '',
					assignedToMe: false,
					assignedToMeOld: false,
					autoSearch: "autoSearch"
				} as any,
				topParams: {
					groupId: '' as any,
					ticket_Status: '' as any,
					assigToId: '' as any,
					ticket_From: '',
				},
			},
			ticketTypeData: [],
		});

		state.formData.ticketContentType = 'Public Ticket';

		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});

		const editorStyle = computed(() => ({
			backgroundColor: '#f0f0f0',
		}));

		watch(
			() => state.formData.ticketContentType,
			(newVal, oldVal) => {
				if (newVal === 'Internal Ticket') {
					state.formData.ticket_Content_Type = 'Internal';
				} else {
					state.formData.ticket_Content_Type = 'Public';

					// 把internal ticket改成public ticket后，会默认是Public Subject，但无法编辑ticket subject type
					// 	- 必须保存后重新打开才能编辑ticket subject type
					firstTicketContentType.value = 'Public Ticket';
				}
			}
		);

		const loadAssigneesByCustomer = async (changeType: any) => {
			//state.formData.nodeId = '';//这个引起新建时，Assigned To马上提示红框

			var projectId = parseInt(state.formData.ticket_Project) > 0 ? state.formData.ticket_Project : state.formData.ticket_Customer;

			if (isNotEmptyOrNull(state.formData.ticket_Category) && isNotEmptyOrNull(projectId)) {
				await userInfoApi.GetAllGroupUserList({ Customer: projectId }).then(async (rs) => {
					state.assigneeOpts = rs.data.groupList;
					state.usergrantOpts = rs.data.groupGrantList;

					if (state.ticketId <= 0 || changeType == 'FromWatch') {
						const categoryId = state.categoryOpts.find((item: any) => {
							return item.category_Code === state.formData.ticket_Category;
						})?.id;

						if (isNotEmptyOrNull(categoryId) && isNotEmptyOrNull(projectId)) {
							await cmCategoryApi.GetDetailByIdAndCompanyId(categoryId, projectId).then((rs) => {
								if (rs.data) {
									state.formData.nodeId = rs.data.selectedAssigneeList;
								}
							});
						}
					}
				});
			} else if (isNotEmptyOrNull(projectId)) {
				await userInfoApi.GetAllGroupUserList({ Customer: projectId }).then((rs) => {
					state.assigneeOpts = rs.data.groupList;
					state.usergrantOpts = rs.data.groupGrantList;
				});
			}
		};

		let isInitialized_ticket_Category = false;
		let isInitialized_ticket_Customer = false;

		// 用watch监听值编号，以此解决第一次选择customer的时候没有调用事件的问题
		watch(
			() => state.formData.ticket_Category,
			() => {
				if (isInitialized_ticket_Category) {
					loadAssigneesByCustomer('FromWatch');
				} else {
					isInitialized_ticket_Category = true;
				}
			},
			{ immediate: false }
		);

		// watch(
		// 	() => state.formData.ticket_Customer,
		// 	() => {
		// 		GetCategoryOptsByCustomer();

		// 		if (isInitialized_ticket_Customer) {
		// 			state.formData.ticket_Category = '';

		// 			loadAssigneesByCustomer('FromWatch');
		// 		} else {
		// 			isInitialized_ticket_Customer = true;
		// 		}
		// 	},
		// 	{ immediate: false }
		// );

		const SetCategory = (value: any) => {
			isInitialized_ticket_Category = true;
			isInitialized_ticket_Customer = true;
		};

		const onRelateTicket = (value: number) => {
			const encodedTicketNumber = encodeTicketNumber(value);
			router.push('/dashboard/editTicket/' + encodedTicketNumber);
		};

		//加载详细的工单信息
		const loadDetail = async () => {
			if (state.ticketId <= 0) return;

			await cmTicketsApi.Detail(state.ticketId).then(async (rs) => {
				const obj = rs.data;

				state.oldNodeId = obj.nodeId;
				state.oldUserGrantFlag = obj.userGrantFlag;

				state.formData = obj;

				updateProjectOptions((parseInt(state.formData.ticket_Project) > 0 ? state.formData.ticket_Project : state.formData.ticket_Customer) + '');

				var projectId = parseInt(state.formData.ticket_Project);

				if (currentUser.value.projects.indexOf(projectId) > -1) {
					if (state.formData.onHold) {
						if (state.hasApprovedPermission) {
							state.isShowSub = true;
						} else {
							state.isShowSub = false;
						}
					} else {
						if (state.hasApprovedPermission) {
							state.isShowSub = false;
						} else {
							state.isShowSub = true;
						}
					}
				} else {
					state.isShowSub = false;
				}

				state.userGrantShowSave = obj.userGrantIsSave;
				if (obj.userGrantIsSave) {
					state.isShowSub = false;
				} else {
					state.isShowSub = true;
				}

				state.userGrantShow = obj.ticket_Parent_Id == 0 && currentUser.value.privateTicket;

				state.formData.ticket_Content_Type = 'Public'; //设置一下类型

				if (obj.ticketContentType) {
					firstTicketContentType.value = obj.ticketContentType;
				}
				state.editorNote.textVal = obj.ticket_Note;
				state.editorNote.htmlVal = obj.ticket_Note;

				if (currentUser.value.roles.indexOf('Employee') > -1) {
					state.formData.ticket_Content_Type = obj.ticketContentType.replace(' Ticket', '');
				}

				state.formData.ticket_Open_Date = formatDay(state.formData.ticket_Open_Date);
				state.formData.ticket_Close_Date = formatDay(state.formData.ticket_Close_Date);
				state.formData.scheduledDate = formatDay(state.formData.scheduledDate);

				//单独把状态拿出来
				state.editStatus = rs.data.ticket_Status;
				readonly_formData.value = readonly(rs.data.ticket_Status);

				//on hold 状态的主工单的子工单不能编辑
				if (state.formData.ticket_Parent_Id > 0) {
					const info = { Id: state.formData.id, Ticket_Parent_Id: state.formData.ticket_Parent_Id };
					var onHoldInfo = {} as any;
					await cmTicketsApi.DetailOnHold(info).then((rs) => {
						if (onHoldInfo != null) {
							onHoldInfo = rs.data;
							if (onHoldInfo.onHold) {
								state.eidtshow = false;
								state.hasApprovedPermission = false;
							}
						}
					});
				}

				//Grant Access to 赋默认
				const usergrantAccess = [] as any;

				rs.data.userGrant.map(function (value: any, index: any, array: any) {
					if (value.userType == 'UserNode') {
						if (value.groupNode == '') {
							usergrantAccess.push(value.userNode + '');
						} else {
							usergrantAccess.push(value.userNode + '');
						}
					} else if (value.userType == 'GroupNode') {
						usergrantAccess.push(value.userNode + '');
					}
				});

				isChooseGrant.value = rs.data.userGrantFlag;
				state.grantIds = usergrantAccess;

				//工作流相关
				state.elsaInstancesId = obj.elsaInstancesId;
				GetCurrentApproval();

				//加载组的可选项
				await loadAssigneesByCustomer('FromLoadDetail');
				state.formData.nodeId = state.oldNodeId;

				//判断默认选项在不在下拉框加载的数据里面，如果没有的话，要清空下拉框
				let haveData = state.assigneeOpts.some((item: any) => {
					if (item.value == state.oldNodeId) {
						return true;
					}

					if (item.children) {
						return item.children.some((child: any) => child.value == state.oldNodeId);
					}
				});

				if (!haveData) {
					state.formData.nodeId = '';
				}

				if (state.formData.onHold) {
					state.formData.ticket_Status = 'Scheduled';
					state.userGrantShow = false;
				}
			});

			GetCategoryOpts();
		};

		//获取流程的节点信息，并检查是否有 审批（提交、修改）权限
		const GetCurrentApproval = () => {
			if (!state.elsaInstancesId) {
				return;
			}

			var params = {
				workflowInstanceId: state.elsaInstancesId,
				userTrusteeId: state.formData.userTrusteeId,
			};

			workflowCrmApi.GetCurrentApproval(params).then((rs) => {
				state.currentNode = rs.data;

				if (state.currentNode.forkType == 'Action' && state.currentNode.forks) {
					const statusName = [] as any;

					state.currentNode.forks.map((a: { name: any }) => {
						if (a.name == 'New') {
							statusName.push({ name: a.name, index: 1 });
						}
						if (a.name == 'Open') {
							statusName.push({ name: a.name, index: 2 });
						}
						if (a.name == 'Replied') {
							statusName.push({ name: a.name, index: 3 });
						}
						if (a.name == 'Hold') {
							statusName.push({ name: a.name, index: 4 });
						}
						if (a.name == 'Scheduled') {
							statusName.push({ name: a.name, index: 5 });
						}
						if (a.name == 'Solved') {
							statusName.push({ name: a.name, index: 6 });
						}
						if (a.name == 'Cancelled') {
							statusName.push({ name: a.name, index: 7 });
						}
					});

					statusName.sort(function (a: any, b: any) {
						return a.index - b.index;
					});

					state.actionOpts = statusName;
				}

				//是否可以审批
				const hasApprovalAndReject = state.currentNode.permissions?.filter((item: { permissionType: string }) => {
					return item.permissionType === WorkflowActions.APPROVE;
				});
				//console.log("hasApprovalAndReject:",hasApprovalAndReject);
				state.hasApprovedPermission = hasApprovalAndReject?.length;
				if (!state.hasApprovedPermission) {
					state.rules.nodeId[0].required = false;
				}
			});
		};

		//More 按钮下拉选择事件
		const onMoreDropdown = (command: string | number | object) => {
			if (command === 'workflowRecords') {
				state.workflowRecordsDrawer = true;
			}
		};

		//获取表单信息
		const getFormData = (submitType: number) => {
			// 使用展开运算符 ... 创建新对象
			let req: any = { ...state.formData };

			var selectAssginTree = localTree.value.getCheckedNodes();
			const selectSubmitAssignToIdTree = [] as any[];
			const selectSubmitAssignTree = [] as any[];
			selectAssginTree.map(function (value: any, index: any, array: any) {
				selectSubmitAssignToIdTree.push({ nodeId: value.nodeId, nodeType: value.nodeType, nodeName: value.label, nodeIdGuid: value.value });
				if (value.nodeType == 'UserNode') {
					selectSubmitAssignTree.push(value.nodeId.indexOf('@@@') > -1 ? value.nodeId.split('@@@')[1] : value.nodeId);
				} else if (value.nodeType == 'GroupNode') {
					if (value.children) {
						for (let child of value.children) {
							selectSubmitAssignTree.push(child.nodeId.indexOf('@@@') > -1 ? child.nodeId.split('@@@')[1] : child.nodeId);
						}
					}
				}
			});

			req.assignTo = selectSubmitAssignTree;
			req.assigToId = selectSubmitAssignToIdTree;
			req.ticket_From = 'From System';

			if (localTreeGrant.value != undefined) {
				var selectAssginTreeGrant = localTreeGrant.value.getCheckedNodes();
				const selectSubmitAssignToIdTreeGrant = [] as any[];
				selectAssginTreeGrant.map(function (value: any, index: any, array: any) {
					selectSubmitAssignToIdTreeGrant.push({ nodeId: value.nodeId, nodeType: value.nodeType, nodeName: value.label, nodeIdGuid: value.value });
				});
				req.userGrants = selectSubmitAssignToIdTreeGrant;
			}

			//这里需要修改
			if (firstTicketContentType.value != '') {
				//注释这里，目前Ticket Type有放开部分人可以修改
				// req.ticketContentType = firstTicketContentType.value;
			} else {
				req.ticketContentType = state.formData.ticketContentType;
			}

			if (state.ticketId == 0) {
				req.ticket_Note = state.editorNoteCreate.htmlVal;
				req.ticketContentsInfo = {
					ticket_Content: state.editorNoteCreate.htmlVal,
					ticket_Content_Type: state.formData.ticket_Content_Type,
				};
			} else {
				req.ticket_Note = state.editorNote.htmlVal;
				if (submitType == 1) {
					req.ticketContentsInfo = {
						ticket_Content: state.editorNote.htmlVal,
						ticket_Content_Type: state.formData.ticket_Content_Type,
					};
				}
			}

			//附件
			req.ticketAttachments = state.fileList.map((item: any) => ({
				attachment_File_Id: item.id,
				attachment_File_Name: item.raw.response.data[0].fileUrl,
				attachment_Original_File_Name: item.name,
				attachment_File_Ext: item.name.substring(item.name.lastIndexOf('.') + 1),
			}));

			//删除多余的属性
			delete req.ticketContents;

			return req;
		};

		//提交一个新的工单
		const onSubmitAsNew = async () => {
			// 保存当前路由信息，确保关闭的是提交工单时的标签页
			const originalRoute = { ...route };

			try {
				await Promise.all([proxy.$refs.ruleFormRef1.validate(), proxy.$refs.ruleFormRef2.validate(), proxy.$refs.ruleFormRef3.validate()]).catch(
					(err) => {
						ElMessage.error("Please fill in the required fields marked with '*'.");
						openCommentBox();
						return new Promise(() => { });
					}
				);

				if (isUploading.value) {
					ElMessage.error('Please wait, file is uploading');
					return;
				}

				if (state.editorNoteCreate.textVal == '') {
					ElMessage.error('Please enter the description.');
					return;
				}

				// 如果两个表单都通过了验证，执行提交操作
				var reqObj = getFormData(0);

				if (reqObj.ticket_Project == '') {
					reqObj.ticket_Project = '0'
				}

				reqObj.openItemDetail = props.itemParams;
				state.submitTicketLoading = true;

				cmTicketsApi
					.TicketSave(reqObj)
					.then((rs) => {
						ElMessage({
							type: 'success',
							dangerouslyUseHTMLString: true,
							message: `Ticket Number ${rs.data.ticket_Number} has been submitted successfully.`,
						});

						state.fileList = [];
						proxy.$refs.ruleFormRef1.resetFields();
						proxy.$refs.ruleFormRef2.resetFields();
						proxy.$refs.ruleFormRef3.resetFields();
						if (!props.itemParams) {
							let closePath = '/dashboard/ticket/create';

							if (state.ticketId <= 0 && route.name == 'ScheduleTicketCreate') {
								closePath = '/dashboard/schedule/create';
							}
							if (isNotEmptyOrNull(route.params.parentId)) {
								closePath = '/dashboard/ticket/create/:parentId';
							}

							nextTick(() => {
								//刷新页面
								mittBus.emit<any>('RefreshTicketList', {});

								// 关闭原始提交工单的标签页
								mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...originalRoute }));
							});

						} else {
							context.emit('closeDialog');
							return;
						}
					})
					.catch((err) => {
						ElMessage.error(err.resultMsg);
					})
					.finally(() => {
						state.submitTicketLoading = false;
					});
			} catch (err: any) {
				// 如果有任何一个表单未通过验证，处理错误
				if (state.ticketId == 0 && currentUser.value.userType == 'End User') {
					if (!isNotEmptyOrNull(err.nodeId[0].fieldValue)) {
						ElMessage.error(t('message.ticketFields.submitTicketError'));
					} else {
						ElMessage.error(t('message.ticketFields.submitTicketOtherError'));
					}
				} else {
					if (err.message) {
						ElMessage.error(err.message);
					}
				}

				return false;
			}
		};

		//修改工单
		const onSubmitTicket = async (submitType: number) => {
			try {
				if (state.formData.ticket_Status == 'Cancelled') {
					ElMessageBox.confirm(`Irrevocable Action, are you sure you want to cancel this ticket?`, 'Tips', {
						confirmButtonText: 'Yes',
						cancelButtonText: 'No',
						type: 'warning',
						closeOnClickModal: false,
					})
						.then(() => {
							updateTicketFrom(submitType);
							state.submitTicketLoading = false;
						})
						.catch(() => {
						});
				} else {
					updateTicketFrom(submitType);
					state.submitTicketLoading = false;
				}
			} catch (err: any) {
				// 如果有任何一个表单未通过验证，处理错误
				if (err.message) {
					ElMessage.error(err.message);
				}
				state.submitTicketLoading = false;
				//验证未通过时展开评论框
				openCommentBox();

				return false;
			}
		};

		const updateTicketFrom = async (submitType: number) => {
			// 保存当前路由信息，确保关闭的是提交工单时的标签页
			const originalRoute = { ...route };

			await Promise.all([proxy.$refs.ruleFormRef1.validate(), proxy.$refs.ruleFormRef2.validate(), proxy.$refs.ruleFormRef3.validate()]).catch(
				(err) => {
					ElMessage.error("Please fill in the required fields marked with '*'.");
					openCommentBox();
					return new Promise(() => { });
				}
			);

			if (isUploading.value) {
				ElMessage.error('Please wait, file is uploading');
				return;
			}

			if (submitType == 0) {
				if (state.editorNote.textVal == '') {
					ElMessage.error('Please enter the description.');
					return;
				}
			} else {
				if (state.editorComment.textVal == '') {
					ElMessage.error('Please enter the comment.');
					return;
				}
			}

			//如果选择了solved，则要把assignee恢复原来的人，主要是为了能够邮件通知他状态变更
			if (state.formData.ticket_Status == 'Solved' || state.formData.ticket_Status == 'Cancelled') {
				state.formData.nodeId = state.oldNodeId;
			}

			var formData = getFormData(submitType);

			const encodedTicketNumber = encodeTicketNumber(formData.ticket_Number);
			formData.ticket_Url = Local.get('SystemUrl') + '/#/login?redirect=/dashboard/editTicket/' + encodedTicketNumber;

			if (formData.ticket_Project == '') {
				formData.ticket_Project = '0'
			}

			if (state.oldUserGrantFlag) {
				formData.scheduledDate = new Date();

				cmTicketsApi
					.UpdateTicketGrant(formData)
					.then((rs) => {
						ElMessage({
							message: t('message.ticketButtons.submitTicketSuccess'),
							type: 'success',
						});

						state.fileList = [];
						proxy.$refs.ruleFormRef1.resetFields();
						proxy.$refs.ruleFormRef2.resetFields();
						proxy.$refs.ruleFormRef3.resetFields();
						state.submitTicketLoading = false;

						nextTick(() => {
							//刷新页面
							mittBus.emit<any>('RefreshTicketList', {});
							mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...originalRoute }));
						});
					})
					.catch((err) => {
						ElMessage.error(err.resultMsg);
					})
					.finally(() => {
						state.submitTicketLoading = false;
					});

				return;
			}

			var action = 'Approved';
			var permission = state.currentNode.permissions.find((item: any) => {
				if (state.formData.ticket_Status == 'Scheduled') {
				}
				return item.permissionType === action;
			});

			if (!permission) {
				ElMessage.error('No permission.');
				return;
			}

			if (state.formData.ticket_Status == 'Scheduled') {
				formData.OnHold = true;
				action = 'Scheduled';
			} else {
				formData.OnHold = false;
			}

			if (submitType == 1) {
				formData.ticketContentsInfo = {
					ticket_Content: state.editorComment.htmlVal,
					ticket_Content_Type: state.formData.ticket_Content_Type,
				};
			}

			var reqWorkFlow = {
				elsaInstancesId: state.elsaInstancesId,
				activityId: permission.activityId,
				trigger: permission.trigger,
				taskId: permission.taskId,
				Data: formData,
				Action: state.formData.ticket_Status,
				executionType: action,
				userTrusteeId: formData.userTrusteeId,
				UserTrusteeIds: formData.UserTrusteeIds,
			};

			state.submitTicketLoading = true;
			state.ticketCommentDrawer = false;

			workflowCrmApi
				.Workflowexecution(reqWorkFlow)
				.then((rs) => {
					ElMessage({
						message: t('message.ticketButtons.submitTicketSuccess'),
						type: 'success',
					});

					state.submitTicketLoading == false;

					state.fileList = [];

					proxy.$refs.ruleFormRef2.resetFields();

					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));

					//刷新页面
					mittBus.emit<any>('RefreshTicketList', {});
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg);
					state.submitTicketLoading = false;
				})
				.finally(() => {
					state.submitTicketLoading == false;
				});
		};

		//创建子工单
		const onCreateSubTicket = () => {
			const encodedTicketNumber = encodeTicketNumber(state.formData.id);
			router.push('/dashboard/ticket/create/' + encodedTicketNumber);
		};

		const downloadFile = (row: any) => {
			request({
				url: `${BASE_URL.value}api/files/ticketFile/${row.id}`,
				method: 'get',
				responseType: 'blob'
			})
				.then((response: any) => {
					// 创建 Blob URL
					const blob = new Blob([response], { type: response.type || 'application/octet-stream' });
					const url = window.URL.createObjectURL(blob);

					// 创建下载链接
					const link = document.createElement('a');
					link.style.display = 'none';
					link.href = url;
					link.download = row.attachment_Original_File_Name; // 可以根据需要设置文件名

					// 触发下载
					document.body.appendChild(link);
					link.click();

					// 清理
					document.body.removeChild(link);
					window.URL.revokeObjectURL(url);
				})
				.catch((error: any) => {
					console.error('Download failed:', error);
					// 可以添加用户友好的错误提示
					ElMessage.error('Download failed');
				});
		};

		const handleFilePreview = (item: any, row: any) => {
			if (compareImg(row.attachment_File_Ext)) {
				// 过滤出图片类型的附件
				previewSrcList.value = item.ticketAttachments
					.filter((item: any) => compareImg(item.attachment_File_Ext))
					.map((item: any) => `${BASE_URL.value}` + item.attachment_File_Name);
				// 设置预览图片的索引
				previewIndexRef.value = previewSrcList.value.indexOf(`${BASE_URL.value}` + row.attachment_File_Name);
				// 显示图片预览
				previewVisibleRef.value = true;
			} else {
				fileData.value = row;
				// 显示文件对话框
				ticketFileDialogRef.value.openDialog(row);
			}
		};

		const previewFile = (type: string) => {
			// 判断是否为图片类型的附件
			const fileTypes = [
				'bmp',
				'jpg',
				'png',
				'gif',
				'svg',
				'webp',
				'ico',
				'jpeg',
				'pdf',
				'doc',
				'docx',
				'xls',
				'xlsx',
				'ppt',
				'pptx',
				'txt',
				'mp3',
				'mp4',
			];
			return fileTypes.includes(type.toLowerCase());
		};

		//判断类型为图片的方法
		const compareImg = (type: string) => {
			const imageTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg'];
			return imageTypes.includes(type.toLowerCase());
		};

		const imagePreviewVisible = () => {
			previewVisibleRef.value = false;
			state.formData.ticketContents.forEach((item: any) => (item.popoverShow = false));
		};

		const GetCategoryByProjectId = async (id: any) => {
			//类别
			await cmCategoryApi.GetListByUserId({ ProjectId: id, order: 'Category_Code', sort: 'asc' }).then((rs) => {
				state.categoryOpts = rs.data;

				//这里是公共调用的，但是编辑的时候英文在创建的时候选择assign to之后，不应该默认就是谁了
				if (!route.params.id) {
					//console.log("route.params.id:",route.params.id);
					loadAssigneesByCustomer('FromWatch');
				}
			});
		};
		const getUserDelegateData = () => {
			let usr = { userTrusteeId: currentUser.value.userId };

			userDelegateApi.GetSelectUserDelegate(usr).then((rs) => {
				state.formData.userDelegateData = rs.data;
			});
		};


		const initTableDataparams = () => {
			state.tableData.topParams = {
				groupId: '' as any,
				ticket_Status: '' as any,
				assigToId: '' as any,
				ticket_From: '',
			} as any;

			//清空 所有统计按钮的 选中状态
			state.buttonSelected = {};

			//默认使用params的参数进行查询 1：表示使用topParams来查询
			state.tableData.paramsType = 0;
		};

		//初始化
		const onInit = async () => {
			//为了把 【统计的搜索条件】  和 【搜索框的条件】区分开来 要这样写
			const query = { ...state.tableData.params };

			if (route.name == 'ScheduledList') {
				state.tableData.params.onHold = true;
				query.onHold = true;
			}

			if (state.tableData.paramsType === 1) {
				query.groupId = state.tableData.topParams.groupId;
				query.ticket_Status = state.tableData.topParams.ticket_Status;
				query.assigToId = state.tableData.topParams.assigToId;
				query.ticket_From = state.tableData.topParams.ticket_From;
			}

			query.disalogList = state.disalogList;

			if (state.ticketTypeData.length == 0) {
				state.ticketTypeData.push({ value: "Public Ticket", label: "Public Ticket" });
				state.ticketTypeData.push({ value: "Internal Ticket", label: "Internal Ticket" });
			}

			cmTicketsApi
				.Query(query)
				.then(async (rs: any) => {
					if (rs.data.length == 0) {
						mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
						router.push('/dashboard/ticket');
					}
				})
				.catch(() => {
					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					router.push('/dashboard/ticket');
				})
				.finally(() => {
				});
		};

		onMounted(async () => {

			//加这两行，为了默认打开Description，编辑器能滚动条。
			onoffNote(true);
			state.isEditNote = true;

			if (props.itemParams) {
				state.disabledCustomer = true;
			}

			var userTrusteeId_Temp = 0;

			//增加这段代码，主要是为了提前获取delegate信息，然后给下面帮忙customer和project提前使用，要不然就会出现以当前登录人的id来获取信息了
			if (route.params.id) {
				const decodedTicketId = decodeTicketNumber(route.params.id.toString());
				if (decodedTicketId > 0) {
					initTableDataparams();

					state.tableData.params.ticket_Number = decodedTicketId;

					onInit();

					await cmTicketsApi.DetailDelegate(decodedTicketId).then((rs) => {
						userTrusteeId_Temp = rs.data.userTrusteeId
					});
				}
			}

			if (currentUser.value.userType == 'End User') {
				if (state.ticketId > 0 && !state.hasApprovedPermission) {
					organizationApi.GetCompanyProjectTree({}).then((rs) => {
						state.customerOpts = rs.data.treeNodeList;
					});
				} else {
					organizationApi.GetCompanyProjectTreeByUserId({}).then((rs) => {
						state.customerOpts = rs.data.treeNodeList;
					});
				}
			} else {
				const tempId = parseFloat(state.ticketId) ?? 0;

				organizationApi.GetCompanyProjectTreeByUserId({ TicketId: tempId, userTrusteeId: userTrusteeId_Temp }).then((rs) => {
					state.customerOpts = rs.data.treeNodeList;
				});
			}

			initCommentCollapse();
			initFooterClass();
			getUserDelegateData();

			var dictArr = ['Priority', 'ContentType', 'BusinessUnit'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.priorityOpts = rs.data?.find((a: any) => {
					return a.dictValue === 'Priority';
				})?.items;

				state.contentTypeOpts = rs.data?.find((a: any) => {
					return a.dictValue === 'ContentType';
				})?.items;

				state.businessUnitData = rs.data?.find((a: any) => {
					return a.dictValue === 'BusinessUnit';
				})?.items;
			});

			if (route.params.id) {
				// 尝试解码ticket number，如果解码失败则按原来的方式处理
				const decodedTicketId = decodeTicketNumber(route.params.id.toString());
				if (decodedTicketId > 0) {
					state.ticketId = decodedTicketId;
					loadDetail();
				} else {
					// 如果解码失败，可能是旧的未编码的ID，直接使用
					// state.ticketId = parseFloat(route.params.id.toString()) ?? 0;

					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					router.push('/dashboard/ticket');
				}
			} else {
				if (route.name == 'TicketCreate') {
					state.userGrantShow = currentUser.value.privateTicket;
				}
			}

			//如果是终端用户，则获取终端用户的基本信息，然后填充到页面上
			if (state.ticketId == 0) {
				await userInfoApi.Detail(currentUser.value.userId).then((rs) => {
					state.formData.ticket_First_Name = rs.data.realName;
					state.formData.ticket_Last_Name = rs.data.lastName;

					//如果只有一项则默认
					if (rs.data.customers.length == 1) {
						updateProjectOptions(rs.data.customers[0]);
					}

					//console.log("currentUser.value:",currentUser.value);
					if (currentUser.value.userType == "End User") {
						state.formData.businessUnit = rs.data.businessUnit
					}

					//如果是OpenItem过来
					if (props.itemParams) {
						//逻辑有问题，这个不可以取用户第一个公司所属的bu用来作为下拉框的默认值，这样子就会不匹配customer
						// state.formData.businessUnit = rs.data.businessUnit;

						nextTick(() => {
							const node = getNodeByOrgId(props.itemParams.Customer);
							state.formData.businessUnit = node.businessUnit
						})
					}

					state.formData.ticket_Customer_Email = rs.data.email;
					state.formData.ticket_Customer_Phone = rs.data.mobile;
				});

				GetCategoryOpts();
			}

			//9/21 - Sub Ticket: End user创建的子工单，Customer必须和主工单Customer保持一致
			if (route.params.parentId) {
				// 尝试解码parent ticket ID，如果解码失败则使用原始值
				const decodedParentId = decodeTicketNumber(route.params.parentId.toString());
				if (decodedParentId > 0) {
					state.formData.ticket_Parent_Id = decodedParentId;

					await cmTicketsApi.Detail(state.formData.ticket_Parent_Id).then((rs) => {
						const obj = rs.data;

						var projectId = parseInt(obj.ticket_Project) > 0 ? obj.ticket_Project : obj.ticket_Customer;
						updateProjectOptions(projectId + '');

						state.formData.ticketContentType = obj.ticketContentType;
						parentTicketConentType.value = obj.ticketContentType;

						//创建子工单时，默认取主工单的
						state.formData.ticket_Customer_Email = obj.ticket_Customer_Email;
						state.formData.ticket_Customer_Phone = obj.ticket_Customer_Phone;
						state.formData.businessUnit = rs.data.businessUnit
					});

					GetCategoryOpts();
				} else {
					// 如果解码失败，可能是旧的未编码的ID，直接使用
					// state.formData.ticket_Parent_Id = parseFloat(route.params.parentId.toString()) ?? 0;

					mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					router.push('/dashboard/ticket');
				}
			}

			//事件监听器
			mittBus.on<any>('UpdateParentTicket', (row: any) => {
				state.formData.parent_ticket_Number = row.id;

				var projectId = parseInt(row.ticket_Project) > 0 ? row.ticket_Project : row.ticket_Customer;

				updateProjectOptions(projectId + '');

				const node = getNodeByOrgId(state.formData.ticket_Customer.toString());

				state.formData.businessUnit = node.businessUnit

				GetCategoryOpts();

				state.formData.ticketContentType = row.ticketContentType;
				state.formData.ticket_Parent_Id = row.id;
				state.disabledCustomer = true;

			});
		});


		const handleClick = (event) => {
			const src = event.src;
			srcList.value = [];
			srcList.value.push(src);
			showPreview.value = true;
		}

		const handleImageClick = (event) => {
			if (event.target.tagName === 'IMG') {
				const alt = event.target.alt;
				if (alt != "noView") {
					handleClick(event.target);
				}
			}
		};


		const showOpenDate = computed(() => {
			let showDateVisible = false;

			if (
				!state.formData.ticket_Open_Date?.includes('2001') &&
				!state.formData.ticket_Open_Date?.includes('0001') &&
				!state.formData.ticket_Open_Date?.includes('1900') &&
				!state.formData.ticket_Open_Date?.includes('1970') &&
				isNotEmptyOrNull(state.formData.ticket_Open_Date)
			) {
				showDateVisible = true;
			}

			return showDateVisible;
		});

		let showScheduleDateVisible = false;
		const showScheduleDate = computed(() => {
			if (state.formData.ticket_Status == 'Scheduled') {
				if (state.hasApprovedPermission) {
					showScheduleDateVisible = true;
				}
			}

			if (state.ticketId <= 0 && route.name == 'ScheduleTicketCreate') {
				showScheduleDateVisible = true;
			}

			if (
				state.formData.scheduledDate?.includes('2001') ||
				state.formData.scheduledDate?.includes('0001') ||
				state.formData.scheduledDate?.includes('1900') ||
				state.formData.scheduledDate?.includes('1970')
			) {
				state.formData.scheduledDate = '';
			}

			return showScheduleDateVisible;
		});

		const showCloseDate = computed(() => {
			let showDateVisible = false;
			if (
				!state.formData.ticket_Close_Date?.includes('2001') &&
				!state.formData.ticket_Close_Date?.includes('0001') &&
				!state.formData.ticket_Close_Date?.includes('1900') &&
				!state.formData.ticket_Close_Date?.includes('1970') &&
				isNotEmptyOrNull(state.formData.ticket_Close_Date)
			) {
				showDateVisible = true;
			}

			return showDateVisible;
		});

		onBeforeRouteLeave(() => {
			//重置表单
			if (state.ticketId == 0) {
				proxy.$refs.ruleFormRef1.clearValidate();
				proxy.$refs.ruleFormRef2.clearValidate();
				proxy.$refs.ruleFormRef3.clearValidate();
			}

			state.submitTicketLoading = false;
		});

		const initCommentCollapse = () => {
			activeNames.value = [];
			activeNames.value = [commentCollapseItemName];
		};

		const initFooterClass = () => {
			footerClass.value = [];
			footerClass.value.push('full-height');
			footerClass.value.push('new-footer-expand');
		};

		const commentBoxCollapse = (val: string[]) => {
			initFooterClass();

			if (val.indexOf(commentCollapseItemName) > -1 && footerClass.value.indexOf('new-footer-expand') == -1) {
				footerClass.value = ['new-footer-expand'];
			}
		};

		const onAddComment = () => {
			state.ticketCommentDrawer = true;

			//加以下，为了清缓存。
			state.submitTicketLoading == false;
			state.editorComment.textVal = '';
			state.editorComment.htmlVal = '';
			// loadDetail();
		};

		const onCloseComment = () => {
			state.ticketCommentDrawer = false;
			state.editorComment.textVal = '';
			state.editorComment.htmlVal = '';
		};

		const onSubmitComment = () => {
			proxy.$refs.ruleFormRefComment.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var formData = getFormData(1);

				if (state.editorComment.textVal == '') {
					ElMessage.error('Please enter the comment.');
					return;
				}
				var obj = {
					cmTicketId: formData.id,
					Ticket_Content: state.editorComment.htmlVal,
					Ticket_Content_Type: formData.ticket_Content_Type,
					ticketAttachments: formData.ticketAttachments,
					assigToId: formData.assigToId,
					ToStatus: formData.ticket_Status,
					FromStatus: formData.ticketStatusFlag,
					Ticket_Customer_Project: formData.ticket_Customer_Project,
				};

				cmTicketsContentApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');

						proxy.$refs.ruleFormRefComment.resetFields();

						//刷新页面
						onCloseComment();

						mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 0, ...route }));
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg);
						state.submitTicketLoading = false;
					})
					.finally(() => {
						state.submitTicketLoading == false;
					});
			});
		};

		const openCommentBox = () => {
			if (activeNames.value.indexOf(commentCollapseItemName) == -1) {
				activeNames.value = [commentCollapseItemName];
			}
			commentBoxCollapse(activeNames.value);
		};

		const onStatus = () => {
			if (state.formData.ticket_Status == 'Scheduled') {
				state.formData.scheduledDate = '';
				showScheduleDateVisible = true;
			} else {
				showScheduleDateVisible = false;
			}
		};

		const disabledDateFn = (time: any) => {
			const today = new Date();
			today.setHours(0, 0, 0, 0); // 设置时间为当天的零点

			return time.getTime() < today.getTime() + 1;
		};

		const SelectParentTicket = () => {
			ticketListDialogRef.value.openDialog({});
		};

		const ClearParentTicket = () => {
			state.categoryOpts = [];

			state.projectOpts = [];
			localProjectTree?.value?.setCheckedKeys([]);

			state.formData.ticket_Project = '';
			state.formData.parent_ticket_Number = '';
			state.formData.ticket_Customer = '';
			state.formData.businessUnit = '';

			delete state.formData.ticket_Parent_Id;

			state.disabledCustomer = false;
		};

		const onoff = (isOn: boolean) => {
			isChooseGrant.value = isOn;

			if (isOn) {
				if (isInitialized_ticket_Category) {
					state.grantIds.push(currentUser.value.nodeId);
					if (state.ticketId == 0) {
						state.formData.ticketContentType = 'Internal Ticket';
					}
				}
			} else {
				state.grantIds = [];
			}
		};

		const onoffNote = (isopen: boolean) => {
			if (isopen) {
				state.editorNote.disable = false;
			} else {
				state.editorNote.disable = true;
			}
		};

		// 通过check的回调里面获取节点id,再获取节点的node对象
		const checkChange = (data: any) => {
			//根据key拿到Tree组件中的node节点
			const node = localTreeGrant.value.getNode(data.value);
			//调用节点处理方法
			setNode(node);
		};

		//递归设置子节点和父节点
		const setNode = (node: any) => {
			if (node != null && node.checked) {
				setChildNode(node);
				setParentNode(node);
			}
		};

		//递归设置父节点全部取消选中
		const setParentNode = (node: any) => {
			if (node.parent) {
				for (const key in node) {
					if (key === 'parent') {
						node[key].checked = false;
						//递归调用相应父节点处理函数
						setParentNode(node[key]);
					}
				}
			}
		};

		//递归设置子节点全部取消选中
		const setChildNode = (node: any) => {
			if (node.childNodes && node.childNodes.length) {
				node.childNodes.forEach((item: any) => {
					item.checked = false;
					item.expanded = true;

					//递归调用相应子节点处理函数
					setChildNode(item);
				});
			}
		};

		const isTicketTypeDisabled = () => {
			var ticketTypeDisabled = false;

			var selectAssginTree = localTree?.value?.getCheckedNodes();

			const selectSubmitAssignTree = [] as any[];

			selectAssginTree?.map(function (value: any, index: any, array: any) {

				if (value.nodeType == 'UserNode') {
					selectSubmitAssignTree.push(value.nodeId.indexOf('@@@') > -1 ? value.nodeId.split('@@@')[1] : value.nodeId);
				} else if (value.nodeType == 'GroupNode') {
					if (value.children) {
						for (let child of value.children) {
							selectSubmitAssignTree.push(child.nodeId.indexOf('@@@') > -1 ? child.nodeId.split('@@@')[1] : child.nodeId);
						}
					}
				}
			});


			if (state.formData.id > 0 && state.formData.disabledTicketTypeUpdate === 'Disabled') {
				ticketTypeDisabled = true;
			} else if (state.formData.id > 0 && state.formData.ticket_Parent_Id > 0 && state.formData.parentTicketContentType === 'Internal Ticket') {
				ticketTypeDisabled = true;
			} else if (state.formData.id > 0 && selectSubmitAssignTree.length > 0 && selectSubmitAssignTree.includes(currentUser.value.userId.toString())) {
				ticketTypeDisabled = false;
			} else if (state.formData.id > 0 || parentTicketConentType.value === 'Internal Ticket') {
				ticketTypeDisabled = true;
			}

			if (state.formData.id > 0 && state.formData.delegateId != null && state.formData.delegateId.includes(currentUser.value.userId)) {
				ticketTypeDisabled = false;
			}
			return ticketTypeDisabled;
		};

		const ticketTypeChangeTip = computed(() => {
			var tipContent = '';

			if (
				state.formData.id > 0 &&
				firstTicketContentType.value != '' &&
				firstTicketContentType.value == 'Public Ticket' &&
				state.formData.ticketContentType == 'Internal Ticket' &&
				state.formData.ticket_Parent_Id == 0
			) {
				tipContent = "All of the related sub tickets will be also changed to Internal ticket once clicking 'Submit Ticket' button.";
			}

			return tipContent;
		});

		const beforeImageUpload = (file: any) => {
			//监控文件上传文本提示
			dragCounter.value = 0;
			isDropFileHere.value = false;
			const maxSize = file.size / 1024 / 1024 <= 20;
			if (!maxSize) {
				ElMessage.warning(t('message.page.uploadLimit'));
				return false;
			}
		};

		const handleUploadChange = (file: any, fileList: any) => {
			if (file.status === 'ready') {
				isUploading.value = true;
			} else if (fileList.every((f: { status: string }) => ['success', 'fail'].includes(f.status))) {
				isUploading.value = false;
			}
		};

		//上传成功时触发（目前没有用到）
		//直接获取 state.fileList 可以得到 上传的文件列表了
		const onUploadSuccess = (result: any, file: any) => {
			if (result == undefined) {
				return;
			}

			if (result.resultCode !== 200) {
				return;
			}

			if (state.formData.ticketAttachments != undefined) {
				state.formData.ticketAttachments.some((a: any) => a.attachment_File_Id === file.id);
			}
		};

		const customUpload = async (options: any) => {
			const { file, onSuccess, onError, onProgress } = options;

			const formData = new FormData();
			formData.append('file', file);

			try {
				const token = Local.get('token');

				const response = await request.post(state.actionStr, formData, {
					headers: {
						Authorization: `Bearer ${token}`,
						'Content-Type': 'multipart/form-data',
					},
					onUploadProgress: (event: any) => {
						if (event.total > 0) {
							onProgress({ percent: (event.loaded / event.total) * 100 }, file);
						}
					},
				});

				file.response = response;

				onSuccess(response, file, state.fileList);
			} catch (error: any) {
				onError(error, file, state.fileList);
			}
		};

		const getEditorblur = (isBlur: boolean) => {
			//console.log("isBlur:",isBlur);
			if (!isBlur && state.isEditNote) {
				var reqObj = getFormData(0);
				var obj = {
					Id: state.ticketId,
					Ticket_Note: state.editorNote.htmlVal,
					assigToId: reqObj.assigToId,
					ToStatus: reqObj.ticket_Status,
					FromStatus: reqObj.ticketStatusFlag,

				};

				cmTicketsApi
					.SaveDescription(obj)
					.then(() => {

					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg);
					})
					.finally(() => { });
			}
		};

		const handleDragEnter = (event: any) => {
			dragCounter.value++;
			if (dragCounter.value === 1) {
				isDropFileHere.value = true;
			}
		};

		const handleDragLeave = (event: any) => {
			dragCounter.value--;
			if (dragCounter.value === 0) {
				isDropFileHere.value = false;
			}
		};

		const handleDrop = (event: any) => {
			event.preventDefault();
			//监控文件上传文本提示
			dragCounter.value = 0;
			isDropFileHere.value = false;
		};

		const UpdateVisible = (item: any, val: boolean) => {
			if (item.popoverShow !== val) {
				item.popoverShow = val;
			}
		};

		const findParentAndSiblings = (nodes: any[], value: string): any => {
			let result = { currentNode: null, parentNode: null, siblingNodes: [] };

			for (const node of nodes) {
				if (node.value == value) {
					result = { currentNode: node, parentNode: null, siblingNodes: node.children };
				}

				if (node.children) {
					const foundIndex = node.children.findIndex((child: { value: string; }) => child.value === value);

					if (foundIndex > -1) {
						var findNode = node.children[foundIndex]

						if (findNode.nodeType == "ProjectNode") {
							result = {
								currentNode: findNode,
								parentNode: node,
								siblingNodes: node.children
							}
						} else {
							result = {
								currentNode: findNode,
								parentNode: node,
								siblingNodes: findNode.children
							}
						}
					} else {
						const tempResult = findParentAndSiblings(node.children, value);
						if (tempResult.currentNode) {
							result = tempResult;
						}
					}
				}
			}

			return result;
		};

		const checkChangeCustomer = (data: any) => {
			if (data.disabled) {
				return;
			}

			state.formData.ticket_Category = '';

			updateProjectOptions(data.value);

			const node = getNodeByOrgId(state.formData.ticket_Customer.toString());

			state.formData.businessUnit = node.businessUnit

			GetCategoryOpts();

			localCompanyTree.value.blur();

			nextTick(() => {
				proxy.$refs.ruleFormRef1.clearValidate();
				proxy.$refs.ruleFormRef1.validateField(['ticket_Category', 'nodeId']);
			});
		};

		const getNodeByOrgId = (orgId: any) => {
			return findNode(state.customerOpts, orgId);
		};

		const findNode = (nodes: any, orgId: any): any => {
			for (const node of nodes) {
				if (node.value == orgId) {
					return node;
				}
				if (node.children && node.children.length > 0) {
					const found = findNode(node.children, orgId);
					if (found) return found;
				}
			}
			return null;
		};


		const clearCustomer = () => {
			state.categoryOpts = [];

			state.projectOpts = [];
			localProjectTree?.value?.setCheckedKeys([]);

			state.formData.ticket_Category = '';
			state.formData.ticket_Project = '';
			state.disabledProject = false;
		};

		const clearProject = () => {
			state.formData.ticket_Category = '';
			state.formData.ticket_Project = '';
			state.disabledProject = false;
			GetCategoryOpts();
		};

		function updateProjectOptions(dataValue: string) {
			state.projectOpts = [];
			state.formData.ticket_Project = '';
			// state.disabledProject = true;
			const { currentNode, parentNode, siblingNodes } = findParentAndSiblings(state.customerOpts, dataValue);

			if (currentNode) {
				if (currentNode.nodeType === "ProjectNode") {
					state.projectOpts = siblingNodes;
					state.formData.ticket_Customer = parentNode ? parentNode.value : '';
					state.formData.ticket_Project = currentNode.value;
					// state.disabledProject = false;
				} else {
					state.projectOpts = siblingNodes.filter((item: any) => {
						return item.nodeType === "ProjectNode"
					});

					state.formData.ticket_Customer = currentNode.value;
				}
				if (props.itemParams) {
					state.projectOpts = props.itemParams.Projects;
				}
			}

			if (state.projectOpts.length > 0) {
				state.showTicketProject = true
			} else {
				state.showTicketProject = false
			}
		}

		const checkChangeProject = (data: any) => {
			if (data.disabled) {
				return;
			}

			state.formData.ticket_Category = '';

			state.formData.ticket_Project = data.value;

			GetCategoryOptsByProject();

			localProjectTree.value.blur();
		};

		const GetCategoryOptsByCustomer = () => {
			state.categoryOpts = [];

			if (parseThanZero(parseInt(state.formData.ticket_Customer))) {
				var projectId = parseInt(state.formData.ticket_Customer);
				GetCategoryByProjectId(projectId);
			}
		}

		const GetCategoryOptsByProject = () => {
			state.categoryOpts = [];

			if (parseThanZero(parseInt(state.formData.ticket_Project))) {
				var projectId = parseInt(state.formData.ticket_Project);
				GetCategoryByProjectId(projectId);
			}
		}

		const GetCategoryOpts = () => {
			state.categoryOpts = [];
			if (props.itemParams) {
				state.formData.ticket_Customer = props.itemParams.Customer;
			}
			if (parseThanZero(parseInt(state.formData.ticket_Customer))) {
				var projectId =
					parseInt(state.formData.ticket_Project) > 0 ? parseInt(state.formData.ticket_Project) : parseInt(state.formData.ticket_Customer);
				GetCategoryByProjectId(projectId);
				//ccb
				updateProjectOptions(projectId + '');
			}
		}

		const disabledBusinessUnit = computed(() => {
			return true;
		});

		const onDelComent = (id: number) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				cmTicketsContentApi
					.DeleteByKey(id)
					.then((rs: any) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 0, ...route }));
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg);
					});
			});


		}

		return {
			Download,
			localTree,
			localTreeGrant,
			localCompanyTree,
			loadAssigneesByCustomer,
			formatPast,
			onMoreDropdown,
			onSubmitAsNew,
			onMounted,
			onCreateSubTicket,
			onSubmitTicket,
			onUploadSuccess,
			downloadFile,
			beforeImageUpload,
			acceptUploadType,
			...toRefs(state),
			showOpenDate,
			showCloseDate,
			showScheduleDate,
			onStatus,
			currentUser,
			isNotEmptyOrNull,
			readonly_formData,
			firstTicketContentType,
			parentTicketConentType,
			SetCategory,
			onRelateTicket,
			commentBoxCollapse,
			disabledDateFn,
			SelectParentTicket,
			ticketListDialogRef,
			ticketFileDialogRef,
			route,
			ClearParentTicket,
			onoff,
			onoffNote,
			checkChange,
			isChooseGrant,
			isTicketTypeDisabled,
			ticketTypeChangeTip,
			handleUploadChange,
			customUpload,
			onAddComment,
			onCloseComment,
			onSubmitComment,
			getEditorblur,
			handleFilePreview,
			previewFile,
			imagePreviewVisible,
			handleDragEnter,
			handleDragLeave,
			handleDrop,
			UpdateVisible,
			previewVisibleRef,
			previewIndexRef,
			previewSrcList,
			fileData,
			isDropFileHere,
			editorStyle,
			checkChangeCustomer,
			checkChangeProject,
			localProjectTree,
			clearCustomer,
			clearProject,
			getUserDelegateData,
			disabledBusinessUnit,
			showPreview,
			srcList,
			onDelComent,
			handleImageClick,
		};
	},
});
</script>

<style scoped lang="scss">
:deep(.el-header) {
	--el-header-padding: 0 20px;
	--el-header-height: 30px;
	box-sizing: border-box;
	flex-shrink: 0;
	height: var(--el-header-height);
	padding: var(--el-header-padding);
	border-bottom: none;
}

.tk-header-hide {
	min-height: 200px;
	width: auto;
	display: flex;
}

.el-textarea__inner {
	overflow: hidden !important;
}

:deep(.el-upload-list__item.is-success .el-upload-list__item-status-label) {
	display: none;
}

:deep(.el-upload-list__item .el-icon--close) {
	display: inline;
}

.uploader-icon {
	width: 24px;
	height: 24px;
	font-size: 20px;
}

:deep(.el-collapse-item__header) {
	height: 35px;
	padding-left: 10px;
}

:deep(.el-collapse-item__header.is-active) {
	border-bottom-color: #efebeb;
}

:deep(.el-collapse-item__content) {
	padding-top: 10px;
	padding-bottom: 0;
}

.card-footer {
	height: 600px;
	width: 100%;
	overflow-y: auto;
}

.comment-button {
	padding-left: 10%;
}

.ticket-info_layout .tk-footer.new-footer-default {
	height: 50px;
}

.ticket-info_layout .tk-footer.new-footer-default.new-footer-expand {
	height: 100px;
}

:deep(.el-input__wrapper) {
	position: relative;

	.el-input__inner {
		padding-right: 18px;
	}

	.el-input__suffix {
		position: absolute;
		right: 8px;
		top: 50%;
		transform: translateY(-50%);
	}
}

::v-deep .card-footer .el-input__wrapper .el-input__suffix {
	position: absolute !important;
	right: 8px !important;
	top: 0%;
	transform: translateY(-50%) !important;
}

::v-deep .el-divider--horizontal {
	margin: 5px 0;
}

::v-deep .el-timeline-item {
	padding-bottom: 0px;
}

::v-deep.el-main {
	--el-main-padding: 6px;
}
</style>
<style scoped lang="scss">
.button-group {
	display: flex;
	padding-left: 90px;
	padding-bottom: 10px;
}

:deep(.el-collapse) {
	border-radius: 4px;
	box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
}

:deep(.el-collapse.full) {
	height: 100%;
}

:deep(.el-collapse-item__header) {
	height: 45px;
}
</style>

<style scoped lang="scss">
.ticket-info_layout {
	padding: 10px;
	display: flex;
	height: 100%;

	.ticket-info-left {
		max-width: 495px;
		width: 600px;
	}

	.ticket-info-left,
	.ticket-info-right {
		height: 100%;
	}

	.ticket-info-right {
		flex: 1;
		margin: 0px 10px;
		display: flex;
		flex-direction: column;
	}

	.btn-card {
		/* 提交Notes 按钮的样式 */
		min-height: 60px;
	}

	.notes-card {
		min-height: 410px;
	}

	.list-card {
		flex-grow: 1;
		/* 其他内容的样式 */
	}

	.dic-tree-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		/* 可根据需求调整 */
	}

	.dic-tree-header-title {
		font-size: 16px;
		/* 可根据需求调整 */
		font-weight: bold;
		/* 可根据需求调整 */
	}

	.comment-dr {}

	:deep(.el-drawer.rtl) {
		width: 800px !important;
	}
}

@media (max-width: 1024px) {
	.ticket-info_layout {
		display: block;
		flex-direction: column;

		.el-menu {
			width: 100% !important;
		}

		.ticket-info-left {
			width: 100%;
			/* 在小屏幕上，左边部分占据整个宽度 */
			margin-bottom: 10px;
			max-width: none;
			height: auto !important;
		}

		.ticket-info-right {
			margin-left: 0;
			width: 100%;
			/* 在小屏幕上，右边部分占据整个宽度 */
		}

		:deep(.el-drawer.rtl) {
			width: 100% !important;
		}
	}
}

@media (max-width: 768px) {
	.ticket-info_layout {
		display: block;
		flex-direction: column;

		.el-menu {
			width: 100% !important;
		}

		.ticket-info-left {
			width: 100%;
			/* 在小屏幕上，左边部分占据整个宽度 */
			margin-bottom: 10px;

		}

		.ticket-info-right {
			margin-left: 0;
			width: 100%;
			/* 在小屏幕上，右边部分占据整个宽度 */
		}
	}
}

:deep(.el-collapse-item__content) {
	height: 100%;
	max-height: calc(100vh - 230px);
	overflow-y: auto;
	overflow-x: hidden;
}

.card-header {
	display: flex;
	align-items: center;
	height: 15px;
}


:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
	border: var(--el-descriptions-table-border);
	padding: 3px 11px;
}

:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
	box-sizing: border-box;
	text-align: left;
	line-height: 17px;
	font-size: 13px;
}

:deep(.el-card__body) {
	padding: 9px;
}

::v-deep .el-image-viewer__close {
	font-size: 24px !important;
	/* 调整图标大小 */
}

::v-deep .el-image-viewer__mask {
	opacity: 0.02 !important;
	/* 设置遮罩层透明度为50% */
}
</style>
