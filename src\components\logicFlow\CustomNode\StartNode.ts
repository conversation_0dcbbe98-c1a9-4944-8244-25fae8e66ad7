import { CircleNode, CircleNodeModel, h } from '@logicflow/core';
import { v4 as uuidv4 } from 'uuid';
import { permissions, emailNotices } from '/@/components/logicFlow/config.js';
class StartNode extends CircleNode {
	getLabelShape() {
		const { model } = this.props;
		const { x, y } = model;
		return h(
			'text',
			{
				fill: '#000000',
				fontSize: 12,
				x: x - 12,
				y: y + 4,
				width: 50,
				height: 25,
			},
			'Start'
		);
	}
	getShape() {
		const { model } = this.props;
		const { x, y, r } = model;
		const { fill, stroke, strokeWidth } = model.getNodeStyle();
		return h('g', {}, [
			h('circle', {
				cx: x,
				cy: y,
				r,
				fill,
				stroke,
				strokeWidth,
			}),
			this.getLabelShape(),
		]);
	}
}
class StartModel extends CircleNodeModel {
	// 自定义节点形状属性
	initNodeData(data) {
		if (!data.properties) {
			data.text = {
				value: (data.text && data.text.value) || '',
				x: data.x,
				y: data.y + 35,
				dragable: false,
				editable: true,
			};
			data.properties = {
				name: this.findNodeByName(),
				approveNoticeData: this.initEmailNotices(),
				permissions,
			};
		}
		super.initNodeData(data);
		this.r = 20;
	}
	findNodeByName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `node${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
	setTriggerName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.execute.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `SignalReceived${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
	// 自定义节点样式属性
	getNodeStyle() {
		const style = super.getNodeStyle();
		return style;
	}
	// 自定义锚点样式
	getAnchorStyle() {
		const style = super.getAnchorStyle();
		style.hover.r = 8;
		style.hover.fill = 'rgb(24, 125, 255)';
		style.hover.stroke = 'rgb(24, 125, 255)';
		return style;
	}
	// 自定义节点outline
	getOutlineStyle() {
		const style = super.getOutlineStyle();
		style.stroke = '#88f';
		return style;
	}
	getConnectedTargetRules() {
		const rules = super.getConnectedTargetRules();
		const notAsTarget = {
			message: '起始节点不能作为连线的终点',
			validate: () => false,
		};
		rules.push(notAsTarget);
		return rules;
	}
	initEmailNotices() {
		const approveNoticeData = {};
		emailNotices.forEach((opt) => {
			approveNoticeData[opt.noticeType] = { value: 1 };//开始节点的配置为全局配置，默认是1 表示使用系统默认
		});
		return approveNoticeData;
	}
}

export default {
	type: 'StartNode',
	view: StartNode,
	model: StartModel,
};
