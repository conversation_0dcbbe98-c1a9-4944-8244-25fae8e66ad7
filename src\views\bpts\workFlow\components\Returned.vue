<template>
	<div>
		<h4>{{ nodeName }}</h4>
		<p></p>
		<p>审核人员：{{ item.operatorName }}</p>
		<p>审核结果： <el-tag type="danger">退回</el-tag></p>
		<p>审核时间：{{ item.operationDate }}</p>
		<p>退回原因：{{ item.operationMsg }}</p>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue';
export default defineComponent({
	name: 'workflowOperationsApproved',
	props: {
		nodeData: Object,
	},
	setup(props) {
		const state = reactive({
			nodeName: '',
			item: {},
		});

		onMounted(() => {
			const { nodeName, operationData } = props.nodeData;
			state.nodeName = nodeName;
			state.item = operationData[0];
		});

		return {
			...toRefs(state),
		};
	},
});
</script>