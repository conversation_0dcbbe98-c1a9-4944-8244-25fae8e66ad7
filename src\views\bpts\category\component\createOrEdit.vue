<template>
	<div class="system-edit-category-container">
		<el-dialog :title="state.title" v-model="state.isShowDialog" width="400px" :close-on-click-modal="false"
			@close="onCancel" draggable :destroy-on-close="true">
			<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" label-width="150px"
				label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.categoryFields.Category_Code')" prop="category_Code">
							<!-- <el-input
								v-model="state.ruleForm.category_Code"
								
								:disabled="state.DialogParams.action == 'Add Other Company'"
							/> -->
							<el-select v-model="state.ruleForm.category_Code" filterable clearable style="width: 100%"
								@change="SelectCategoryCode">
								<el-option v-for="item in state.CategoryData" :label="item.itemValue"
									:value="item.itemValue" :key="item.itemId" :description="item.description" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.categoryFields.Category_Description')"
							prop="category_Description">
							<el-input v-model="state.ruleForm.category_Description" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.categoryFields.Category_Status')"
							prop="category_Status_Value">
							<el-select v-model="state.ruleForm.category_Status_Value"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
								:disabled="state.DialogParams.action == 'Add Other Company'">
								<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
								<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.categoryFields.Category_Company')" prop="category_Company">

							<div class="button-tree-container">
								<el-checkbox v-model="checked1" class="check-all-button" @change="selectAll">
									Check All
								</el-checkbox>

								<el-tree-select ref="localCompanyTree" v-model="state.ruleForm.category_Company"
									:data="state.customerOpts" :render-after-expand="false" node-key="orgId"
									show-checkbox :check-strictly="true" check-on-click-node style="width: 100%"
									multiple @change="loadAssignees()" @current-change="onProjectCurrentChange"
									:default-expanded-keys="state.ruleForm.category_Company" :auto-expand-parent="true"
									:props="{ value: 'orgId', label: 'name' }" @node-expand="ClickExpandNode"
									:max-collapse-tags="5" collapse-tags collapse-tags-tooltip filterable>

									<template #default="{ node, data }">
										<div class="custom-option">
											<span>{{ data.name }}</span>

											<template v-if="node.childNodes && node.childNodes.length > 0">
												<el-checkbox @change="optionSelectAll($event, node, data)">
													All
												</el-checkbox>
											</template>
										</div>
									</template>
								</el-tree-select>

							</div>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.categoryFields.NewTicketAssignee')"
							prop="selectedAssigneeList">
							<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')" ref="localTree"
								v-model="state.ruleForm.selectedAssigneeList" :data="state.assigneeOpts"
								:render-after-expand="false" node-key="value" show-checkbox check-strictly
								check-on-click-node style="width: 100%"
								:default-expanded-keys="[state.ruleForm.selectedAssigneeList]"
								:auto-expand-parent="true" filterable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{
						$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { reactive, toRefs, getCurrentInstance, ref, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';

import userInfoApi from '/@/api/user/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import organizationApi from '/@/api/organization/index';
import dictItemApi from '/@/api/dictItem/index';

interface DialogParams {
	action: string;
	id: number;
	companyId: any;
	cmCategoryTalbeRelationId: any;
}

const emit = defineEmits(['fetchData']);

const { t } = useI18n();

const { proxy } = getCurrentInstance() as any;

const localCompanyTree = ref();
const localTree = ref();

const checked1 = ref(false)
const checked2 = ref(false)

const state = reactive({
	loading: false,
	title: t('message.categoryButtons.createCategory'),
	isShowDialog: false,
	DialogParams: {
		action: '',
		id: -1,
		companyId: [] as any,
		cmCategoryTalbeRelationId: [] as any,
	},
	ruleForm: {
		category_Code: '',
		category_Description: '',
		category_Status: 0,
		category_Status_Value: 0,
		selectedAssigneeList: '',
		assigToId: [] as any,
		category_Company: [] as any,
		ActionType: '',
		cmCategoryTalbeRelationId: [] as any,
		oldCategory_Company: [] as any,
	},
	rules: {
		category_Code: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
		category_Description: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
		category_Status_Value: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'change' }],
		category_Company: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'change' }],
		selectedAssigneeList: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'change' }],
	},
	assigneeOpts: [] as any,
	customerOpts: [] as any,
	oldCategory_Company: [] as any,
	CategoryData: [],
	oldAction: ''
});

const openDialog = (params: DialogParams) => {
	checked1.value = false
	checked2.value = false

	onInitForm();

	//已弃用
	// organizationApi.GetEndUserOrganization({}).then((rs) => {
	// 	state.customerOpts = rs.data;
	// });

	/* organizationApi.GetCompanyProjectTree({}).then((rs) => {
		state.customerOpts = rs.data.treeNodeList;
	}); */

	organizationApi.Tree({ IncludeProject: 1 }).then((rs) => {
		state.customerOpts = rs.data;
	});

	state.DialogParams = params;
	var dictArr = ['Category'];
	dictItemApi.Many(dictArr).then((rs: any) => {
		state.CategoryData = rs.data?.find((a: any) => {
			return a.dictValue === 'Category';
		})?.items;
	});

	if (params.action == 'Create') {
		state.oldAction = params.action
		state.title = t('message.categoryButtons.createCategory');
	} else if (params.action == 'Edit') {
		state.title = t('message.categoryButtons.editCategory');

		getData(params.id, params.companyId, params.action, params.cmCategoryTalbeRelationId);
	} else if (params.action == 'Add Other Company') {
		state.title = t('message.categoryButtons.addOtherCompany');

		getData(params.id, params.companyId, params.action, params.cmCategoryTalbeRelationId);
	} else {
		ElMessage.error('Parameter action cannot be empty.');
	}
};

const SelectCategoryCode = (val: any) => {
	if (state.oldAction == "Create") {
		cmCategoryApi
			.QueryForAddCompany({ Category_Code: val })
			.then((rs: any) => {
				if (rs != null && rs.data.length > 0) {
					openDialog({
						action: 'Add Other Company',
						id: rs.data[0].id,
						companyId: rs.data[0].category_Company,
						cmCategoryTalbeRelationId: rs.data[0].cmCategoryTalbeRelationId,
					});
				}
			})
			.catch(() => { })
			.finally(() => {
			});
	}
}

const onProjectCurrentChange = (data: any) => {
	if (data) {
		state.ruleForm.category_Company.push(data.orgId);
		loadAssignees();

		//根据key拿到Tree组件中的node节点
		const node = localCompanyTree.value.getNode(data.orgId);
		//调用节点处理方法
		setChildNodeExpand(node);
	}
};

const selectAll = (val: any) => {
	if (checked1.value) {
		state.ruleForm.category_Company = state.customerOpts.flatMap((item) => {
			return getOrgIds(item);
		});
	} else {
		state.ruleForm.category_Company = []
	}

	loadAssignees();
}

const getOrgIds = (node) => {
	let orgIds = [node.orgId];

	if (node.children && node.children.length > 0) {
		node.children.forEach(child => {
			orgIds = orgIds.concat(getOrgIds(child));
		});
	}

	return orgIds;
};

const optionSelectAll = (val: any, node, data) => {
	const orgIds = getOrgIds(data);

	if (val) {
		orgIds.forEach(id => {
			if (!state.ruleForm.category_Company.includes(id)) {
				state.ruleForm.category_Company.push(id);
			}
		});
	} else {
		state.ruleForm.category_Company = state.ruleForm.category_Company.filter(id => !orgIds.includes(id));
	}

	loadAssignees();
}

const ClickExpandNode = (data: any) => {
	//根据key拿到Tree组件中的node节点
	const node = localCompanyTree.value.getNode(data.orgId);
	//调用节点处理方法
	setChildNodeExpand(node);
};

//递归设置子节点全部展开
const setChildNodeExpand = (node: any) => {
	if (node != null && node.childNodes && node.childNodes.length) {
		node.childNodes.forEach((item: any) => {
			item.expanded = true;

			//递归调用相应子节点处理函数
			setChildNodeExpand(item);
		});
	}
};

const loadAssignees = async () => {
	state.ruleForm.selectedAssigneeList = '';
	await userInfoApi.GetAllGroupUserListV2({ CustomerIds: state.ruleForm.category_Company }).then((rs) => {
		state.assigneeOpts = rs.data.groupList;
	});

	state.ruleForm.category_Company.map((item: any) => {
		//根据key拿到Tree组件中的node节点
		const node = localCompanyTree.value.getNode(parseInt(item));
		//调用节点处理方法
		setChildNodeExpand(node);
	});
};

const getData = async (id: any, companyId: any, actionType: any, cmCategoryTalbeRelationId: any) => {
	await cmCategoryApi.GetDetailByIdAndCompanyIdV2(id, companyId, cmCategoryTalbeRelationId).then(async (rs) => {
		state.oldCategory_Company = rs.data.category_Company;

		if (actionType == 'Add Other Company') {
			rs.data.category_Company = [] as any;
			rs.data.selectedAssigneeList = '';
		}

		state.ruleForm = Object.assign({}, rs.data);

		if (actionType != 'Add Other Company') {
			await userInfoApi.GetAllGroupUserListV2({ CustomerIds: state.oldCategory_Company }).then((rs) => {
				state.assigneeOpts = rs.data.groupList;

				let haveData = state.assigneeOpts.some((item: any) => {
					if (item.value == state.ruleForm.selectedAssigneeList) {
						return true;
					}

					if (item.children) {
						return item.children.some((child: any) => child.value == state.ruleForm.selectedAssigneeList);
					}
				});

				if (!haveData) {
					state.ruleForm.selectedAssigneeList = '';
				}
			});
		}

		state.ruleForm.ActionType = actionType;
		state.ruleForm.cmCategoryTalbeRelationId = cmCategoryTalbeRelationId;
		state.ruleForm.oldCategory_Company = state.oldCategory_Company;
	});
};

const onInitForm = () => {
	state.isShowDialog = true;

	if (proxy.$refs.ruleFormRef) {
		proxy.$refs.ruleFormRef.resetFields();
	}

	state.assigneeOpts = [] as any;

	state.ruleForm = {
		category_Code: '',
		category_Description: '',
		category_Status: 0,
		category_Status_Value: 0,
		selectedAssigneeList: '',
		assigToId: [] as any,
		category_Company: [] as any,
		ActionType: '',
		cmCategoryTalbeRelationId: [] as any,
		oldCategory_Company: [] as any,
	};
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	// 取消所有勾选
	localTree.value.setCheckedKeys([]);
	onInitForm();
	state.isShowDialog = false;
};

const onSubmit = () => {
	proxy.$refs.ruleFormRef.validate((valid: any) => {
		if (!valid) {
			return;
		}

		var obj = state.ruleForm;
		obj.category_Status = obj.category_Status_Value;

		var selectAssginTree = localTree.value.getCheckedNodes();
		const selectSubmitAssignToIdTree = [] as any[];
		const selectSubmitAssignTree = [] as any[];
		selectAssginTree.map(function (value: any, index: any, array: any) {
			selectSubmitAssignToIdTree.push({ nodeId: value.nodeId, nodeType: value.nodeType, nodeName: value.label });
			if (value.nodeType == 'UserNode') {
				selectSubmitAssignTree.push(value.nodeId);
			} else if (value.nodeType == 'GroupNode') {
				if (value.children) {
					for (let child of value.children) {
						selectSubmitAssignTree.push(child.nodeId);
					}
				}
			}
		});

		obj.assigToId = selectSubmitAssignToIdTree;

		state.loading = true;

		cmCategoryApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.saveSuccess'));
				emit('fetchData');
				closeDialog();
			})
			.catch((rs) => {

				ElMessage.error(rs.resultMsg || rs.toString());
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

// 通过check的回调里面获取节点id,再获取节点的node对象
const checkChange = (data: any) => {
	//根据key拿到Tree组件中的node节点
	const node = localCompanyTree.value.getNode(data.value);
	//调用节点处理方法
	setNode(node);
};

//递归设置子节点和父节点
const setNode = (node: any) => {
	if (node.checked) {
		if (node.level == 1) {
			setChildNode(node);
		} else if (node.level == 2) {
			setParentNode(node);
		}
	}
};

//递归设置父节点全部取消选中
const setParentNode = (node: any) => {
	if (node.parent) {
		for (const key in node) {
			if (key === 'parent') {
				node[key].checked = false;
				//递归调用相应父节点处理函数
				setParentNode(node[key]);
			}
		}
	}
};

//递归设置子节点全部取消选中
const setChildNode = (node: any) => {
	if (node.childNodes && node.childNodes.length) {
		node.childNodes.forEach((item: any) => {
			item.checked = false;

			//递归调用相应子节点处理函数
			setChildNode(item);
		});
	}
};

watch(
	() => state.ruleForm.category_Code,
	() => {
		const categorytdis = [];
		state.CategoryData.map((items) => {
			if (items.itemValue == state.ruleForm.category_Code) {
				categorytdis.push(items.description);
			}
		});
		state.ruleForm.category_Description = categorytdis[0];
	},
	{ immediate: false }
);

defineExpose({
	openDialog,
});

</script>

<style scoped>
.button-tree-container {
	position: relative;
	width: 100%;
}

.check-all-button {
	position: absolute !important;
	right: 0 !important;
	top: -28px !important;
	z-index: 1 !important;
	margin-right: 10px !important;
}

.custom-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.custom-option span {
	flex-grow: 1;
	/* 根据需要调整左边距 */
}
</style>