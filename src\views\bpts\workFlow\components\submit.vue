﻿<template>
	<div class="workflow-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="509px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="90px">
				<el-form-item label="审批结果" prop="msg" v-if="ruleForm.executionType === 'Approved' && false">
					<el-radio-group v-model="ruleForm.executionType">
						<el-radio label="Approved" size="large">Approved</el-radio>
						<el-radio label="Rejected" size="large">Rejected</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="选择分支" v-if="ruleForm.executionType === 'Approved' && forks.length > 0">
					<el-select v-model="ruleForm.branch" placeholder="Select" filterable clearable class="w100" @change="onActionChange">
						<el-option v-for="item in forks" :label="item" :value="item" :key="item"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="选择操作" v-if="ruleForm.executionType === 'Approved' && actions.length > 0">
					<el-select v-model="ruleForm.action" placeholder="Select" filterable clearable class="w100" @change="onActionChange">
						<el-option v-for="item in actions" :label="item" :value="item" :key="item"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="审批人" v-if="ruleForm.executionType === 'Approved' && isApproverSelect && approvers.length > 0">
					<el-select v-model="ruleForm.approvers" placeholder="Select" filterable multiple class="w100">
						<el-option v-for="item in approvers" :label="item.name" :value="item.userId" :key="item.userId"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="退回节点" v-if="ruleForm.executionType === 'Returned' && manualActivitys.length > 1">
					<el-select v-model="ruleForm.revokeOrReturnActivityId" placeholder="Select" filterable clearable class="w100">
						<el-option v-for="item in manualActivitys" :label="item.name" :value="item.id" :key="item.id"></el-option>
					</el-select>
				</el-form-item>
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="审批意见" prop="msg">
							<el-input v-model="ruleForm.msg" type="textarea" placeholder="请输入审批意见" maxlength="150" rows="6"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" size="small" @click="onSubmit">Submit</el-button>
					<el-button type="danger" size="small" @click="onCancel">Close</el-button>
				</span>
			</template>
		</el-dialog>
		<SelectUser ref="selectUserRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref, computed } from 'vue';
// import { useStore } from '/@/store/index';
import { WorkflowActions, ElsaActivityType } from '/@/types/workflow';
import { ElMessageBox, ElMessage } from 'element-plus';
import SelectUser from '/@/views/bpts/user/components/SelectUser.vue';
import workflowCrmApi from '/@/api/workflowCrm/index';
import { isNullOrWhiteSpace } from '/@/utils';
import { WorkflowExecutionDto } from '/@/types/models';

export default defineComponent({
	name: 'workflowAction',
	components: { SelectUser },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const selectUserRef = ref();
		const userRef = ref();
		// const store = useStore();
		const state = reactive({
			title: 'Execution Workflow',
			closedLoading: false,
			isShowClosed: false,
			isShowReject: false,
			isShowReady: false,
			isShowCancelled: false,
			isShowDialog: false,
			isApproverSelect: false,
			readytoAuditLoading: false,
			rejectLoading: false,
			cancelledLoading: false,
			saveLoading: false,
			deleteLoading: false,
			ruleForm: {} as WorkflowExecutionDto,
			rules: {
				msg: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
			},
			userDataSelect: [] as any, //已选择的主管
			userData: [], //部门主管 数据源
			permissions: [],
			forks: [], //分支
			forkSelect: { signalReceived: '' }, //已选择的分支
			manualActivitys: [],
			actions: [],
			approvers: [],
			lastActivity: {} as any,
		});

		// 获取用户信息 vuex
		// const currentUser = computed(() => {
		// 	return store.state.userInfos.userInfos;
		// });

		// 打开弹窗
		const openDialog = (crmWorkflowId: any, elsaInstancesId: string, permissions: any, action: string, formData: any, lastActivity: any) => {
			state.isShowDialog = true;
			state.rejectLoading = true;
			state.saveLoading = true;
			state.isApproverSelect = false;
			state.ruleForm.approvers = [];
			state.ruleForm.msg = '';
			state.ruleForm.action = '';
			state.ruleForm.crmWorkflowId = crmWorkflowId;
			state.ruleForm.elsaInstancesId = elsaInstancesId;
			state.permissions = permissions;
			state.ruleForm.executionType = action;
			state.ruleForm.Data = formData;
			state.lastActivity = lastActivity;

			state.title = action;

			console.log('lastActivity', lastActivity);

			if (lastActivity.forks.length > 0) {
				if (lastActivity.forkType === 'Action') {
					state.actions = lastActivity.forks.map((a) => {
						return a.name;
					});
				} else if (lastActivity.forkType === 'Branch') {
					state.forks = lastActivity.forks.map((a) => {
						return a.name;
					});
				} else {
					state.approvers = lastActivity.forks[0].approvers;
					state.isApproverSelect = lastActivity.forks[0].isApproverSelect;
					if (state.approvers.length == 1) {
						//如果只有一个人，就没有必要选择了
						state.ruleForm.approvers.push(state.approvers[0].userId);
					}
				}
			}

			if (state.ruleForm.executionType === WorkflowActions.RETURN) {
				const permission = permissions.find((item: { permissionType: string }) => {
					return item.permissionType === WorkflowActions.RETURN;
				});

				const req = {
					workflowInstanceId: state.ruleForm.elsaInstancesId,
					activityId: permission.activityId,
					taskId: permission.taskId,
				};
				console.log('req', permission);
				workflowCrmApi.GetReturnActivitys(req).then((rs) => {
					state.manualActivitys = rs.data;
					if (state.manualActivitys.length == 1) {
						//如果只有一条，就不用显示下拉框，直接赋值
						state.ruleForm.revokeOrReturnActivityId = state.manualActivitys[0].id;
					}
				});
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var req = state.ruleForm;
				var permission = state.permissions.find((item: any) => {
					return item.permissionType === req.executionType;
				});

				req.activityId = permission.activityId;
				req.trigger = state.forkSelect.signalReceived;
				req.taskId = permission.taskId;
				console.log('req', req);
				workflowCrmApi.Workflowexecution(req).then((rs) => {
					context.emit('fetchData');
					closeDialog();
				});
			});
		};
		const onVisible = () => {
			userRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
			selectUserRef.value.openDialog({ multiple: false });
		};
		const setSelectItem = (items: any) => {
			const newItem = [...items]; //复制数组
			state.userData = items;
			state.userDataSelect = newItem.map((a) => {
				return a.itemId;
			});
		};

		const onActionChange = (action: any) => {
			var obj = state.lastActivity.forks.find((a) => a.name === action);
			state.forkSelect = obj;
			state.isApproverSelect = obj.isApproverSelect;
			state.approvers = obj.approvers;
		};

		return {
			selectUserRef,
			userRef,
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onVisible,
			setSelectItem,
			// currentUser,
			onActionChange,
			...toRefs(state),
		};
	},
});
</script>
