<template>
	<el-container class="userDelegateAudit-index-container tablelist" style="height: 100%" v-if="isShowDialog">
		<el-dialog width="90%" v-model="isShowDialog" :close-on-click-modal="false" draggable :destroy-on-close="true">
			<el-main class="nopadding" ref="printMain" style="height: 100%">
				<div class="scTable" style="height: 600px" ref="scTableMain">
					<TicketList :disalogList="true"></TicketList>
				</div>
			</el-main>
		</el-dialog>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, ref, defineComponent, getCurrentInstance, onMounted } from 'vue';

import TicketList from '/@/views/bpts/ticket/list.vue';
import mittBus from '/@/utils/mitt';

export default defineComponent({
	name: 'ticketListDialog',
	components: { TicketList },
	setup() {
		const { proxy } = getCurrentInstance() as any;

		const state = reactive({
			isShowDialog: false,
		});

		const openDialog = () => {
			state.isShowDialog = true;
		};

		onMounted(async () => {
			//事件监听器
			mittBus.on('HideDialogList', () => {
				state.isShowDialog = false;
			});
		});

		return {
			openDialog,
			...toRefs(state),
		};
	},
});
</script>
