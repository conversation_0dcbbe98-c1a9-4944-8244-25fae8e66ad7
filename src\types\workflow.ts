
//流程操作类型
export const WorkflowActions = {
	APPROVE: 'Approved', //审批
	REJECTED: 'Rejected', //拒绝
	REMIND: 'Escalate', //催办
	REVOKE: 'Revoked', //撤回
	RETURN: 'Returned', //退回
	CANCEL: 'Cancelled', //取消
	RESUBMIT: 'Resubmit', //重新提交
	ASSIGNTO: 'AssignTo', //转办
	AUTO_PASS: 'AutoPass', //自动通过
};

//工作流的活动类型
export const ElsaActivityType = {
	START: 'StartActivity',
	APPROVE: 'Approved',
	FINISH: 'Finish',
};

export const LogicFlowNodeType = {
	StartNode: 'StartNode',
	ApprovalNode: 'ApprovalNode',
	JugementNode: 'JugementNode',
	EndNode: 'EndNode',
	ForkNode: 'ForkNode',
	JoinNode: 'JoinNode',
	UserTask: 'UserTask',
};