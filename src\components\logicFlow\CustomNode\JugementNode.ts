import { PolygonNode, PolygonNodeModel, h } from '@logicflow/core';
import { v4 as uuidv4 } from 'uuid';
const NODE_COLOR = '#9932CC';
class JugementNodeView extends PolygonNode {
	getIconShape() {}
	getLabelShape() {
		const { model } = this.props;
		const { x, y, height, width } = model;
		return h(
			'text',
			{
				fill: '#000000',
				fontSize: 12,
				x: width / 2 - 10,
				y: height / 2 + 5,
				width: 50,
				height: 25,
			},
			'判断'
		);
	}
	getShape() {
		const { model } = this.props;
		const { width, height, x, y, points } = model;
		const { fill, fillOpacity, strokeWidth, stroke, strokeOpacity } = model.getNodeStyle();
		const transform = `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`;
		const pointsPath = points.map((point) => point.join(',')).join(' ');
		return h(
			'g',
			{
				transform,
			},
			[
				h('polygon', {
					points: pointsPath,
					fill,
					stroke,
					strokeWidth,
					strokeOpacity,
					fillOpacity,
				}),
				this.getIconShape(),
				this.getLabelShape(), // 调用 getLabelShape 方法并将其返回的结果添加到渲染列表中
			]
		);
	}
}
class JugementNodeModel extends PolygonNodeModel {
	constructor(data, graphModel) {
		super(data, graphModel);
	}
	initNodeData(data) {
		data.text = {
			value: (data.text && data.text.value) || '',
			x: data.x,
			y: data.y + this.height / 2 + 20, // 将文本的 y 坐标设置为形状下方
			dragable: false,
			editable: true,
		};
		if (!data.properties) {
			data.properties = {
				name: this.findNodeByName(),
				entity: data.entity,
				conditionList: data.conditionList,
			};
		}
		super.initNodeData(data);
	}

	getNodeStyle() {
		const style = super.getNodeStyle();
		style.stroke = NODE_COLOR;
		return style;
	}
	getTextStyle() {
		const textStyle = super.getTextStyle();
		const { height, y } = this;
		textStyle.y = y + height / 2 + 20; // 将文本的 y 坐标设置为形状下方
		return textStyle;
	}
	findNodeByName() {
		const nodes = this.graphModel.nodes;
		const names = nodes.map((node) => node.properties.name).sort(); // 将所有name从小到大排序
		const lastNodeName = names.pop(); // 取出最后一个name
		const lastNodeNumber = lastNodeName ? parseInt(lastNodeName.replace(/^\D+/g, '')) : 0; // 提取数字部分并转换为数字
		const nextNodeNumber = lastNodeNumber + 1; // 计算下一个数字
		const nextNodeName = `node${nextNodeNumber}`; // 拼接下一个name
		return nextNodeName;
	}
}

export default {
	type: 'JugementNode',
	view: JugementNodeView,
	model: JugementNodeModel,
};
