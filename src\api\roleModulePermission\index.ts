﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class roleModulePermissionApi extends BaseApi {
	GetPermissionIdByRoleId(roleId: any) {
		return request({
			url: '/Api/RoleModulePermission/GetPermissionIdByRoleId/' + roleId,
			method: 'get',
			params: {},
		});
	}

	GetPermissionList(roleId: number, menuType: number) {
		return request({
			url: '/Api/RoleModulePermission/GetPermissionList',
			method: 'get',
			params: {
				roleId,
				menuType,
			},
		});
	}

	Assign(data: any) {
		return request({
			url: this.baseurl + 'Assign',
			method: 'post',
			data,
		});
	}
}

export default new roleModulePermissionApi('/api/roleModulePermission/', 'id');
