/**
 * 2020.11.29 lyt 整理
 * 工具类集合，适用于平时开发
 * 新增多行注释信息，鼠标放到方法名即可查看
 */

/**
 * 验证百分比（不可以小数）
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyNumberPercentage(val: string): string {
	// 匹配空格
	let v = val.replace(/(^\s*)|(\s*$)/g, '');
	// 只能是数字和小数点，不能是其他输入
	v = v.replace(/[^\d]/g, '');
	// 不能以0开始
	v = v.replace(/^0/g, '');
	// 数字超过100，赋值成最大值100
	v = v.replace(/^[1-9]\d\d{1,3}$/, '100');
	// 返回结果
	return v;
}

/**
 * 验证百分比（可以小数）
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyNumberPercentageFloat(val: string): string {
	let v = verifyNumberIntegerAndFloat(val);
	// 数字超过100，赋值成最大值100
	v = v.replace(/^[1-9]\d\d{1,3}$/, '100');
	// 超过100之后不给再输入值
	v = v.replace(/^100\.$/, '100');
	// 返回结果
	return v;
}

/**
 * 小数或整数(不可以负数)
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyNumberIntegerAndFloat(val: string) {
	// 匹配空格
	let v = val.replace(/(^\s*)|(\s*$)/g, '');
	// 只能是数字和小数点，不能是其他输入
	v = v.replace(/[^\d.]/g, '');
	// 以0开始只能输入一个
	v = v.replace(/^0{2}$/g, '0');
	// 保证第一位只能是数字，不能是点
	v = v.replace(/^\./g, '');
	// 小数只能出现1位
	v = v.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
	// 小数点后面保留2位
	v = v.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
	// 返回结果
	return v;
}

/**
 * 正整数验证
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifiyNumberInteger(val: string) {
	// 匹配空格
	let v = val.replace(/(^\s*)|(\s*$)/g, '');
	// 去掉 '.' , 防止贴贴的时候出现问题 如 0.1.12.12
	v = v.replace(/[\.]*/g, '');
	// 去掉以 0 开始后面的数, 防止贴贴的时候出现问题 如 00121323
	v = v.replace(/(^0[\d]*)$/g, '0');
	// 首位是0,只能出现一次
	v = v.replace(/^0\d$/g, '0');
	// 只匹配数字
	v = v.replace(/[^\d]/g, '');
	// 返回结果
	return v;
}

/**
 * 去掉中文及空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyCnAndSpace(val: string) {
	// 匹配中文与空格
	let v = val.replace(/[\u4e00-\u9fa5\s]+/g, '');
	// 匹配空格
	v = v.replace(/(^\s*)|(\s*$)/g, '');
	// 返回结果
	return v;
}

/**
 * 去掉英文及空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyEnAndSpace(val: string) {
	// 匹配英文与空格
	let v = val.replace(/[a-zA-Z]+/g, '');
	// 匹配空格
	v = v.replace(/(^\s*)|(\s*$)/g, '');
	// 返回结果
	return v;
}

/**
 * 禁止输入空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyAndSpace(val: string) {
	// 匹配空格
	let v = val.replace(/(^\s*)|(\s*$)/g, '');
	// 返回结果
	return v;
}

/**
 * 金额用 `,` 区分开
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyNumberComma(val: string) {
	// 调用小数或整数(不可以负数)方法
	let v: any = verifyNumberIntegerAndFloat(val);
	// 字符串转成数组
	v = v.toString().split('.');
	// \B 匹配非单词边界，两边都是单词字符或者两边都是非单词字符
	v[0] = v[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	// 数组转字符串
	v = v.join('.');
	// 返回结果
	return v;
}

/**
 * 匹配文字变色（搜索时）
 * @param val 当前值字符串
 * @param text 要处理的字符串值
 * @param color 搜索到时字体高亮颜色
 * @returns 返回处理后的字符串
 */
export function verifyTextColor(val: string, text = '', color = 'red') {
	// 返回内容，添加颜色
	let v = text.replace(new RegExp(val, 'gi'), `<span style='color: ${color}'>${val}</span>`);
	// 返回结果
	return v;
}

/**
 * 数字转中文大写
 * @param val 当前值字符串
 * @param unit 默认：仟佰拾亿仟佰拾万仟佰拾元角分
 * @returns 返回处理后的字符串
 */
export function verifyNumberCnUppercase(val: any, unit = '仟佰拾亿仟佰拾万仟佰拾元角分', v = '') {
	// 当前内容字符串添加 2个0，为什么??
	val += '00';
	// 返回某个指定的字符串值在字符串中首次出现的位置，没有出现，则该方法返回 -1
	let lookup = val.indexOf('.');
	// substring：不包含结束下标内容，substr：包含结束下标内容
	if (lookup >= 0) val = val.substring(0, lookup) + val.substr(lookup + 1, 2);
	// 根据内容 val 的长度，截取返回对应大写
	unit = unit.substr(unit.length - val.length);
	// 循环截取拼接大写
	for (let i = 0; i < val.length; i++) {
		v += '零壹贰叁肆伍陆柒捌玖'.substr(val.substr(i, 1), 1) + unit.substr(i, 1);
	}
	// 正则处理
	v = v
		.replace(/零角零分$/, '整')
		.replace(/零[仟佰拾]/g, '零')
		.replace(/零{2,}/g, '零')
		.replace(/零([亿|万])/g, '$1')
		.replace(/零+元/, '元')
		.replace(/亿零{0,3}万/, '亿')
		.replace(/^元/, '零元');
	// 返回结果
	return v;
}

/**
 * 手机号码
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyPhone(val: string) {
	// false: 手机号码不正确
	if (!/^((12[0-9])|(13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\d{8}$/.test(val)) return false;
	// true: 手机号码正确
	else return true;
}

/**
 * 国内电话号码
 * @param val 当前值字符串
 * @returns 返回 true: 国内电话号码正确
 */
export function verifyTelPhone(val: string) {
	// false: 国内电话号码不正确
	if (!/\d{3}-\d{8}|\d{4}-\d{7}/.test(val)) return false;
	// true: 国内电话号码正确
	else return true;
}

/**
 * 登录账号 (字母开头，允许5-16字节，允许字母数字下划线)
 * @param val 当前值字符串
 * @returns 返回 true: 登录账号正确
 */
export function verifyAccount(val: string) {
	// false: 登录账号不正确
	if (!/^[a-zA-Z][a-zA-Z0-9_]{4,15}$/.test(val)) return false;
	// true: 登录账号正确
	else return true;
}

/**
 * 密码 (以字母开头，长度在6~16之间，只能包含字母、数字和下划线)
 * @param val 当前值字符串
 * @returns 返回 true: 密码正确
 */
export function verifyPassword(val: string) {
	// false: 密码不正确
	if (!/^[a-zA-Z]\w{5,15}$/.test(val)) return false;
	// true: 密码正确
	else return true;
}

/**
 * 强密码 (字母+数字+特殊字符，长度在6-16之间)
 * @param val 当前值字符串
 * @returns 返回 true: 强密码正确
 */
export function verifyPasswordPowerful(val: string) {
	// 定义字符类型（避免过于宽泛的字符范围）
	const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
	const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const digits = '**********';
	const specialChars = '!@#$%^&.*';

	// 检查长度
	if (val.length < 6 || val.length > 16) {
		return false;
	}

	// 检查是否包含所有字符类型
	let hasLower = false;
	let hasUpper = false;
	let hasDigit = false;
	let hasSpecial = false;

	for (const char of val) {
		if (lowerCase.includes(char)) {
			hasLower = true;
		} else if (upperCase.includes(char)) {
			hasUpper = true;
		} else if (digits.includes(char)) {
			hasDigit = true;
		} else if (specialChars.includes(char)) {
			hasSpecial = true;
		} else {
			// 包含不允许的字符
			return false;
		}
	}

	// 必须包含字母、数字和特殊字符
	const hasLetter = hasLower || hasUpper;
	return hasLetter && hasDigit && hasSpecial;
}

/**
 * 密码强度
 * @param val 当前值字符串
 * @description 弱：纯数字，纯字母，纯特殊字符
 * @description 中：字母+数字，字母+特殊字符，数字+特殊字符
 * @description 强：字母+数字+特殊字符
 * @returns 返回处理后的字符串：弱、中、强
 */
export function verifyPasswordStrength(val: string) {
	// 定义字符类型（避免过于宽泛的字符范围）
	const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
	const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const digits = '**********';
	const specialChars = '!@#$%^&.*';

	// 检查长度
	if (val.length < 6 || val.length > 16) {
		return '';
	}

	// 检查字符类型
	let hasLower = false;
	let hasUpper = false;
	let hasDigit = false;
	let hasSpecial = false;

	for (const char of val) {
		if (lowerCase.includes(char)) {
			hasLower = true;
		} else if (upperCase.includes(char)) {
			hasUpper = true;
		} else if (digits.includes(char)) {
			hasDigit = true;
		} else if (specialChars.includes(char)) {
			hasSpecial = true;
		} else {
			// 包含不允许的字符，返回空字符串
			return '';
		}
	}

	const hasLetter = hasLower || hasUpper;
	const typeCount = (hasLetter ? 1 : 0) + (hasDigit ? 1 : 0) + (hasSpecial ? 1 : 0);

	// 强：字母+数字+特殊字符
	if (hasLetter && hasDigit && hasSpecial) {
		return '强';
	}
	// 中：两种类型组合
	if (typeCount === 2) {
		return '中';
	}
	// 弱：单一类型
	if (typeCount === 1) {
		return '弱';
	}

	// 返回空字符串（不符合任何强度要求）
	return '';
}

/**
 * IP地址
 * @param val 当前值字符串
 * @returns 返回 true: IP地址正确
 */
export function verifyIPAddress(val: string) {
	// false: IP地址不正确
	if (
		!/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(
			val
		)
	)
		return false;
	// true: IP地址正确
	else return true;
}

/**
 * 邮箱
 * @param val 当前值字符串
 * @returns 返回 true: 邮箱正确
 */
export function verifyEmail(val: string) {
	console.log(val);
	// false: 邮箱不正确
	if (
		!/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
			val
		)
	)
		return false;
	// true: 邮箱正确
	else return true;
}

export function checkEmail(rule: any, value: any, cb: any) {
	//验证邮箱的正则表达式
	const regEmail = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
	if (regEmail.test(value)) {
		//合法的邮箱
		return true;
	}
	return false;
}

/**
 * 身份证
 * @param val 当前值字符串
 * @returns 返回 true: 身份证正确
 */
export function verifyIdCard(val: string) {
	// false: 身份证不正确
	if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(val)) return false;
	// true: 身份证正确
	else return true;
}

/**
 * 姓名
 * @param val 当前值字符串
 * @returns 返回 true: 姓名正确
 */
export function verifyFullName(val: string) {
	// false: 姓名不正确
	if (!/^[\u4e00-\u9fa5]{1,6}(·[\u4e00-\u9fa5]{1,6}){0,2}$/.test(val)) return false;
	// true: 姓名正确
	else return true;
}

/**
 * 邮政编码
 * @param val 当前值字符串
 * @returns 返回 true: 邮政编码正确
 */
export function verifyPostalCode(val: string) {
	// false: 邮政编码不正确
	if (!/^[1-9][0-9]{5}$/.test(val)) return false;
	// true: 邮政编码正确
	else return true;
}

/**
 * 检查是否为IP地址
 * @param hostname 主机名
 * @returns 是否为IP地址
 */
function isIPAddress(hostname: string): boolean {
	const ipPattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
	return ipPattern.test(hostname);
}

/**
 * 验证公网IP地址范围（与原始正则逻辑保持一致）
 * @param ip IP地址
 * @returns 是否为有效的公网IP
 */
function isValidPublicIP(ip: string): boolean {
	const parts = ip.split('.').map(Number);
	if (parts.length !== 4) return false;

	// 第一个八位组：1-223（排除0和224-255）
	if (parts[0] < 1 || parts[0] > 223) return false;

	// 第二、三个八位组：0-255
	if (parts[1] < 0 || parts[1] > 255 || parts[2] < 0 || parts[2] > 255) return false;

	// 第四个八位组：1-254（排除0和255）
	if (parts[3] < 1 || parts[3] > 254) return false;

	return true;
}

/**
 * url 处理
 * @param val 当前值字符串
 * @returns 返回 true: url 正确
 */
export function verifyUrl(val: string): boolean {
	// 基础检查：空值或非字符串
	if (!val || typeof val !== 'string') {
		return false;
	}

	// 长度检查：防止过长URL导致性能问题
	if (val.length > 2048) {
		return false;
	}

	// 处理可选协议的情况（原始正则支持省略协议）
	let urlToValidate = val;

	// 检查是否以 // 开头（原始正则不支持仅双斜杠）
	if (val.startsWith('//')) {
		return false;
	}

	// 如果没有协议且不是明显的非URL格式，默认添加 http://
	if (!/^[a-zA-Z][a-zA-Z\d+\-.]*:/.test(val)) {
		// 基本域名格式检查，避免将明显的非URL当作域名
		if (!/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\./.test(val) && !/^[a-zA-Z0-9]+$/.test(val)) {
			return false;
		}
		urlToValidate = 'http://' + val;
	}

	// 基础格式检查：使用简单正则避免回溯
	const basicUrlPattern = /^(https?|ftp):\/\/.+/i;
	if (!basicUrlPattern.test(urlToValidate)) {
		return false;
	}

	// 使用原生URL API进行严格验证
	try {
		const url = new URL(urlToValidate);

		// 协议检查：允许http、https和ftp（与原始正则保持一致）
		if (!['http:', 'https:', 'ftp:'].includes(url.protocol)) {
			return false;
		}

		// 主机名检查：不能为空且长度合理
		if (!url.hostname || url.hostname.length > 253) {
			return false;
		}

		// 域名标签长度检查：每个标签不能超过63个字符
		const domainLabels = url.hostname.split('.');
		if (domainLabels.some(label => label.length > 63)) {
			return false;
		}

		// 端口检查：如果存在端口，必须是2-5位有效数字（与原始正则保持一致）
		if (url.port) {
			const portNum = Number(url.port);
			if (isNaN(portNum) || portNum < 10 || portNum > 99999 || url.port.length < 2 || url.port.length > 5) {
				return false;
			}
		}

		// IP地址验证（与原始正则逻辑保持一致）
		if (isIPAddress(url.hostname)) {
			// 私有IP地址检查
			const privateIpPattern = /^(10\.|127\.|169\.254\.|192\.168\.|172\.(1[6-9]|2\d|3[01])\.)/;
			if (privateIpPattern.test(url.hostname)) {
				return false;
			}

			// 公网IP范围检查（与原始正则保持一致）
			if (!isValidPublicIP(url.hostname)) {
				return false;
			}
		}

		return true;
	} catch {
		// URL构造失败，说明格式不正确
		return false;
	}
}

/**
 * 车牌号
 * @param val 当前值字符串
 * @returns 返回 true：车牌号正确
 */
export function verifyCarNum(val: string) {
	// false: 车牌号不正确
	if (
		!/^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/.test(
			val
		)
	)
		return false;
	// true：车牌号正确
	else return true;
}
