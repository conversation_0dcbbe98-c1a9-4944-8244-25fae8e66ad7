export const nodeList = [
	{
		text: '流程发起',
		type: 'StartNode',
		class: 'node-start',
	},
	{
		text: '审批节点',
		type: 'ApprovalNode',
		class: 'node-rect',
	},
	{
		text: '用户交互',
		type: 'UserTask',
		class: 'node-user',
	},
	{
		text: '条件判断',
		type: 'JugementNode',
		class: 'node-push',
	},
	{
		text: '分支',
		type: 'Fork',
		class: 'node-fork',
	},
	{
		text: '合并',
		type: 'Join',
		class: 'node-join',
	},
	{
		text: '结束',
		type: 'EndNode',
		class: 'node-end',
	},
];

export const BpmnNode = [
	{
		type: 'bpmn:startEvent',
		text: '开始',
		class: 'bpmn-start',
	},
	{
		type: 'bpmn:endEvent',
		text: '结束',
		class: 'bpmn-end',
	},
	{
		type: 'bpmn:exclusiveGateway',
		text: '网关',
		class: 'bpmn-exclusiveGateway',
	},
	{
		type: 'bpmn:userTask',
		text: '用户',
		class: 'bpmn-user',
	},
];

export const permissions = [
	{ permissionType: 'Approved', permissionTypeName: '同意', enable: true, permissionText: '同意' },
	{ permissionType: 'AssignTo', permissionTypeName: '转办', enable: true, permissionText: '转办' },
	{ permissionType: 'Revoked', permissionTypeName: '撤回', enable: true, permissionText: '撤回' },
	{ permissionType: 'Returned', permissionTypeName: '退回', enable: true, permissionText: '退回' },
	{ permissionType: 'Resubmit', permissionTypeName: '提交', enable: true, permissionText: '重新提交' },
	{ permissionType: 'Escalate', permissionTypeName: '催办', enable: true, permissionText: '催办' },
	{ permissionType: 'Cancelled', permissionTypeName: '取消', enable: true, permissionText: '取消' },
];

export const emailNotices = [
	{ label: '节点同意', noticeType: 'Approve' },
	{ label: '节点拒绝', noticeType: 'Reject' },
	{ label: '节点退回', noticeType: 'Return' },
	{ label: '节点撤回', noticeType: 'Revoked' },
	{ label: '节点转审', noticeType: 'AssignTo' },
	{ label: '流程取消', noticeType: 'Cancell' },
	{ label: '流程催办', noticeType: 'Escalate' },
	{ label: '流程待办', noticeType: 'Todo' },
	{ label: '流程结束', noticeType: 'Finish' },
];
