﻿<template>
	<div class="dic-tree-container dic-index-tree">
		<div class="dic-tree-left">
			<el-card style="height: 100%">
				<template #header>
					<el-row class="mb-4">
						<el-button v-auth="'systemDict.Create'" type="success" round size="small" @click="onCreateType">
							{{ $t('message.page.buttonCreate') }}
						</el-button>
						<el-button v-auth="'systemDict.Edit'" type="primary" size="small" round :disabled="tableData.param.dictTypeId <= 0">
							{{ $t('message.page.buttonEdit') }}
						</el-button>
						<el-button
							v-auth="'systemDict.Delete'"
							type="danger"
							size="small"
							round
							@click="onDeleteDictType"
							:disabled="tableData.param.dictTypeId <= 0"
						>
							{{ $t('message.page.buttonDelete') }}
						</el-button>
					</el-row>
				</template>
				<el-menu
					:default-active="0"
					class="el-menu"
					active-text-color="#ffd04b"
					background-color="#545c64"
					text-color="#fff"
					@select="dictTypeSelect"
				>
					<el-menu-item :index="index" v-for="(item, index) in dictData" :key="item.dictId">
						<el-icon class="el-input__icon">
							<ele-Menu />
						</el-icon>
						<span>{{ item.name }}</span>
					</el-menu-item>
				</el-menu>
			</el-card>
		</div>
		<div class="dic-tree-right">
			<el-card style="height: 100%">
				<template #header>
					<div class="el-card-header" style="border-bottom: none; padding: 0px">
						<div class="left-panel">
							<el-button v-auth="'systemDict.Create'" size="small" type="success" class="ml10" @click="onOpenAddDic">
								{{ $t('message.page.buttonCreate') }}
							</el-button>
						</div>
						<div class="right-panel">
							<el-dropdown v-if="dctTypeName == 'Category'">
								<el-button v-auth="'ticketCategory.Export'" type="primary" size="small" class="ml10">
									<el-icon>
										<ele-ArrowDownBold />
									</el-icon>
									{{ $t('message.page.buttonExport') }}
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item @click="onExportAllRecord(0)">{{ $t('message.page.buttonExportEntireList') }}</el-dropdown-item>
										<el-dropdown-item @click="onExportAllRecord(1)">{{ $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
					</div>
				</template>
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:data="tableData.data"
							v-loading="tableData.loading"
							table-layout="fixed"
							height="calc(100%)"
							@row-dblclick="onDetail"
							@selection-change="selectionChange"
							stripe border
						>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />
							<el-table-column type="index" :label="$t('message.dictFields.serial')" width="80" />
							<el-table-column prop="name" :label="$t('message.dictFields.name')" width="150" show-overflow-tooltip></el-table-column>
							<el-table-column prop="value" :label="$t('message.dictFields.value')" width="150" show-overflow-tooltip></el-table-column>
							<el-table-column prop="enable" :label="$t('message.dictFields.enable')" width="80" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.enable" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="!row.enable" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>
							<el-table-column prop="description" :label="$t('message.dictFields.description')" show-overflow-tooltip> </el-table-column>
							<el-table-column prop="createdAt" :label="$t('message.page.createAt')" show-overflow-tooltip>
								<template #default="scope">
									<span>{{ scope.row.createdAt }}</span>
								</template>
							</el-table-column>
							<el-table-column prop="sort" :label="$t('message.page.sort')" show-overflow-tooltip>
								<template #default="scope">
									<span>{{ scope.row.sort }}</span>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.page.Action')" width="170">
								<template #default="scope">
									<el-button v-auth="'systemDict.Edit'" size="small" type="primary" @click="onOpenEditDic(scope.row)">
										{{ $t('message.page.actionsEdit') }}
									</el-button>
									<el-button v-auth="'systemDict.Delete'" size="small" type="danger" @click="onRowDel(scope.row)">
										{{ $t('message.page.actionsDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination
							@size-change="onSizechange"
							@current-change="onCurrentChange"
							:pager-count="5"
							:page-sizes="[10, 20, 30]"
							v-model:current-page="tableData.param.pageIndex"
							background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="tableData.total"
							small
						>
						</el-pagination>
					</div>
				</div>
			</el-card>
		</div>
		<CreateOrEdit ref="createOrEditRef" @fetchData="onSearch" />
		<EditDicType ref="editDicTypeRef" @fetchData="getDictList" />
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import CreateOrEdit from '/@/views/bpts/dict/component/createOrEdit.vue';
import EditDicType from './component/editDicType.vue';
import { useI18n } from 'vue-i18n';

import dictApi from '/@/api/dict/index';
import dictItemApi from '/@/api/dictItem/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import { formatDateTime } from '/@/utils/formatTime';

// 定义接口来定义对象的类型
interface DictItemInfo {
	itemId: 0; //
	dictId: 0; //
	parentId: 0; //
	name: ''; //
	value: ''; //
	sort: 0; //
	enable: false; //
	description: ''; //
}

interface TableDataState {
	tableData: {
		data: Array<DictItemInfo>;
		total: number;
		loading: boolean;
		selection: [];
		param: {
			searchType: string;
			searchKey: string;
			pageNum: number;
			pageSize: number;
			dictTypeId: number;
			dictName: string;
			dictDescr: string;
			ids: Array<number>;
			ischeckType: number;
			order: string;
			sort: string;
		};
	};
	dictData: object;
	dictIndex: number;
	requestDetailData: [];
	dctTypeName: string;
}

export default defineComponent({
	name: 'systemDict',
	components: { CreateOrEdit, EditDicType },
	setup() {
		const { t } = useI18n();
		const createOrEditRef = ref();
		const editDicTypeRef = ref();
		const state = reactive<TableDataState>({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					searchType: '',
					searchKey: '',
					pageNum: 1,
					pageSize: 15,
					dictTypeId: 0,
					dictName: '',
					dictDescr: '',
					ids: [],
					ischeckType: 0,
					order: 'name',
					sort: 'asc',
				},
			},
			dictData: [],
			dictIndex: -1,
			requestDetailData: [],
			dctTypeName: '',
		});

		// 初始化表格数据
		const initTableData = (dictTypeId?: any) => {
			dictItemApi.Query(state.tableData.param).then((rs) => {
				state.tableData.data = rs.data;
				state.tableData.total = rs.totalCount;
				state.dctTypeName = rs.data[0].dictTypeName;
			});
		};
		const onSearch = () => {
			initTableData();
		};

		const onSearchReSet = () => {
			var dictTypeId = state.tableData.param.dictTypeId;
			initFormData();
			//initTableData();
			state.tableData.param.dictTypeId = dictTypeId;
			initTableData(state.tableData.param.dictTypeId);
		};
		const initFormData = () => {
			state.tableData.param = {
				searchType: '',
				searchKey: '',
				pageNum: 1,
				pageSize: 15,
				dictTypeId: 0,
				dictName: '',
				dictDescr: '',
				ids: [],
				ischeckType: 0,
				order: 'name',
				sort: 'asc',
			} as any;
		};

		// 打开新增字典弹窗
		const onOpenAddDic = () => {
			createOrEditRef.value.openDialog({
				action: 'Create',
				dictTypeId: state.tableData.param.dictTypeId,
				dctTypeName: state.dctTypeName,
			});
		};
		// 打开修改字典弹窗
		const onOpenEditDic = (row: DictItemInfo) => {
			createOrEditRef.value.openDialog({
				action: 'Edit',
				itemId: row.itemId,
				dctTypeName: state.dctTypeName,
			});
		};
		// 删除字典
		const onRowDel = (row: DictItemInfo) => {
			const params = {
				pageIndex: 1,
				pageSize: 15,
				searchKey: '',
				category_Code: row.value,
				category_Description: '',
				category_Status: '',
				category_Company: [],
				ids: [],
				ischeckType: 0,
			} as any;

			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				cmCategoryApi
					.Query(params)
					.then((rs: any) => {
						if (rs.totalCount > 0) {
							ElMessageBox.alert(
								t("You're deleting a record that has been applied to a Category / Ticket, please click 'Ok' to cancel this action."),
								t('message.page.dlgTip'),
								{
									confirmButtonText: t('message.page.buttonOk'),
									type: 'warning',
								}
							);
							return false;
						} else {
							dictItemApi
								.DeleteByKey(row.itemId)
								.then(() => {
									ElMessage.success('Succeed');
									initTableData(row.dictId);
								})
								.catch((rs) => {
									ElMessage.error(rs.ResultMsg || rs.toString());
								});
						}
					})
					.catch(() => {})
					.finally(() => (state.tableData.loading = false));
			});
		};
		// 分页改变
		const onHandleSizeChange = (val: number) => {
			state.tableData.param.pageSize = val;
			initTableData();
		};
		// 分页改变
		const onHandleCurrentChange = (val: number) => {
			state.tableData.param.pageNum = val;
			initTableData();
		};

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			initTableData();
		};

		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			initTableData();
		};

		const onCreateType = () => {
			editDicTypeRef.value.openDialog();
		};

		const dictTypeSelect = (index: any) => {
			var item = state.dictData[index];

			state.tableData.param.dictTypeId = item.dictId;
			initTableData(state.tableData.param.dictTypeId);
		};

		const onDeleteDictType = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				dictApi
					.DeleteByKey(state.tableData.param.dictTypeId)
					.then(() => {
						state.dictIndex = -1;
						getDictList();
						ElMessage.success('Succeeded');
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					});
			});
		};

		const getDictList = () => {
			dictApi.GetList({ order: 'name', sort: 'asc' }).then((rs) => {
				var arr = [
					{
						dictId: '0',
						name: 'All',
						enable: true,
					},
				];
				state.dictData = [...arr, ...rs.data];
			});
		};

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.param.pageSize;
			state.tableData.param.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;
				idjson = item.itemId;
				ids_arr.push(idjson);
			});
			state.tableData.param.ischeckType = selectAll;
			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}
			if (selectAll == 0) {
				state.tableData.param.ids = [];
			} else {
				state.tableData.param.ids = ids_arr;
			}

			dictItemApi
				.Export(state.tableData.param)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => {})
				.finally(() => (state.tableData.loading = false));

			state.tableData.param.pageSize = currPageSize;
		};

		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Category_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};
		// 页面加载时
		onMounted(() => {
			initTableData();
			getDictList();
		});
		return {
			createOrEditRef,
			editDicTypeRef,
			onSearch,
			onCurrentChange,
			onSizechange,
			onOpenAddDic,
			onOpenEditDic,
			onRowDel,
			onHandleSizeChange,
			onHandleCurrentChange,
			onCreateType,
			getDictList,
			onDeleteDictType,
			dictTypeSelect,
			...toRefs(state),
			onSearchReSet,
			onExportAllRecord,
			selectionChange,
		};
	},
});
</script>

<style scoped>
.aside {
	margin: 5px 1px 2px 5px;
	background-color: #fff;
}

.aside-right {
	margin: 5px 1px 2px 2px;
	background-color: #fff;
}

.dep-tree {
	margin-top: 10px;
}

.el-menu {
	width: 210px;
}
</style>

<style scoped lang="scss">
.dic-tree-container {
	padding: 10px;
	display: flex;
	height: 100%;
	box-sizing: border-box; /* 确保内边距包含在宽度内 */
	.dic-tree-left,
	.dic-tree-right {
		height: 100%;
	}
	.dic-tree-right {
		flex: 1;
		margin: 0px 10px;
	}

	.dic-tree-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px; /* 可根据需求调整 */
	}

	.dic-tree-header-title {
		font-size: 16px; /* 可根据需求调整 */
		font-weight: bold; /* 可根据需求调整 */
	}
}

@media (max-width: 768px) {
	.dic-tree-container {
		flex-direction: column;
		.el-menu {
			width: 100% !important;
		}
		.dic-tree-left {
			width: 100%; /* 在小屏幕上，左边部分占据整个宽度 */
			margin-bottom: 10px;
		}

		.dic-tree-right {
			margin-left: 0;
			width: 100%; /* 在小屏幕上，右边部分占据整个宽度 */
		}
	}
}
</style>
