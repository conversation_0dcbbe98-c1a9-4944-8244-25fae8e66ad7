﻿<template>
	<div class="workflow-edit-container">
		<el-dialog v-model="isShowDialog" :show-close="false" :fullscreen="true" :modal="false" class="full-dialog">
			<div class="full-dialog-header">
				<div class="header-title">
					<img src="/img/bpts.png" class="header-logo" />
					<p class="header-txt">{{ $t('message.workflow_labelArea.workflowDesign') }}</p>
				</div>
				<div class="steps">
					<el-steps simple :active="active">
						<el-step :title="$t('message.workflow_labelArea.information')"> </el-step>
						<el-step :title="$t('message.workflow_labelArea.workflowDesign')"> </el-step>
					</el-steps>
				</div>
				<div class="options">
					<el-button size="small" :disabled="active == 0" @click="onPrev">{{ $t('message.workflow_labelArea.back') }}</el-button>
					<el-button size="small" @click="onNext" :disabled="active == 1">{{ $t('message.workflow_labelArea.next') }}</el-button>
					<el-button size="small" type="primary" @click="onSubmit" :loading="saveLoading">{{ $t('message.workflow_labelArea.save') }}</el-button>
					<el-button size="small" @click="onCancel">{{ $t('message.workflow_labelArea.cancel') }}</el-button>
				</div>
			</div>
			<div class="bd">
				<div class="basic-box" v-if="active == 0">
					<div class="basicForm">
						<el-form ref="wfFormRef" :model="ruleForm" :rules="rules" label-width="150px">
							<el-form-item :label="$t('message.workflow_fieldArea.workflowName')" class="form-lable" prop="workflowName">
								<el-input v-model="ruleForm.workflowName" ></el-input>
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.workflowCode')" class="form-lable" prop="workflowCode">
								<el-input v-model="ruleForm.workflowCode" ></el-input>
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.type')" class="form-lable" prop="categoryId">
								<el-select v-model="ruleForm.categoryId" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100">
									<el-option v-for="item in workflowTypes" :label="item.itemName" :value="item.itemValue" :key="item.itemId"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.businessTable')" class="form-lable" prop="entity">
								<el-select v-model="ruleForm.entity" :placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100">
									<el-option v-for="item in entitys" :label="item.name" :value="item.entityClass" :key="item.entityClass"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.sort')" class="form-lable" prop="sorting">
								<el-input-number v-model="ruleForm.sorting" :min="0" :max="99" />
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.description')" class="form-lable">
								<el-input
									v-model="ruleForm.description"
									type="textarea"
									:placeholder="$t('message.workflow_placeholder.description')"
									rows="8"
									maxlength="150"
								></el-input>
							</el-form-item>
							<el-form-item :label="$t('message.workflow_fieldArea.flowChart')" class="form-lable">
								<sc-upload v-model="ruleForm.imageUrl" :title="$t('message.workflow_labelArea.clickImageToUpload')" icon="ele-Picture"></sc-upload>
							</el-form-item>
						</el-form>
					</div>
				</div>
				<div v-if="active == 1" class="designlf">
					<Designlf ref="designRef" :info="ruleForm"></Designlf>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import Designlf from './designlf.vue';
import dictItemApi from '/@/api/dictItem/index';
import workflowCrmApi from '/@/api/workflowCrm/index';
import { LogicFlowNodeType } from '/@/types/workflow';
import { formatDate } from '/@/utils/formatTime';
import { useI18n } from 'vue-i18n';

interface DialogParams {
	action: string;
	designId: string;
	row: object;
}

export default defineComponent({
	name: 'workflowCreateOrEdit',
	components: { Designlf },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			active: 0,
			isShowDialog: false,
			saveLoading: false,
			dialogParams: {
				action: '',
				designId: '',
			},
			ruleForm: {
				designId: '',
				workflowName: '',
				workflowCode: '',
				workflowDefinitionId: '',
				categoryId: '',
				entity: '',
				sorting: 0,
				description: '',
				imageUrl: '',
				data: '',
			},
			rules: {
				workflowName: [{ required: true, message: 'Please input', trigger: 'blur' }],
				workflowCode: [{ required: true, message: 'Please input', trigger: 'blur' }],
				categoryId: [{ required: true, message: 'Please select', trigger: 'change' }],
				entity: [{ required: true, message: 'Please select', trigger: 'change' }],
			},
			workflowTypes: [],
			entitys: [
				{ name: t('message.workflow_labelArea.invoiceInfo'), entityClass: 'InvoiceInfo' },
				{ name: t('message.workflow_labelArea.requireInfo'), entityClass: 'RequisitionsInfo' },
				{ name: t('message.workflow_labelArea.ticketInfo'), entityClass: 'CmTicketsInfo' },
			],
		});

		const designRef = ref();

		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			onInitForm();
			if (parmas.row) {
				state.ruleForm = parmas.row;
			}

			var dictArr = ['WorkflowType'];
			dictItemApi.Many(dictArr).then((rs) => {
				state.workflowTypes = rs.data?.find((a) => {
					return a.dictValue === 'WorkflowType';
				})?.items;
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
			state.active = 0;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			if (state.active == 1) {
				const json = designRef.value.catData();
				if (Object.keys(json || {}).length > 0) {
					state.ruleForm.data = JSON.stringify(json);
				}

				if (json.nodes?.findIndex((a) => a.type === LogicFlowNodeType.StartNode) < 0) {
					ElMessage.error(t('message.workflow_msgArea.missingStartNode'));
					return;
				}
			}
			var req = { ...state.ruleForm };
			state.saveLoading = true;
			workflowCrmApi
				.SaveWorkflowTemplate(req)
				.then((rs) => {
					if (!rs.data) {
						ElMessage.error(t('message.workflow_msgArea.fail'));
						return;
					}
					ElMessage.success(t('message.workflow_msgArea.success'));
					onCancel();
					context.emit('fetchData'); //关闭的时候刷新流程列表
				})
				.finally(() => {
					state.saveLoading = false;
				});
		};
		const onNext = () => {
			proxy.$refs.wfFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				state.active = 1;
			});
		};
		const onPrev = () => {
			var json = designRef.value.catData();
			state.ruleForm.data = JSON.stringify(json);
			state.active = 0;
		};

		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				designId: '',
				workflowName: '',
				workflowCode: formatDate(new Date(), 'YYYYmmddHHMMSSS'),
				workflowDefinitionId: '',
				categoryId: '',
				entity: '',
				sorting: 0,
				description: '',
				imageUrl: '',
				data: '',
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onNext,
			onPrev,
			onInitForm,
			designRef,
			...toRefs(state),
		};
	},
});
</script>
