<template>
	<div class="dictItem-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Name" prop="Name">
							<el-input v-model="ruleForm.Name" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Value" prop="Value">
							<el-input v-model="ruleForm.Value" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Category" prop="DictIdArr">
							<el-cascader :options="dictData"
								:props="{ checkStrictly: true, value: 'DictId', label: 'Name' }" placeholder="select"
								clearable class="w100" v-model="ruleForm.DictIdArr">
								<template #default="{ node, data }">
									<span>{{ data.Name }}</span>
									<span v-if="!node.isLeaf"> ({{ data.Children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Sort" prop="Sort">
							<el-input-number v-model="ruleForm.Sort" :min="0" class="w100" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Enable" prop="Enable">
							<el-radio-group v-model="ruleForm.Enable">
								<el-radio :label="true">Enable</el-radio>
								<el-radio :label="false">Disable</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="Created At" prop="CreatedAt">
							<el-date-picker v-model="ruleForm.CreatedAt" type="date" placeholder="Pick a day"
								format="MM/DD/YYYY hh:mm" class="w100">
							</el-date-picker>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="Description" prop="Description">
							<el-input v-model="ruleForm.Msg" type="textarea" placeholder="请输入备注"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
					<el-button @click="onInitForm" size="small">Reset</el-button>
					<el-button v-if="ruleForm.MchId > 0" @click="onDelete" type="danger" size="small">Delete</el-button>
					<el-button :loading="loading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import dictApi from '/@/api/dict/index';
import dictItemApi from '/@/api/dictItem/index';

interface DialogParams {
	action: string;
	dictTypeId: number;
	itemId: number;
}

export default {
	name: 'DictItemEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create',
			isShowDialog: false,
			loading: false,
			ruleForm: {
				ItemId: 0, //
				DictId: 0, //字典类别
				ParentId: 0, //
				Name: '', //名称
				Value: '', //对应值
				Sort: 0, //排序
				Enable: false, //是否启用
				Description: '', //描述
				IsDeleted: false, //
				CreatedAt: new Date(), //
				DictIdArr: [],
			},
			dictData: [],
			rules: {
				DictIdArr: [{ required: true, message: 'Please input', trigger: 'change' }],
				Name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				Value: [{ required: true, message: 'Please input', trigger: 'blur' }],
				Sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
				Enable: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			onInitForm();

			dictApi.Tree().then((rs) => {
				state.dictData = rs.data;
			});

			if (parmas.action == 'Create') {
				if (parmas.dictTypeId) {
					state.ruleForm.DictIdArr.push(parmas.dictTypeId);
				}
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit';
				state.ruleForm.ItemId = parmas.itemId;
				getData(parmas.itemId);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				if (state.ruleForm.DictIdArr.length > 0) {
					obj.DictId = state.ruleForm.DictIdArr[state.ruleForm.DictIdArr.length - 1];
				}
				state.loading = true;
				dictItemApi
					.Save(obj)
					.then(() => {
						context.emit('fetchData', obj.dictId);
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			dictItemApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				state.ruleForm.DictIdArr = [];
				state.ruleForm.DictIdArr.push(state.ruleForm.dictId);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				dictItemApi
					.DeleteByKey(state.ruleForm.ItemId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				ItemId: 0, //
				DictId: 0, //字典类别
				ParentId: 0, //
				Name: '', //名称
				Value: '', //对应值
				Sort: 0, //排序
				Enable: false, //是否启用
				Description: '', //描述
				IsDeleted: false, //
				CreatedAt: new Date(), //
				DictIdArr: [],
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>
