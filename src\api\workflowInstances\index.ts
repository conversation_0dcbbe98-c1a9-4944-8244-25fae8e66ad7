﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowInstancesApi extends BaseApi {

    WorkflowInstanceCeancel(data: any) {
		return request({
			url: this.baseurl + 'WorkflowInstanceCeancel',
			method: 'post',
			data,
		});
	}

    WorkflowInstanceWithdraw(data: any) {
		return request({
			url: this.baseurl + 'WorkflowInstanceWithdraw',
			method: 'post',
			data,
		});
	}

}

export default new workflowInstancesApi('/api/workflowInstances/','id');




                        
        
        