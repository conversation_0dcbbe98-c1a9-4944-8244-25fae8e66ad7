<template>
	<el-container class="workflowApproval-index-container tablelist" style="height: 100%">
		<el-header>
			<el-page-header :content="data.name"></el-page-header>
			<div class="do">
				<el-button type="primary" @click="exportJson">export JSON</el-button>
			</div>
		</el-header>
		<el-main class="nopadding">
			<sc-workflow v-model="data.nodeConfig"></sc-workflow>
		</el-main>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent } from 'vue';
import scWorkflow from '/@/components/scWorkflow/index.vue';
import { v4 as uuidv4 } from 'uuid';

export default defineComponent({
	name: 'workflowApprovalIndex',
	components: { scWorkflow },
	setup() {
		const state = reactive({
			data: {
				id: 1,
				name: '发票录入',
				displayName: '',
				nodeConfig: {
					nodeId: uuidv4(),
					nodeName: '发起人',
					displayName: '发起人',
					type: 0,
					typeName: 'InitWorkflow',
					nodeRoleList: [],
					childNode: null,
					execute: { activityId: uuidv4(), name: 'TestSignalStart', displayName: '触发流程', type: 'SignalReceived' },
				},
			},
		});

		const exportJson = () => {
			console.log(state.data);
		};

		// 页面加载时
		onMounted(() => {});
		return {
			exportJson,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.el-main {
	--el-main-padding: 20px;
	display: block;
	flex: 1;
	flex-basis: auto;
	overflow: auto;
	box-sizing: border-box;
	padding: var(--el-main-padding);
}
</style>