﻿<template>
	<div class="dict-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="500px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item label="Name" prop="Name">
							<el-input v-model="ruleForm.Name" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item label="Value" prop="Value">
							<el-input v-model="ruleForm.Value" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item label="Description" prop="Description">
							<el-input v-model="ruleForm.Msg" type="textarea" placeholder="Please input" maxlength="150"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
					<el-button @click="onInitForm" size="small">Reset</el-button>
					<el-button v-if="ruleForm.MchId > 0" @click="onDelete" type="danger" size="small">Delete</el-button>
					<el-button :loading="loading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import dictApi from '/@/api/dict/index';

export default {
	name: 'DictEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Dictionary category',
			isShowDialog: false,
			loading: false,
			ruleForm: {},
			rules: {
				Name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				Value: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (row: Object) => {
			onInitForm();
			state.ruleForm = row || state.ruleForm;
			state.isShowDialog = true;
			if (parseThanZero(state.ruleForm.dictId)) {
				state.title = 'Update ';
			}
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				state.loading = true;
				dictApi
					.Save(obj)
					.then((rs) => {
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			dictApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No，Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				dictApi
					.DeleteByKey(state.ruleForm.dictId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				DictId: 0, //
				Name: '', //名称
				Value: '', //对应值
				Description: '', //描述
			};
			if (state.ruleForm.dictId) {
				state.title = 'Edit Dictionary category';
			}
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>
