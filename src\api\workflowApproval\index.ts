﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class workflowApprovalApi extends BaseApi {

    GetWorkflowNodeLogs(params: any) {
        return request({
            url: this.baseurl + 'GetWorkflowNodeLogs',
            method: 'get',
            params,
        });
    }


    
    ApproveWorkflow(data: any) {
        return request({
            url: this.baseurl + 'ApproveWorkflow',
            method: 'post',
            data,
        });
    }

    RejectWorkflow(data: any) {
        return request({
            url: this.baseurl + 'RejectWorkflow',
            method: 'post',
            data,
        });
    }

    TransferWorkflow(data: any) {
        return request({
            url: this.baseurl + 'TransferWorkflow',
            method: 'post',
            data,
        });
    }
    

    QueryWorkflowApproval(params: any) {
        return request({
            url: this.baseurl + 'QueryWorkflowApproval',
            method: 'get',
            params,
        });
    }
}

export default new workflowApprovalApi('/api/workflowApproval/','id');




                        
        
        