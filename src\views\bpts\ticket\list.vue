<template>
	<div class="list-page-layout">
		<el-container style="height: 100%">
			<el-header style="height: auto">
				<div class="statistics" v-if="false">
					<el-card class="ticket_list_c">
						<ul>
							<li class="tk_c-item">
								<span>Open Ticks ( current )</span>
							</li>
							<li class="tk_c-item">
								<el-badge :value="countData.self" class="item">
									<el-button round @click="setQuery('assigToId')" size="small"
										:type="buttonSelected['assigToId'] ? 'primary' : 'default'">
										YOU
									</el-button>
								</el-badge>
							</li>
							<li class="tk_c-item">
								<el-badge :value="countData.group" class="item">
									<el-button round @click="setQuery('groupId')" size="small"
										:type="buttonSelected['groupId'] ? 'primary' : 'default'">
										GROUPS
									</el-button>
								</el-badge>
							</li>
						</ul>
					</el-card>

					<el-card class="ticket_list_c">
						<ul>
							<li class="tk_c-item">
								<span>Ticket Statistics ( this week )</span>
							</li>
							<li class="tk_c-item">
								<el-badge :value="countData.fromEmail" class="item">
									<el-button round @click="setQuery('fromEmail')" size="small"
										:type="buttonSelected['fromEmail'] ? 'primary' : 'default'">
										FROM EMAIL
									</el-button>
								</el-badge>
							</li>
							<li class="tk_c-item">
								<el-badge :value="countData.fromSystem" class="item">
									<el-button round @click="setQuery('fromSystem')" size="small"
										:type="buttonSelected['fromSystem'] ? 'primary' : 'default'">
										FROM SYSTEM
									</el-button>
								</el-badge>
							</li>
							<li class="tk_c-item">
								<el-badge :value="countData.solved" class="item">
									<el-button round @click="setQuery('ticket_Status', 'Solved')" size="small"
										:type="buttonSelected['ticket_Status'] ? 'primary' : 'default'">
										SOLVED
									</el-button>
								</el-badge>
							</li>
						</ul>
					</el-card>
				</div>

				<el-card class="list-search-card mb1" shadow="never" :body-style="{ paddingTop: '6px' }">
					<div class="list-index-search">
						<el-form label-width="98px" @keyup.enter="onSearch">
							<el-row :gutter="1">
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.caseNumber')">
										<el-input class="w-15" v-model="tableData.params.ticket_Number"
											clearable></el-input>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.customers')">
										<el-tree-select v-model="tableData.params.ticket_Customer_ListV2"
											:data="customerOptsV2" node-key="value" multiple
											:render-after-expand="false" filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											:check-on-click-node="true" :check-strictly="true"
											:default-expand-all="true" class="w-15 tag-select-input" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleCustomerCheckChange">
											<template #header>
												<el-checkbox v-model="customerCheckAll"
													@change="handleCustomerCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.priority')">
										<el-tree-select v-model="tableData.params.ticket_Priority_List"
											:data="priorityData" multiple :render-after-expand="false" filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											class="w-20 tag-select-input" :check-on-click-node="true" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handlePriorityCheckChange">
											<template #header>
												<el-checkbox v-model="priorityCheckAll"
													@change="handlePriorityCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.createDate')" class="date-form-item">
										<div class="date-container">
											<MyDate v-model:input="tableData.params.startTime" class="date-input" />
											<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
											<MyDate v-model:input="tableData.params.endTime" class="date-input" />
										</div>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.ticketSubject')">
										<el-input class="w-15" v-model="tableData.params.ticket_Subject"
											clearable></el-input>
									</el-form-item>
								</el-col>



								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketFields.BusinessUnit')">
										<el-tree-select v-model="tableData.params.BusinessUnitList"
											:data="businessUnitData" multiple :render-after-expand="false" filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											class="w-15 tag-select-input" :check-on-click-node="true" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleBusinessUnitCheckChange">
											<template #header>
												<el-checkbox v-model="businessUnitCheckAll"
													@change="handleBusinessUnitCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="currentUser.userType == 'Staff Member'">
									<el-form-item label="Assigned To">
										<el-tree-select ref="assigneToRef" v-model="tableData.params.assignToSearchList"
											:data="assigneeData" node-key="value" multiple :render-after-expand="false"
											filterable :filter-node-method="filterNodeMethod" clearable show-checkbox
											:check-on-click-node="true" :check-strictly="true"
											:default-expand-all="true" class="w-15 tag-select-input" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleAssignedToCheckChange">
											<template #header>
												<el-checkbox v-model="assignedToCheckAll"
													@change="handleAssignedToCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.modifiedAt')" class="date-form-item">
										<div class="date-container">
											<MyDate v-model:input="tableData.params.startTimeModified"
												class="date-input" />
											<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
											<MyDate v-model:input="tableData.params.endTimeModified"
												class="date-input" />
										</div>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="currentUser.userType == 'Staff Member'">
									<el-form-item :label="$t('message.ticketLabels.ticketContentType')">
										<el-tree-select v-model="tableData.params.ticketContentType_List"
											:data="ticketTypeData" multiple :render-after-expand="false" filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											class="w-15 tag-select-input" :check-on-click-node="true" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleTicketTypeCheckChange">
											<template #header>
												<el-checkbox v-model="ticketTypeCheckAll"
													@change="handleTicketTypeCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.projects')">
										<el-tree-select v-model="tableData.params.ticket_Customer_List"
											:data="customerOpts" node-key="value" multiple :render-after-expand="false"
											filterable :filter-node-method="filterNodeMethod" clearable show-checkbox
											:check-on-click-node="true" :check-strictly="true"
											:default-expand-all="true" class="w-15 tag-select-input" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleProjectsCheckChange">
											<template #header>
												<el-checkbox v-model="projectsCheckAll"
													@change="handleProjectsCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>


								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="currentUser.userType == 'Staff Member'">
									<el-form-item :label="$t('message.userFields.delegateTo')">
										<el-input v-model="tableData.params.userDelegateName" clearable
											class="w-15"></el-input>
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="tableData.params.onHold == false">
									<el-form-item :label="$t('message.ticketLabels.status')">
										<el-tree-select ref="selStatus" v-model="tableData.params.ticket_Status_List"
											:data="statusOpts" multiple filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											class="w-15 tag-select-input" :check-on-click-node="true"
											@node-click="nodeClick" collapse-tags
											:default-checked-keys="defaultCheckedKeys"
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleStatusCheckChange">
											<template #header>
												<el-checkbox v-model="statusCheckAll" @change="handleStatusCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
										<!-- <el-select placeholder="All" v-model="tableData.params.ticket_Status" clearable class="w-20"
											default-first-option
											filterable	
											>
											<el-option v-for="item in statusOpts" :label="item.itemName" :value="item.itemValue" :key="item.itemId"></el-option>
										</el-select> -->
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="tableData.params.onHold == true">
									<el-form-item :label="$t('message.ticketLabels.scheduledDate')"
										class="date-form-item">
										<div class="date-container">
											<MyDate v-model:input="tableData.params.startScheduled"
												class="date-input" />
											<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
											<MyDate v-model:input="tableData.params.endScheduled" class="date-input" />
										</div>
									</el-form-item>
								</el-col>


								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6">
									<el-form-item :label="$t('message.ticketLabels.category')">
										<el-tree-select v-model="tableData.params.ticket_Category_List"
											:data="categoryOpts" multiple :render-after-expand="false" filterable
											:filter-node-method="filterNodeMethod" clearable show-checkbox
											class="w-15 tag-select-input" :check-on-click-node="true" collapse-tags
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											@change="handleCategoryCheckChange">
											<template #header>
												<el-checkbox v-model="categoryCheckAll"
													@change="handleCategoryCheckAll">
													All
												</el-checkbox>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb6"
									v-if="userCenter && currentUser.userType == 'Staff Member'">
									<el-form-item :label="$t('message.ticketLabels.assignedtoMe')">
										<el-checkbox v-model="tableData.params.assignedToMe" @click="onAssignedToMe" />
									</el-form-item>
								</el-col>

								<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch"> {{
											$t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet"> {{
											$t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>

							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>
			<el-header class="table_header mt5" style="display: flex" v-if="!disalogList">
				<div class="left-panel">
					<Auth :value="route.name == 'TicketList' ? 'TicketInfo.Visit' : 'ScheduleTicketInfo.Visit'">
						<el-button size="small" type="primary" class="ml10" @click="onAdd">
							{{ tableData.params.onHold == false ? $t('message.ticketButtons.newTicket') :
								$t('message.ticketButtons.newScheduleTicket') }}
						</el-button>
					</Auth>
				</div>
				<div class="right-panel">
					<Auth :value="route.name == 'TicketList' ? 'TickList.Export' : 'TicketListScheduled.Export'">
						<el-dropdown>
							<el-button type="primary" size="small" class="ml10">
								<el-icon>
									<ele-ArrowDownBold />
								</el-icon>
								{{ $t('message.page.buttonExport') }}
							</el-button>
							<template #dropdown>
								<el-dropdown-menu class="user-dropdown">
									<el-dropdown-item @click="onExportAllRecord(0)">{{
										$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
									<el-dropdown-item @click="onExportAllRecord(1)">{{
										$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</Auth>
				</div>
			</el-header>
			<el-main style="flex: 1; padding: 0px 22px">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<!-- @select="selectFun" -->
						<el-table ref="ticketTableRef" :data="tableData.data" v-loading="tableData.loading"
							:default-expand-all="tableData.params.searchKey !== ''" height="calc(100%)"
							table-layout="fixed" row-key="id" lazy highlight-current-row scrollbar-always-on
							:load="loadChildrenTickets" @row-click="rowClick" @sort-change="onSortChange"
							@selection-change="selectionChange" @expand-change="onExpandChange"
							@select-all="selectAllList" :row-class-name="rowExpandHandler" class="ticketTable"
							:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
							:row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
							:default-sort="{ prop: 'ticket_Number', order: 'descending' }" stripe border>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<el-table-column fixed type="selection" :reserve-selection="true" v-if="!disalogList" />

							<el-table-column fixed min-width="32">
								<template #default="{ row }">
									<el-icon v-if="row.showChildRenIcon" style="top: -10px">
										<ele-Minus />
									</el-icon>
								</template>
							</el-table-column>

							<el-table-column fixed align="center" min-width="60">
								<template #default="{ row }">
									<el-badge :value="row.ticketContentsTotal" class="comment-count"
										:hidden="row.ticketContentsTotal == 0">
										<img src="/img/Comment.gif" style="cursor: pointer" alt="noView"
											@click="openTicketCommentView(row.id)" />
									</el-badge>
								</template>
							</el-table-column>
							<!-- <el-table-column fixed type="expand"  /> -->

							<el-table-column fixed sortable :label="$t('message.ticketFields.status')"
								prop="ticket_Status" width="90" show-overflow-tooltip>
								<template #default="{ row }">
									<span v-if="tableData.params.onHold && row.onHold">Scheduled</span>
									<span v-else-if="!tableData.params.onHold">{{ row.ticket_Status }}</span>
									<span v-else>{{ row.ticket_Status }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed :label="$t('message.ticketFields.caseNumber')" prop="ticket_Number"
								min-width="140" show-overflow-tooltip type="" v-if="!disalogList" sortable="custom"
								:sort-orders="['ascending', 'descending']">
								<template #default="{ row }" v-if="ticketEditVisit">
									<el-link @click="onEdit(row)" type="primary"
										v-if="tableData.params.onHold && row.onHold">
										{{ row.ticket_Number }}
									</el-link>
									<el-link @click="onEdit(row)" type="primary" v-else-if="!tableData.params.onHold">
										{{ row.ticket_Number }}
									</el-link>
									<span v-else>{{ row.ticket_Number }}</span>
								</template>
								<template #default="{ row }" v-else>
									<span>{{ row.ticket_Number }}</span>
								</template>
							</el-table-column>

							<el-table-column fixed sortable :label="$t('message.ticketFields.caseNumber')"
								prop="ticket_Number" min-width="130" show-overflow-tooltip type="" v-else>
								<template #default="{ row }">
									<el-link @click="onEdit(row)" type="primary" v-if="row.ticket_Parent_Id == 0">
										{{ row.ticket_Number }}
									</el-link>
									<span v-else>{{ row.ticket_Number }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.ticketSubject')"
								prop="ticket_Subject" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.ticket_Subject }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.customer')"
								prop="ticket_Company_Name" width="130" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_Company_Name }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.BusinessUnit')"
								prop="businessUnit" width="130" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.businessUnit }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.project')"
								prop="ticket_Customer_Value" width="130" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_Customer_Value }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.assignTo')" prop="AssigToName"
								width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.assigToName }}</span>
								</template>
							</el-table-column>

							<el-table-column v-if="showGrantAccess" :label="$t('message.ticketFields.grantAccess')"
								prop="userGrantFlag" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.userGrantName }}</span>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.ticketFields.delegateTo')" prop="delegateId"
								width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.delegateName }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.scheduledDate')"
								prop="scheduledDate" width="200" show-overflow-tooltip type=""
								v-if="tableData.params.onHold">
								<template #default="{ row }">
									{{ row.scheduledDate?.toString().split(' ')[0].replace('01/01/0001', '') }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.priority')"
								prop="ticket_Priority" width="130" show-overflow-tooltip type="">
								<template #default="{ row }">
									<el-tag type="success" v-if="row.ticket_Priority === 'Low'" style="width: 70px">{{
										row.ticket_Priority }}</el-tag>
									<el-tag type="info" v-if="row.ticket_Priority === 'Normal'" style="width: 70px">{{
										row.ticket_Priority }}</el-tag>
									<el-tag type="warning" v-if="row.ticket_Priority === 'High'" style="width: 70px">{{
										row.ticket_Priority }}</el-tag>
									<el-tag type="danger" v-if="row.ticket_Priority === 'Urgent'" style="width: 70px">{{
										row.ticket_Priority }}</el-tag>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.category')"
								prop="ticket_Category" width="130" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_Category }}
								</template>
							</el-table-column>

							<!-- <el-table-column
								sortable
								:label="$t('message.ticketFields.company')"
								prop="ticket_Company_Name"
								width="130"
								show-overflow-tooltip
								type=""
							>
								<template #default="{ row }">
									{{ row.ticket_Company_Name }}
								</template>
							</el-table-column> -->

							<el-table-column sortable :label="$t('message.ticketFields.attachments')"
								prop="isAttachment" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<el-icon v-if="row.isAttachment === 'Y'" style="color: #67c23a">
										<!--CCB-placement使用top或bottom定位都没有偏移问题，加上用offset属性调整位置即可 -->
										<el-popover ref="popoverFilldRef" placement="bottom-start" :width="400"
											trigger="hover" @before-enter="getTicketAttachments(row.id)"
											:popper-options="{ modifiers: [{ name: 'offset', options: { offset: [15, 0] } }] }">
											<template #reference>
												<ele-SuccessFilled />
											</template>
											<el-table :data="ticketAttachments">
												<el-table-column property="attachment_Original_File_Name" label="Name">
													<template #default="{ row }">
														<el-icon class="ele-icon"><ele-Download /></el-icon>
														<el-link class="link_text" @click="downloadFile(row)">
															{{ row.attachment_Original_File_Name }}
														</el-link>
														<el-tag v-if="previewFile(row.attachment_File_Ext)"
															type="warning" style="cursor: pointer;"
															@click="handleFilePreview(ticketAttachments, row)">{{
																$t('message.ticketLabels.preview') }}</el-tag>
													</template>
												</el-table-column>
											</el-table>
										</el-popover>
									</el-icon>
									<el-icon v-if="row.isAttachment === 'N'" style="color: red">
										<ele-CircleCloseFilled />
									</el-icon>
								</template>
							</el-table-column>
							<el-table-column sortable :label="$t('message.ticketFields.ticketType')"
								prop="ticketContentType" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticketContentType }}
								</template>
							</el-table-column>
							<el-table-column sortable :label="$t('message.ticketFields.openedBy')" prop="ticket_Open_By"
								width="140" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_Open_By_Name }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.ticketFields.solvedBy')" width="130"
								show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_Close_By_Name }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.createdDate')" prop="createdAt"
								width="200" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.createdAt }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.createdBy')" prop="createdByName"
								width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.createdByName }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.modifiedDate')" prop="modifiedAt"
								width="200" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.modifiedAt }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.modifiedBy')"
								prop="modifiedByName" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.modifiedByName }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.operatorName')"
								prop="operatorName" width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									<span>{{ row.operatorName }}</span>
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.ticketFields.ticketFrom')" prop="ticket_From"
								width="150" show-overflow-tooltip type="">
								<template #default="{ row }">
									{{ row.ticket_From }}
								</template>
							</el-table-column>

							<Auth
								:value="route.name == 'TicketList' ? 'TickList.Delete' : 'TicketListScheduled.Delete'">
								<el-table-column fixed="right" align="left" :label="$t('message.page.actions')"
									:min-width="160" v-if="!disalogList">
									<template #default="{ row }">
										<!-- <el-button v-if="0 > 1" size="mini" type="text" @click="onDetail(row)">{{
											$t('message.page.actionsView') }}</el-button> -->

										<el-row v-if="row.ticket_Status == 'New'">
											<el-col :xs="24" :sm="24" :md="24" :lg="6">
												<el-button size="mini" type="text" style="color: #ff3a3a"
													@click="onDelete(row)">
													{{ $t('message.page.actionsDelete') }}
												</el-button>
											</el-col>
										</el-row>
									</template>
								</el-table-column>
							</Auth>
						</el-table>
						<el-image-viewer v-if="previewVisibleRef" :url-list="previewSrcList" hide-on-click-modal
							:initial-index="previewIndexRef" @close="previewVisibleRef = false" />
						<div class="scTable-page">
							<el-pagination @size-change="onSizechange" @current-change="onCurrentChange"
								:pager-count="5" :page-sizes="[10, 20, 30]"
								v-model:current-page="tableData.params.pageIndex" background
								v-model:page-size="tableData.params.pageSize"
								layout="total, sizes, prev, pager, next, jumper" :total="tableData.total" small>
							</el-pagination>
						</div>
					</div>
				</div>
			</el-main>
			<ticket-comment-view :show-Comment-ID="showCommentID" :show-Comment-View="showCommentView"
				@closed-Comment="closedComment"></ticket-comment-view>
			<FilePreview ref="ticketFileDialogRef"></FilePreview>
		</el-container>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent, computed, onBeforeMount, getCurrentInstance, onUnmounted, watch, onActivated, nextTick } from 'vue';
import { ElMessageBox, ElMessage, ElTree } from 'element-plus';
import { formatStrDate, formatDateTime, formatDay } from '/@/utils/formatTime';
import print from '/@/utils/print.js';
import { useI18n } from 'vue-i18n';

import cmTicketsApi from '/@/api/cmTickets/index';
import dictItemApi from '/@/api/dictItem/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import organizationApi from '/@/api/organization/index';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import MyDate from '/@/components/ticket/ticketDate.vue';
import ticketCommentView from './components/commentView.vue';
import { useRoute } from 'vue-router';
import Auth from '/@/components/auth/auth.vue';
import cmTicketAttachment from '/@/api/cmTicketAttachment/index';
import mittBus from '/@/utils/mitt';
import FilePreview from './components/filePreview.vue';
import request from '/@/utils/request';
import { encodeTicketNumber } from '/@/utils';

export default defineComponent({
	name: 'TicketList',
	components: {
		MyDate,
		ticketCommentView,
		Auth,
		FilePreview,
	},
	props: {
		disalogList: {
			type: Boolean,
			default: false,
		},
		info: Object,
	},
	setup(props: any) {
		const { proxy } = <any>getCurrentInstance();
		const { t } = useI18n();
		const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const route = useRoute();

		const previewVisibleRef = ref(false);
		const previewIndexRef = ref(0);
		const previewSrcList = ref();
		const ticketFileDialogRef = ref();
		const fileData = ref({});
		const popoverFilldRef = ref();

		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});

		proxy._.type.name = route.name;

		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [] as any,
				paramsType: 0, //默认使用params的参数进行查询 1：表示使用topParams来查询
				params: {
					groupId: '' as any,
					ticket_Parent_Id: 0,
					ticket_Status: '' as any,
					ticket_Number: '',
					ticket_Subject: '',
					ticket_Priority: '',
					ticket_Status_List: ["New", "Open", "Replied", "Hold "],
					ticket_Priority_List: [],
					ticketContentType_List: [],
					ticket_Category_List: [],
					ticket_Customer_List: [],
					BusinessUnitList: [],
					ticket_Customer_ListV2: [],
					assignToSearchList: [],
					userDelegateName: '',
					ticket_From: '',
					pageIndex: 1,
					pageSize: 20,
					searchKey: 's',
					startTime: '',
					endTime: '',
					startTimeModified: '',
					endTimeModified: '',
					assigToId: '' as any,
					order: 'Ticket_Number',
					sort: 'desc', // asc or desc
					ids: [],
					ischeckType: 0,
					onHold: false,
					startScheduled: '',
					endScheduled: '',
					assignedToMe: false,
					assignedToMeOld: false,
				} as any,
				topParams: {
					groupId: '' as any,
					ticket_Status: '' as any,
					assigToId: '' as any,
					ticket_From: '',
				},
			},
			priorityData: [],
			ticketTypeData: [],
			countData: [] as any,
			pickerStartDate: '',
			pickerEndDate: '',
			categoryOpts: [],
			customerOpts: [],
			customerOptsV2: [],
			businessUnitData: [] as any,
			statusOpts: [],
			statusCheckAll: false,
			defaultCheckedKeys: ["New", "Open", "Replied", "Hold "],
			radio2: '',
			buttonSelected: {} as any,
			showCommentView: false,
			showCommentID: '0' as any,
			disalogList: props.disalogList,
			ticketAttachments: [] as any,
			showGrantAccess: false,
			userCenter: (props.info == null || props.info == undefined) ? true : false,
			assigneeData: [] as any,
			categoryCheckAll: false,
			customerCheckAll: false,
			priorityCheckAll: false,
			projectsCheckAll: false,
			ticketTypeCheckAll: false,
			assignedToCheckAll: false,
			businessUnitCheckAll: false,

		});

		const rowExpandHandler = ({ row }) => {
			if (row.children == null) {
				return 'icon-no';
			}
		};

		const ticketEditVisit = computed(() => {
			return userInfos.value.authBtnList.some((v: string) => v === 'TicketEdit.Visit');
		});

		//单击选中多选框
		const rowClick = (row: { id: any; }, column: any, event: any) => {
			// 点击行多选
			// 从已选中数据中 判断当前点击的是否被选中
			const selected = state.tableData.selection.some((item: { id: any }) => item.id === row.id); // 是取消选择还是选中
			if (!selected) {
				// 不包含   代表选择
				state.tableData.selection.push(row);
				proxy.$refs['ticketTableRef'].toggleRowSelection(row, true);
			} else {
				// 取消选择
				var finalArr = state.tableData.selection.filter((item: { id: any }) => {
					return item.id !== row.id;
				});
				state.tableData.selection = finalArr; // 取消后剩余选中的

				proxy.$refs['ticketTableRef'].toggleRowSelection(row, false);
			}
		};

		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return userInfos.value;
		});

		const filterNodeMethod = (value: string, data: Tree) => {
			return data.label.toLowerCase().includes(value.toLowerCase());
		};

		//初始化
		const onInit = (loadData: any = 'No') => {
			//为了把 【统计的搜索条件】  和 【搜索框的条件】区分开来 要这样写
			const query = { ...state.tableData.params };

			if (route.name == 'ScheduledList') {
				state.tableData.params.onHold = true;
				query.onHold = true;
			}

			if (state.tableData.paramsType === 1) {
				query.groupId = state.tableData.topParams.groupId;
				query.ticket_Status = state.tableData.topParams.ticket_Status;
				query.assigToId = state.tableData.topParams.assigToId;
				query.ticket_From = state.tableData.topParams.ticket_From;
			}

			// state.tableData.params.ticket_Status_List= [{ value: 0, label: 'New' },
			// { value:1, label: 'Open' },
			// { value: 2, label: 'Replied' },
			// { value: 3, label: 'Hold' }];


			state.tableData.loading = true;

			query.disalogList = state.disalogList;

			if (state.ticketTypeData.length == 0) {
				state.ticketTypeData.push({ value: "Public Ticket", label: "Public Ticket" });
				state.ticketTypeData.push({ value: "Internal Ticket", label: "Internal Ticket" });
			}

			cmTicketsApi
				.Query(query)
				.then((rs: any) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;

					rs.data.forEach((item) => {
						if (item.showGrantAccess == true) {
							state.showGrantAccess = true;
							return;
						}
					});
				})
				.catch(() => { })
				.finally(() => {
					state.tableData.loading = false;

					if (loadData == 'Yes') {
						setTimeout(() => {
							var dictArr = ['Priority', 'TicketStatus', 'TicketSearchAssignTo', 'BusinessUnit'];

							dictItemApi.Many(dictArr).then((rs: any) => {
								let priorityArr = rs.data?.find((a: any) => {
									return a.dictValue === 'Priority';
								})?.items;

								let statusArr = rs.data?.find((a: any) => {
									return a.dictValue === 'TicketStatus';
								})?.items;

								statusArr.sort((a: number, b: number) => a.sort - b.sort);

								state.statusOpts = [];
								for (let item of statusArr) {
									state.statusOpts.push({ value: item.itemValue, label: item.itemName });
								}

								for (let item of priorityArr) {
									state.priorityData.push({ value: item.itemValue, label: item.itemName });
								}

								state.assigneeData = rs.data?.find((a: any) => {
									return a.dictValue === 'TicketSearchAssignTo';
								})?.objectItems.groupList;

								let businessUnitArr = rs.data?.find((a: any) => {
									return a.dictValue === 'BusinessUnit';
								})?.items;

								state.businessUnitData = businessUnitArr.map((item: any) => {
									return {
										value: item.itemValue,
										label: item.description ? item.itemValue + ' - ' + item.description : item.itemValue,
									}
								});
							});

							//类别
							cmCategoryApi.GetListByUserId({ order: 'Category_Code', sort: 'asc' }).then((rs) => {
								let categoryArr = rs.data;

								for (let item of categoryArr) {
									state.categoryOpts.push({ value: item['category_Code'], label: item['category_Code'] + ' - ' + item['category_Description'] });
								}
							});

							if (currentUser.value.roles.indexOf('Super Admin') > -1) {
								organizationApi.GetCompanyProjectTree({}).then((rs) => {
									state.customerOpts = rs.data.treeNodeList;
									//console.log("bbbb:",rs.data.treeNodeList);
								});

								organizationApi.GetCompanyProjectTreeV3({}).then((rs) => {
									state.customerOptsV2 = rs.data.treeNodeList;
								});
							} else {
								organizationApi.GetCompanyProjectTreeByUserId({ MasterUserTypeFlag: 1 }).then((rs) => {
									state.customerOpts = rs.data.treeNodeList;
								});

								organizationApi.GetCompanyProjectTreeByUserIdV3({ MasterUserTypeFlag: 1 }).then((rs) => {
									state.customerOptsV2 = rs.data.treeNodeList;
								});
							}
						}, 100);
					}
				});
		};

		const assgintoNameInit = () => {
			if (currentUser.value.roles.indexOf('Employee') > -1 ||
				currentUser.value.roles.indexOf('Super Admin') > -1 ||
				currentUser.value.roles.indexOf('Admin') > -1) {
				state.tableData.params.assignedToMe = true;
				state.tableData.params.assignedToMeOld = true;
			}
			else {
				state.tableData.params.assignedToMe = false;
				state.tableData.params.assignedToMeOld = false;
			}
		}

		const toggleRowExpansionAll = (data, isExpansion) => {
			data.forEach((item) => {
				proxy.$refs.ticketTableRef.toggleRowExpansion(item, isExpansion);
				if (item.children !== undefined && item.children !== null) {
					toggleRowExpansionAll(item.children, isExpansion);
				}
			});
		};

		//设置统计按钮选中的 查询条件
		const setQuery = (paramType: string, value?: string) => {
			const isSelect = state.buttonSelected[paramType];

			initFormData();

			state.buttonSelected[paramType] = isSelect;

			// 检查 state.buttonSelected[paramType] 的值
			if (state.buttonSelected[paramType]) {
				state.buttonSelected[paramType] = false;
			} else {
				state.buttonSelected = {};
				state.buttonSelected[paramType] = true;
			}

			const isCheck = state.buttonSelected[paramType];
			if (isCheck) {
				if (paramType === 'assigToId') {
					state.tableData.topParams.assigToId = currentUser.value.userId;
				}
				if (paramType === 'groupId') {
					state.tableData.topParams.groupId = currentUser.value.userId;
				}
				if (paramType === 'fromEmail') {
					state.tableData.topParams.ticket_From = 'From Email';
					state.tableData.topParams.assigToId = currentUser.value.userId;
				}
				if (paramType === 'fromSystem') {
					state.tableData.topParams.ticket_From = 'From System';
					state.tableData.topParams.assigToId = currentUser.value.userId;
				}
				if (paramType === 'ticket_Status') {
					state.tableData.topParams.ticket_Status = value;
				}
			}

			//默认使用params的参数进行查询 1：表示使用topParams来查询
			state.tableData.paramsType = 1;
			onInit();
		};

		const loadTicketCount = () => {
			cmTicketsApi
				.TicketCount()
				.then((rs) => {
					state.countData = rs.data;
				})
				.catch((rs) => { })
				.finally();
		};

		const loadChildrenTickets = async (row: any, treeNode: unknown, resolve: (data: any[]) => void) => {
			var params = {
				Ticket_Parent_Id: row.id,
			};
			const result = await cmTicketsApi.Query(params);

			resolve(result.data);
		};

		//全选开始-----------------------------------------
		const handleCategoryCheckChange = (value: any) => {
			if (value.length == 0) {
				state.categoryCheckAll = false;
			}

		};
		const handleCategoryCheckAll = (value: boolean) => {
			if (value) {
				state.tableData.params.ticket_Category_List = state.categoryOpts.map((item: any) => item.value);
			}
			else {
				state.tableData.params.ticket_Category_List = [];
			}
		};
		const handleCustomerCheckChange = (value: any) => {

			if (value.length == 0) {
				state.customerCheckAll = false;
			}

		};

		const handleCustomerCheckAll = (value: boolean) => {
			if (value) {
				let allNodesIds = [];
				GetTreeAllNode(state.customerOptsV2, allNodesIds);
				state.tableData.params.ticket_Customer_ListV2 = allNodesIds;

			}
			else {
				state.tableData.params.ticket_Customer_ListV2 = [];
			}
		};
		//返回所有节点值
		const GetTreeAllNode = (nodes, allNodesIds) => {
			nodes.forEach(item => {
				allNodesIds.push(item.value);
				if (item.children && item.children.length) {
					GetTreeAllNode(item.children, allNodesIds);
				}
			});
		};

		const handleProjectsCheckChange = (value: any) => {
			if (value.length == 0) {
				state.projectsCheckAll = false;
			}
		};

		const handleProjectsCheckAll = (value: boolean) => {
			if (value) {
				let allNodesIds = [];
				GetTreeAllNode(state.customerOpts, allNodesIds);
				state.tableData.params.ticket_Customer_List = allNodesIds;

			}
			else {
				state.tableData.params.ticket_Customer_List = [];
			}
		}

		const handleStatusCheckChange = (value: any) => {
			if (value.length == 0) {
				state.statusCheckAll = false;
			}

		};
		const handleStatusCheckAll = (value: boolean) => {
			if (value) {
				state.tableData.params.ticket_Status_List = ["New", "Open", "Replied", "Hold ", "Solved", "Cancelled"];
			}
			else {
				state.tableData.params.ticket_Status_List = [];
			}
		};
		const handlePriorityCheckChange = (value: any) => {
			if (value.length == 0) {
				state.priorityCheckAll = false;
			}

		};
		const handlePriorityCheckAll = (value: boolean) => {

			if (value) {
				state.tableData.params.ticket_Priority_List = state.priorityData.map((item: any) => item.value);
			}
			else {
				state.tableData.params.ticket_Priority_List = [];
			}
		};

		const handleTicketTypeCheckChange = (value: any) => {
			if (value.length == 0) {
				state.ticketTypeCheckAll = false;
			}

		};
		const handleTicketTypeCheckAll = (value: boolean) => {
			if (value) {
				state.tableData.params.ticketContentType_List = state.ticketTypeData.map((item: any) => item.value);
			}
			else {
				state.tableData.params.ticketContentType_List = [];
			}
		};

		const handleAssignedToCheckChange = (value: any) => {
			if (value.length == 0) {
				state.assignedToCheckAll = false;
			}

		};
		const handleAssignedToCheckAll = (value: boolean) => {
			if (value) {
				let allNodesIds = [];
				GetTreeAllNode(state.assigneeData, allNodesIds);
				state.tableData.params.assignToSearchList = allNodesIds;

			}
			else {
				state.tableData.params.assignToSearchList = [];
			}
		};


		const handleBusinessUnitCheckChange = (value: any) => {
			if (value.length == 0) {
				state.businessUnitCheckAll = false;
			}
		}

		const handleBusinessUnitCheckAll = (value: boolean) => {
			if (value) {
				state.tableData.params.BusinessUnitList = state.businessUnitData.map((item: any) => item.value);
			}
			else {
				state.tableData.params.BusinessUnitList = [];
			}
		}

		//全选结束---------------------------------------


		//搜索
		const onSearch = () => {
			if (state.tableData.params.endTime.length > 0 && state.tableData.params.startTime > state.tableData.params.endTime) {
				ElMessage.warning(t('message.ticketSearch.searchDataUnvalid'));
				return;
			}

			if (
				state.tableData.params.ticket_Status_List.length > 0 ||
				state.tableData.params.ticket_Priority_List.length > 0 ||
				state.tableData.params.ticketContentType_List.length > 0 ||

				state.tableData.params.ticket_Category_List.length > 0 ||
				state.tableData.params.ticket_Customer_List.length > 0 ||
				state.tableData.params.BusinessUnitList.length > 0 ||
				state.tableData.params.ticket_Customer_ListV2.length > 0 ||
				state.tableData.params.assignToSearchList.length > 0 ||
				state.tableData.params.ticket_Number !== '' ||
				state.tableData.params.ticket_Subject !== '' ||
				state.tableData.params.startTime !== '' ||
				state.tableData.params.endTime !== '' ||
				state.tableData.params.startModified !== '' ||
				state.tableData.params.endTimeModified !== '' ||
				state.tableData.params.userDelegateName !== '' ||
				state.tableData.params.startScheduled !== '' ||
				state.tableData.params.endScheduled !== '' ||
				state.tableData.params.assignedToMe
			) {
				state.tableData.params.searchKey = 's';
			} else {
				state.tableData.params.searchKey = '';
			}

			initTableDataparams();

			onInit();

			if (state.tableData.params.searchKey == '') {
				toggleRowExpansionAll(state.tableData.data, false);
			}
		};

		const onSearchReSet = () => {
			proxy.$refs['ticketTableRef'].clearSelection();
			state.tableData.params.searchKey = '';
			initFormData();
			assgintoNameInit();
			onInit();

			if (state.tableData.params.searchKey == '') {
				toggleRowExpansionAll(state.tableData.data, false);
			}
		};

		const onAssignedToMe = () => {
			state.tableData.params.assignedToMeOld = !state.tableData.params.assignedToMeOld;
		}

		// 添加
		const onAdd = () => {
			if (route.name == 'ScheduledList') {
				router.push('/dashboard/schedule/create');
			} else {
				router.push('/dashboard/ticket/create');
			}
		};

		// 修改
		const onEdit = (row: any) => {
			if (state.disalogList) {
				mittBus.emit('UpdateParentTicket', row);
				mittBus.emit('HideDialogList');
			} else {
				const encodedTicketNumber = encodeTicketNumber(row.ticket_Number);
				router.push('/dashboard/editTicket/' + encodedTicketNumber);
			}
		};

		// 删除
		const onDelete = (row: any) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				cmTicketsApi
					.DeleteByKey(row.id)
					.then((rs: any) => {
						if (!rs.data) {
							ElMessage.error(rs.resultMsg);
							return;
						}
						//assgintoNameInit();
						onInit();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg);
					});
			});
		};

		//批量删除
		const onDeleteByList = () => {
			ElMessageBox.confirm(
				t('message.page.dlgDeleteSelectText1') + ` ${state.tableData.selection.length} ` + t('message.page.dlgDeleteSelectText2'),
				t('message.page.dlgTip'),
				{
					type: 'warning',
					closeOnClickModal: false,
				}
			).then(() => {
				let deleteIds = [];

				state.tableData.selection.forEach((element) => {
					deleteIds.push(element.id);
				});

				cmTicketsApi.TicketDelete({ DeleteIds: deleteIds.join(',') }).then((rs) => {
					if (rs.data == 0) {
						ElMessage.error(rs.resultMsg);
						return;
					}
					//assgintoNameInit();
					onInit();
				});
			});
		};

		const onSortChange = (column: any) => {
			state.tableData.params.order = column.prop;
			state.tableData.params.sort = column.order;
			// assgintoNameInit();
			onInit();
		};

		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};

		//勾选全选的话，会把子工单也全都选中
		let checkedAllKeys = false;
		const selectAllList = () => {
			checkedAllKeys = !checkedAllKeys;

			state.tableData.data.forEach((row) => {
				row.isSelect = checkedAllKeys;
				proxy.$refs.ticketTableRef.toggleRowSelection(row, checkedAllKeys);

				if (row.children != null && row.children.length > 0) {
					row.children.forEach((row) => {
						proxy.$refs.ticketTableRef.toggleRowSelection(row, checkedAllKeys);
						row.isSelect = checkedAllKeys;
					});
				}
			});
		};

		const selectFun = (selection, row) => {
			proxy.$nextTick(() => {
				if (row.isSelect == 'half') {
					row.isSelect = false;
					proxy.$refs.ticketTableRef.toggleRowSelection(row, true);
				}
				row.isSelect = !row.isSelect;

				if (row.children != null && row.children.length > 0) {
					row.children.forEach((item) => {
						item.isSelect = true;
						proxy.$refs.ticketTableRef.toggleRowSelection(item, true);
					});
				} else {
					let parentId = row.parentId;
					state.tableData.data.forEach((item) => {
						let isAllSelect = [];
						if (item.id == parentId) {
							if (item.children != null) {
								item.children.forEach((element) => {
									isAllSelect.push(element.isSelect);
								});
							}

							if (
								isAllSelect.every((selectItem) => {
									return true == selectItem;
								})
							) {
								item.isSelect = true;
								proxy.$refs.ticketTableRef.toggleRowSelection(item, true);
							} else if (
								isAllSelect.every((selectItem) => {
									return false == selectItem;
								})
							) {
								item.isSelect = false;
								proxy.$refs.ticketTableRef.toggleRowSelection(item, false);
							} else {
								item.isSelect = 'half';
							}
						}
					});
				}
			});
		};

		const onPrint = () => {
			print(printMain.value);
		};

		const onSizechange = (val: number) => {
			state.tableData.params.pageSize = val;
			// assgintoNameInit();
			onInit();
		};

		const onCurrentChange = (val: number) => {
			state.tableData.params.pageIndex = val;
			// assgintoNameInit();
			onInit();
		};

		const initFormData = () => {
			state.tableData.params.assignedToMeOld = true;
			state.tableData.params = {
				groupId: '' as any,
				ticket_Parent_Id: 0,
				ticket_Status: '' as any,
				ticket_Number: '',
				ticket_Subject: '',
				ticket_Priority: '',
				ticket_Status_List: ["New", "Open", "Replied", "Hold "],
				ticket_Priority_List: [],
				ticketContentType_List: [],
				ticket_Category_List: [],
				ticket_Customer_List: [],
				BusinessUnitList: [],
				ticket_Customer_ListV2: [],
				assignToSearchList: [],
				userDelegateName: '',
				ticket_From: '',
				pageIndex: 1,
				pageSize: 20,
				searchKey: state.tableData.params.assignedToMeOld ? 's' : '',
				startTime: '',
				endTime: '',
				startModified: '',
				endModified: '',
				assigToId: '' as any,
				order: 'Ticket_Number',
				sort: 'desc', // asc or desc
				onHold: false,
				startScheduled: '',
				endScheduled: '',
				assignedToMe: state.tableData.params.assignedToMeOld,
				assignedToMeOld: state.tableData.params.assignedToMeOld,
			} as any;
			state.statusCheckAll = false;
			state.categoryCheckAll = false;
			state.customerCheckAll = false;
			state.priorityCheckAll = false;
			state.projectsCheckAll = false;
			state.ticketTypeCheckAll = false;
			state.assignedToCheckAll = false;
			initTableDataparams();
		};

		const initTableDataparams = () => {
			state.tableData.topParams = {
				groupId: '' as any,
				ticket_Status: '' as any,
				assigToId: '' as any,
				ticket_From: '',
			} as any;

			// if (state.tableData.params.ticket_Status_List.length > 0) {
			// 	state.tableData.params.ticket_Status = state.tableData.params.ticket_Status_List[0];
			// }

			//清空 所有统计按钮的 选中状态
			state.buttonSelected = {};

			//默认使用params的参数进行查询 1：表示使用topParams来查询
			state.tableData.paramsType = 0;
		};

		const onExpandChange = async (row, expanded) => {
			if (expanded.length > 0) {
				var params = {
					Ticket_Parent_Id: row.id,
				};

				let result = await cmTicketsApi.Query(params);
				row.children = result.data;
			}
		};

		const nodeClick = (data, node) => { };

		// 页面加载时
		onMounted(async () => {
			assgintoNameInit();
			onInit('Yes');

			//事件监听器
			mittBus.on('RefreshTicketList', () => {
				assgintoNameInit();
				onInit();
			});
		});

		// 页面加载前
		onBeforeMount(() => { });

		// 页面销毁时
		onUnmounted(() => {
			if (!state.disalogList) {
				mittBus.off('RefreshTicketList');
			}
		});

		const onExportAllRecord = (selectAll: number) => {
			let currPageSize = state.tableData.params.pageSize;
			state.tableData.params.pageSize = 10000;

			var checkSelection = state.tableData.selection;
			let ids_arr = [] as any;

			let haveChildrenKey = false;

			checkSelection.forEach(function (item, index) {
				let idjson = {} as any;

				if (item.ticket_Parent_Id > 0) {
					haveChildrenKey = true;
				}

				idjson = item.id;
				ids_arr.push(idjson);
			});

			if (checkedAllKeys) {
				selectAll = 0;
			}

			state.tableData.params.ischeckType = selectAll;

			if (selectAll == 1 && ids_arr.length == 0) {
				ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
				return;
			}

			let clearSearchKey = false;

			if (selectAll == 0) {
				state.tableData.params.ids = [];
			} else {
				if (haveChildrenKey) {
					state.tableData.params.removeTicket = 'r';

					if (state.tableData.params.searchKey == '') {
						clearSearchKey = true;
						state.tableData.params.searchKey = 's';
					}

					selectAll = 0;
				}

				state.tableData.params.ischeckType = selectAll;

				state.tableData.params.ids = ids_arr;
			}

			cmTicketsApi
				.Export(state.tableData.params)
				.then((rs) => {
					downloadCallback(rs);
				})
				.catch((rs) => { })
				.finally(() => {
					state.tableData.loading = false;

					if (clearSearchKey) {
						state.tableData.params.searchKey = '';
					}

					state.tableData.params.removeTicket = '';
				});

			state.tableData.params.pageSize = currPageSize;
		};

		const downloadCallback = (rs) => {
			let data = rs;
			var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
			var anchor = document.createElement('a');
			anchor.download = 'Ticket_' + formatDateTime() + '.xlsx';
			anchor.href = window.URL.createObjectURL(newBlob);
			anchor.click();
		};

		const openTicketCommentView = (ticketNumber: String) => {
			state.showCommentID = ticketNumber;
			state.showCommentView = true;
		};

		const closedComment = () => {
			state.showCommentView = false;
			state.showCommentID = '0';
		};

		const getTicketAttachments = (id: number) => {
			let req: any = { ticketId: id };
			cmTicketAttachment
				.Query(req)
				.then((rs) => {
					//downloadCallback(rs);
					state.ticketAttachments = rs.data;
				})
				.catch((rs) => { })
				.finally(() => {
					state.tableData.loading = false;
					//重新计算位置
					//proxy.$nextTick(() => {
					//	proxy.$refs['popoverFilldRef'].updatePopper();
					//});
				});
		};

		const downloadFile = (row: any) => {
			request({
				url: `${BASE_URL.value}api/files/ticketFile/${row.id}`,
				method: 'get',
				responseType: 'blob'
			})
				.then((response: any) => {
					// 创建 Blob URL
					const blob = new Blob([response], { type: response.type || 'application/octet-stream' });
					const url = window.URL.createObjectURL(blob);

					// 创建下载链接
					const link = document.createElement('a');
					link.style.display = 'none';
					link.href = url;
					link.download = row.attachment_Original_File_Name; // 可以根据需要设置文件名

					// 触发下载
					document.body.appendChild(link);
					link.click();

					// 清理
					document.body.removeChild(link);
					window.URL.revokeObjectURL(url);
				})
				.catch((error: any) => {
					console.error('Download failed:', error);
					// 可以添加用户友好的错误提示
					ElMessage.error('Download failed');
				});
		};

		const handleFilePreview = (attachments: any, row: any) => {
			if (compareImg(row.attachment_File_Ext)) {
				// 过滤出图片类型的附件
				previewSrcList.value = attachments.filter((item: any) => compareImg(item.attachment_File_Ext)).map((item: any) => `${BASE_URL.value}` + item.attachment_File_Name)
				// 设置预览图片的索引
				previewIndexRef.value = previewSrcList.value.indexOf(`${BASE_URL.value}` + row.attachment_File_Name);
				// 显示图片预览
				previewVisibleRef.value = true;
			} else {
				fileData.value = row;
				// 显示文件对话框
				ticketFileDialogRef.value.openDialog(row);
				// window.open(`${BASE_URL.value}`+row.attachment_File_Name,'_blank');
			}
		};
		const previewFile = (type: string) => {
			// 判断是否为图片类型的附件
			const fileTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp3', 'mp4'];
			return fileTypes.includes(type.toLowerCase());
		};
		//判断类型为图片的方法
		const compareImg = (type: string) => {
			const imageTypes = ['bmp', 'jpg', 'png', 'gif', 'svg', 'webp', 'ico', 'jpeg'];
			return imageTypes.includes(type.toLowerCase());
		};

		return {
			printMain,
			formatStrDate,
			formatDateTime,
			onPrint,
			createOrEditRef,
			selectionChange,
			selectAllList,
			onInit,
			assgintoNameInit,
			loadChildrenTickets,
			onAdd,
			onEdit,
			onDelete,
			onDeleteByList,
			onSizechange,
			onCurrentChange,
			onSearch,
			onSearchReSet,
			onAssignedToMe,
			onSortChange,
			setQuery,
			filterNodeMethod,
			rowClick,
			...toRefs(state),
			currentUser,
			onExpandChange,
			onExportAllRecord,
			nodeClick,
			rowExpandHandler,
			selectFun,
			openTicketCommentView,
			closedComment,
			ticketEditVisit,
			route,
			getTicketAttachments,
			downloadFile,
			handleFilePreview,
			previewFile,
			previewVisibleRef,
			previewIndexRef,
			previewSrcList,
			ticketFileDialogRef,
			fileData,
			popoverFilldRef,
			handleStatusCheckAll,
			handleStatusCheckChange,
			handleCategoryCheckAll,
			handleCategoryCheckChange,
			handlePriorityCheckAll,
			handlePriorityCheckChange,
			handleCustomerCheckAll,
			handleCustomerCheckChange,
			handleProjectsCheckAll,
			handleProjectsCheckChange,
			handleAssignedToCheckAll,
			handleAssignedToCheckChange,
			handleTicketTypeCheckAll,
			handleTicketTypeCheckChange,
			handleBusinessUnitCheckAll,
			handleBusinessUnitCheckChange,

		};
	},
});
</script>

<style scoped>
.el-date-picker__header {
	display: flex;
	align-items: center;
}

.el-date-picker__header span {
	flex: 1;
}

.el-date-picker__header> :nth-child(1) {
	order: 1;
}

.el-date-picker__header> :nth-child(2) {
	order: 3;
}

.el-date-picker__header> :nth-child(3) {
	order: 2;
}

.el-date-picker__header> :nth-child(4) {
	order: 4;
}

:deep(.icon-no .el-table__expand-icon) {
	display: none;
}

:deep(td.el-table__cell.el-table__expanded-cell) {
	padding: 0px;
}

.tags-select-input {
	height: 30px;
}

.tag-select-input /deep/ .el-select__tags {
	height: 30px;
	white-space: nowrap;
	overflow: hidden;
	flex-wrap: nowrap;
}

.tag-select-input /deep/ .el-select__tags-text {
	display: inline-block;
	max-width: 85px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.comment-count {
	margin-top: 10px;
	margin-right: 40px;
	cursor: pointer;
}
</style>

<style scoped lang="scss">
.list-index-search {
	.date-container {
		display: flex;
		align-items: center;
	}

	:deep(.el-text) {
		margin: 0 10px;
	}
}

:deep(.el-date-editor.el-input) {
	flex: 1;
	width: calc(50% - 20px);
}

::v-deep .el-image-viewer__close {
	font-size: 24px !important;
	/* 调整图标大小 */
}
</style>
