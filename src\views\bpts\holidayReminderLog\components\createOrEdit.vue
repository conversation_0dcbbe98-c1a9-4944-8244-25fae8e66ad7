﻿<template>
	<div class="holidayReminderLog-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px" >
				<el-row :gutter="35">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="IsDeleted" prop="IsDeleted">
							<el-radio-group v-model="ruleForm.IsDeleted">
								<el-radio :label="true">Enable</el-radio>
								<el-radio :label="false">Disable</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createdById" prop="createdById">
                            <el-input-number v-model="ruleForm.createdById" :min="0" class="w100"/>
						</el-form-item>
					</el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="createdAt" prop="createdAt">
							<el-date-picker v-model="ruleForm.createdAt" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="modifiedById" prop="modifiedById">
                            <el-input-number v-model="ruleForm.modifiedById" :min="0" class="w100"/>
						</el-form-item>
					</el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="modifiedAt" prop="modifiedAt">
							<el-date-picker v-model="ruleForm.modifiedAt" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
                     <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
      <el-form-item label="cmTicketId" prop="cmTicketId">
        <el-input v-model="ruleForm.cmTicketId" placeholder="Please input " />
      </el-form-item>
                 </el-col>    
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="holiday" prop="holiday">
							<el-date-picker v-model="ruleForm.holiday" type="date" placeholder="Pick a day" class="w100"> </el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="select">
							<el-cascader :options="deptData" :props="{ checkStrictly: true, value: 'deptName', label: 'deptName' }" placeholder="select" clearable class="w100" v-model="ruleForm.department">
								<template #default="{ node, data }">
									<span>{{ data.deptName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">Cannel</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="default" type="danger">Reset</el-button>
					<el-button v-if="dialogParams.action==='Edit'" @click="onDelete" type="danger" size="default">Delete</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="default">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs,getCurrentInstance,defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import holidayReminderLogApi from '/@/api/holidayReminderLog';

interface DialogParams {
	action: string;
    id:number;
}

export default defineComponent({
	name: 'holidayReminderLogCreateOrEdit',
	components: {},
    setup(props, context) {
        const { proxy } = getCurrentInstance() as any;
		const state = reactive({
            title:'Create HolidayReminderLog',
			isShowDialog: false,
			saveLoading: false,
            deleteLoading: false,
            dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
                 id: 0,// 
            isDeleted: false,// 
                 createdById: 0,// 
            createdAt: new Date(),// 
                 modifiedById: 0,// 
            modifiedAt: new Date(),// 
                 cmTicketId: 0,// 
            holiday: new Date(),// 
            },
			rules: {
            id: [{ required: true, message: 'Please input', trigger: 'blur' }],
            isDeleted: [{ required: true, message: 'Please input', trigger: 'blur' }],
            createdById: [{ required: true, message: 'Please input', trigger: 'blur' }],
                createdAt: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
            modifiedById: [{ required: true, message: 'Please input', trigger: 'blur' }],
                modifiedAt: [{ type: 'date', required: true, message: 'Pick date', trigger: 'change' }],
            cmTicketId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				
				//type1: [{ type: 'array', required: true, message: '', trigger: 'change' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
        
			state.dialogParams = parmas;
            
			onInitForm();
           
			if (parmas.action == 'Create') {
				state.title = 'Create holidayReminderLog';
			} else if (parmas.action == 'Edit') {
                state.title = 'Edit HolidayReminderLog';
				getData(parmas.id);
			}else{
                ElMessage.error("Parameter action cannot be empty.");
            }
		};
		// 关闭弹窗
		const closeDialog = () => {
            onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
                if (!valid) {return;}
				var obj = state.ruleForm;
				state.saveLoading = true;
				holidayReminderLogApi.Save(obj)
					.then(() => {
                        ElMessage.success("Succeed");
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
        //根据id获取完整的信息
		const getData = (id: any) => {
			holidayReminderLogApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
			});
		};
		//删除纪录
		const onDelete = () => {

			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No，Thanks',
				type: 'warning',
			}).then(() => {
                state.deleteLoading = true;
				holidayReminderLogApi.DeleteByKey(state.ruleForm.id)
					.then(() => {
                        ElMessage.success("Succeed")
						context.emit('fetchData');
                        closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
                    .finally(() => {
						state.deleteLoading = false;
					});
			});
		};
        // 重置表单
		const onInitForm = () => {
            state.isShowDialog=true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
           id: 0,// 
            isDeleted: false,// 
           createdById: 0,// 
            createdAt: new Date(),// 
           modifiedById: 0,// 
            modifiedAt: new Date(),// 
           cmTicketId: 0,// 
            holiday: new Date(),// 
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>



                        
        
        