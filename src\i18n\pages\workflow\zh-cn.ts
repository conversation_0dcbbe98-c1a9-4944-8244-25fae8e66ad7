export default {
	workflow_btnArea : {
        search : '查询',
        create : '创建',

        edit : '编辑',
        delete : '删除',
        more : '更多',
        disable : '禁用流程',
    },
    workflow_msgArea : {
        missingStartNode : '必须包含开始节点.',
        success : '发布成功',
        fail : '发布失败',
    },
    workflow_placeholder : {
        description : '请输入'
    },
    workflow_labelArea : {
        keywords : '关键字',
        type : '流程分类',
        enable : '启用',
        disable : '禁用',
        workflowDesign : '流程设计',
        information : '基础信息',
        back : '上一步',
        next : '下一步',
        save : '保存',
        cancel : '取消',
        clickImageToUpload : '点击选择图片',
        invoiceInfo : '发票录入',
        requireInfo : '请购单',
        ticketInfo : '工单录入',
    },
    workflow_fieldArea : {
        workflowName : '流程名称',
        workflowCode : '流程编码',
        type : '所属分类',
        createdByName : '创建人',
        createdAt : '创建时间',
        status : '状态',
        operation : '操作',
        businessTable : '业务表',
        sort : '流程排序',
        description: '流程说明',
        flowChart : '流程图',
    }
    
};
