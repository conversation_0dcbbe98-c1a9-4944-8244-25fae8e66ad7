<template>
	<div class="approval-setting">
		<el-form label-position="top" v-model="form" :rules="formRules">
			<el-tabs v-model="activeName" class="demo-tabs">
				<el-tab-pane label="基础设置" name="base">
					<el-form-item label="节点名称" class="form-lable">
						<el-input v-model="form.name" placeholder="Please input node name"></el-input>
					</el-form-item>
					<el-form-item label="显示名称" class="form-lable">
						<el-input v-model="form.displayName" placeholder="Please input node name"></el-input>
					</el-form-item>
					<el-form-item label="节点类型" class="form-lable">
						<el-select v-model="form.activityType" class="form-select" placeholder="Select" size="large" clearable>
							<el-option v-for="item in activityTypeOptions" :key="item" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="触发方式" class="form-lable" v-if="false">
						<el-select v-model="form.execute.mode" class="form-select" placeholder="Select" size="large" clearable>
							<el-option label="SignalReceived" value="SignalReceived" />
						</el-select>
					</el-form-item>
					<el-form-item label="触发器名称" class="form-lable" v-if="false">
						<el-input v-model="form.execute.name" placeholder="Please input node name"></el-input>
					</el-form-item>
					<el-form-item label="审批方式" class="form-lable">
						<el-radio-group v-model="form.autoApproval" class="ml-4" @change="changeReturnType">
							<el-radio :label="0" size="large">默认</el-radio>
							<el-radio :label="1" size="large">自动通过</el-radio>
						</el-radio-group>
					</el-form-item>
					<div v-if="type === 'ApprovalNode' && form.autoApproval == 0">
						<el-form-item label="审批人设置" class="form-lable">
							<el-radio-group v-model="form.approvalType" @change="changeApprovalType">
								<el-radio :label="1" size="large">指定成员</el-radio>
								<el-radio :label="2" size="large">发起人本人</el-radio>
								<el-radio :label="3" size="large">部门主管</el-radio>
								<el-radio :label="4" size="large">表单变量</el-radio>
								<el-radio :label="5" size="large">调用接口</el-radio>
							</el-radio-group>
						</el-form-item>
						<div v-if="showApprovalSelect" class="mb20">
							<el-form-item v-for="item in approverSettings" :key="item.type" class="form-lable">
								<el-select
									v-model="approvalValue[item.type]"
									multiple
									ref="selectRef"
									:placeholder="item.placeholder"
									style="width: 100%"
									@focus="onVisible(item.type, item.id)"
									@remove-tag="(val) => onRemoveTag(item.type, val)"
								>
									<el-option v-for="opt in form.approvalData[item.type]" :key="opt.id" :label="opt.name" :value="opt.id" />
								</el-select>
							</el-form-item>
						</div>
						<div v-if="showApprovalForm" class="mb20">
							<el-form-item class="form-lable">
								<el-select v-model="form.approvalDataForm" placeholder="Please select" style="width: 100%">
									<el-option v-for="item in formPropertys" :label="item.name" :value="item.name" :key="item.name"></el-option>
								</el-select>
							</el-form-item>
						</div>
						<div v-if="showApprovalUrlForm" class="mb20">
							<el-form-item class="form-lable">
								<el-input v-model="form.approvalUrl" placeholder="Please input"></el-input>
								<el-select v-model="form.approvalUrlForm" multiple placeholder="Please select" style="width: 100%; margin-top: 10px">
									<el-option v-for="item in formPropertys" :label="item.name" :value="item.name" :key="item.name"></el-option>
								</el-select>
							</el-form-item>
						</div>
						<el-form-item label="审批人选择" class="form-lable">
							<el-radio-group v-model="form.approvalFilter" class="ml-4">
								<el-radio :label="1" size="large">允许选择</el-radio>
								<el-radio :label="0" size="large">不允许选择</el-radio>
							</el-radio-group>
						</el-form-item>
						<el-form-item label="审批方式" class="form-lable">
							<el-radio-group v-model="form.approvalMethod" class="ml-4">
								<el-radio label="WaitAny" size="large">或签（一名审批人同意或退回即可）</el-radio>
								<el-radio label="WaitAll" size="large">会签（所有审批人同意或退回）</el-radio>
							</el-radio-group>
						</el-form-item>
						<el-form-item label="退回设置" class="form-lable">
							<el-radio-group v-model="form.returnType" class="ml-4" @change="changeReturnType">
								<el-radio :label="1" size="large">退回上级</el-radio>
								<el-radio :label="2" size="large">重新审批</el-radio>
								<el-radio :label="3" size="large">自定义退回节点</el-radio>
							</el-radio-group>
						</el-form-item>
						<el-form-item v-if="form.returnType === 3" class="form-lable">
							<el-select v-model="form.returnNodeId" placeholder="Select" multiple filterable clearable class="w100">
								<el-option v-for="item in nodeOptions" :label="item.text" :value="item.id" :key="item.id"></el-option>
							</el-select>
						</el-form-item>
					</div>
				</el-tab-pane>
				<el-tab-pane label="高级设置" name="advanced">
					<el-form-item label="操作设置" class="form-lable">
						<el-row v-for="item in form.permissions" :key="item.permissionType" class="per-cell">
							<el-col :span="4">
								<el-checkbox :label="item.permissionTypeName" v-model="item.enable" />
							</el-col>
							<el-col :span="20">
								<el-input v-model="item.permissionText" placeholder="Please input" />
							</el-col>
						</el-row>
					</el-form-item>
				</el-tab-pane>

				<el-tab-pane label="节点通知" name="cc">
					<el-form-item v-for="cfg in emailNotices" :key="cfg.noticeType" class="form-lable">
						<el-card class="email-card">
							<template #header>
								<el-row style="width: 100%">
									<el-col :span="18">
										<span>{{ cfg.label }}</span>
									</el-col>
									<el-col :span="6">
										<el-select
											v-model="approveNoticeValue[cfg.noticeType]"
											class="form-select"
											placeholder="Select"
											size="small"
											@change="(val) => onNoticeTypeChange(cfg.noticeType, val)"
										>
											<el-option v-for="item in notificationOptions" :key="item.value" :label="item.label" :value="item.value" />
										</el-select>
									</el-col>
								</el-row>
							</template>
							<el-descriptions size="small" :column="3" border>
								<el-descriptions-item label="邮件模板" :span="4" v-if="approveNoticeValue[cfg.noticeType] === 2">
									<el-select
										v-model="templateValue[cfg.noticeType]"
										class="form-select"
										placeholder="Select"
										size="large"
										@change="(val) => onTemplateChange(cfg.noticeType, val)"
									>
										<el-option v-for="item in emailOptions" :key="item.templateId" :label="item.name" :value="item.templateId" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="收件人" :span="2" v-if="approveNoticeValue[cfg.noticeType] !== 3">
									<el-row style="width: 100%">
										<el-col :span="18">
											<el-badge :value="badgeValue(cfg.noticeType, 'appendTo')" class="item" type="primary">
												<el-button @click="onSetting(cfg.noticeType, 'appendTo')" size="small">Setting</el-button>
											</el-badge>
										</el-col>
										<el-col :span="6" style="padding-left: 10px"> </el-col>
									</el-row>
								</el-descriptions-item>
								<el-descriptions-item label="分开发送" :span="2" v-if="approveNoticeValue[cfg.noticeType] === 2">
									<el-select
										v-model="noticeSeparateSendValue[cfg.noticeType]"
										placeholder="Select"
										size="small"
										clearable
										@change="(val) => onSeparateSendChange(cfg.noticeType, val)"
									>
										<el-option label="按用户类型" :value="1" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="抄送人" :span="4" v-if="approveNoticeValue[cfg.noticeType] !== 3">
									<el-row style="width: 100%">
										<el-col :span="18">
											<el-badge :value="badgeValue(cfg.noticeType, 'appendCc')" class="item" type="primary">
												<el-button @click="onSetting(cfg.noticeType, 'appendCc')" size="small">Setting</el-button>
											</el-badge>
										</el-col>
										<el-col :span="6" style="padding-left: 10px"> </el-col>
									</el-row>
								</el-descriptions-item>
							</el-descriptions>
						</el-card>
					</el-form-item>
				</el-tab-pane>
			</el-tabs>
		</el-form>
		<div style="margin: 20px">
			<el-button size="small" @click="onClosed">Cancel</el-button>
			<el-button type="primary" size="small" @click="onSubmit">Save</el-button>
		</div>
		<ApproverDlg ref="approverDlgRef" @fetchData="fetchSelectData" />
		<SelectTempDlg ref="selectTempDlgRef" @fetchData="fetchEmailSelectData" />
		<MailSettings ref="mailSettingsRef" @fethData="setNoticeAppend" />
		<Recipient ref="recipientRef" @fethData="setNoticeAppend" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref, onMounted } from 'vue';
import { emailNotices } from '/@/components/logicFlow/config.js';

import ApproverDlg from './ApproverDlg.vue';
import Recipient from './Recipient.vue';
import MailSettings from './MailSettings.vue';
import SelectTempDlg from '/@/views/bpts/emails/components/selectTempDlg.vue';
import workflowCrmApi from '/@/api/workflowCrm/index';
import emailTemplateApi from '/@/api/emailTemplate/index';

export default defineComponent({
	name: 'approvalSetting',
	props: {
		nodeData: Object,
		lf: Object || String,
		entity: String,
	},
	components: { ApproverDlg, SelectTempDlg, MailSettings, Recipient },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const recipientRef = ref();
		const mailSettingsRef = ref();
		const approverDlgRef = ref();
		const selectTempDlgRef = ref();
		const selectRef = ref();
		const state = reactive({
			type: '',
			approverDlgType: '', //审批人操作类型
			approverDlgData: '',
			activeName: 'base',
			showApprovalSelect: true,
			showApprovalForm: false,
			showApprovalUrlForm: false,
			approvalValue: {},
			templateValue: {} as any,
			noticeSeparateSendValue: {} as any,
			approveNoticeValue: {} as any, //通知操作对应的选中项
			currentNoticeType: '', //当前操作哪个通知 是 同意 还是 取消 还是 转办
			returnType: 1, //退回方式
			form: {
				name: '', //节点名称
				displayName: '',
				activityType: '', //自定义Elsa Activity
				approvalType: 1, //审批人类型
				approvalData: {}, //审批人
				approvalDataForm: '',
				approveNoticeData: {},
				approvalMethod: 'WaitAny', //审批方式
				approvalFilter: 0,
				returnNodeId: null, //退回节点
				returnType: 1,
				autoApproval: 0, //审批方式
				execute: {},
				approveNotice: '',
				returnNotice: '',
				assignToNotice: '',
				rejectNotice: '',
				timedoutNotice: '',
				cc: { users: [], roles: [], departments: [], organizations: [] },
				timedout: {
					approval: '0', //超时自动审批
					configType: '',
					hours: 0,
					notice: '',
					noticeEmail: '',
				},
			} as any,
			formRules: {
				name: [{ required: true, message: 'Please input node name.', trigger: 'blur' }],
			},
			activityTypeOptions: [],
			notificationOptions: [
				{
					value: 0,
					label: '同步开始设置',
				},
				{
					value: 1,
					label: '系统默认',
				},
				{
					value: 2,
					label: '自定义',
				},
				{
					value: 3,
					label: '关闭',
				},
			],

			noticeFormItems: [
				{ action: 'approval', name: '节点同意', value: 'default' },
				{ action: 'return', name: '节点退回', value: 'default' },
				{ action: 'assignTo', name: '节点转审', value: 'default' },
			],
			approverSettings: [
				{ type: 'User', label: '用户', placeholder: '请选择人员' },
				{ type: 'Department', label: '部门', placeholder: '请选择部门' },
				{ type: 'Role', label: '角色', placeholder: '请选择角色' },
				{ type: 'Position', label: '岗位', placeholder: '请选择岗位' },
			],
			nodeOptions: [],
			formPropertys: [],
			emailOptions: [],
		});

		const onSetting = (dataType: string, appendType: any) => {
			const approveNoticeData = props.nodeData.properties.approveNoticeData[dataType];
			recipientRef.value.openDialog({ dataType, className: props.entity, appendType, noticeData: approveNoticeData, nodeOptions: state.nodeOptions });
		};

		const setNoticeAppend = (dataType: string, items: any, appendType: any) => {
			items.forEach((item) => {
				if (item.source !== '1') {
					delete item['sourceOption']; //删除多余的属性
				}
			});
			state.form.approveNoticeData[dataType][appendType] = items;
			console.log('state.form.approveNoticeData', state.form.approveNoticeData);
		};

		const badgeValue = (noticeType: string, appendType: string) => {
			return state.form.approveNoticeData[noticeType]?.[appendType]?.length || '';
		};
		const onSubmit = () => {
			const nodeData = props.nodeData;
			nodeData.properties = state.form;
			const { id } = props.nodeData;
			props.lf.updateText(id, state.form.displayName);
			props.lf.setProperties(nodeData.id, state.form);
			context.emit('onClose');

			console.log('form', state.form);
		};

		const onClosed = () => {
			context.emit('onClose');
		};

		const changeApprovalType = () => {
			state.showApprovalSelect = false;
			state.showApprovalForm = false;
			state.showApprovalUrlForm = false;
			if (state.form.approvalType !== 1) {
				state.form.approvalData = {};
			}
			if (state.form.approvalType === 1) {
				state.showApprovalSelect = true;
			}
			if (state.form.approvalType === 4) {
				state.showApprovalForm = true;
				workflowCrmApi.GetEntityProperties({ className: props.entity }).then((rs) => {
					state.formPropertys = rs.data;
				});
			}
			if (state.form.approvalType === 5) {
				state.showApprovalUrlForm = true;
				workflowCrmApi.GetEntityProperties({ className: props.entity }).then((rs) => {
					state.formPropertys = rs.data;
				});
			}
		};

		const onVisible = (dataType: string, id: string) => {
			//使选择器的输入框失去焦点，并隐藏下拉框
			selectRef.value.forEach((el) => el.blur());
			state.approverDlgType = dataType;
			state.approverDlgData = state.approvalValue[dataType];
			// 根据 value 的不同，可以在这里设置不同的对话框内容
			approverDlgRef.value.setDialogParams({ multiple: true, dataType, initData: state.approverDlgData });
		};

		const onNoticeTypeChange = (dataType: string, val: number) => {
			//console.log("state.form.approveNoticeData[dataType]",state.form.approveNoticeData[dataType])
			state.form.approveNoticeData[dataType]['value'] = val;
			state.currentNoticeType = dataType;
			if (val === 2) {
				//自定义
				//selectTempDlgRef.value.openDialog();
			}
		};

		const onSeparateSendChange = (dataType: string, val: number) => {
			state.form.approveNoticeData[dataType]['separateSendType'] = val;
		};

		const onTemplateChange = (dataType: string, val: number) => {
			state.form.approveNoticeData[dataType]['valueData'] = { id: val };
			//console.log('state.form.approveNoticeData[dataType]', state.form.approveNoticeData[dataType]);
		};

		const getEmailOptions = () => {
			emailTemplateApi.GetList({}).then((rs) => {
				state.emailOptions = rs.data;
			});
		};

		const onEmailTagClose = (noticeType: string) => {
			state.form.approveNoticeData[noticeType] = { value: 0 };
			state.approveNoticeValue[noticeType] = 0;
		};

		const onRemoveTag = (type: string, val: number) => {
			state.form.approvalData[type] = state.form.approvalData[type].filter((obj) => obj.id !== val);
		};

		const fetchSelectData = (dataType: any, items: any) => {
			state.form.approvalData[dataType] = items;
			state.approvalValue[dataType] = items.map((a) => {
				return a.id;
			});
		};

		const fetchEmailSelectData = (row: any) => {
			state.form.approveNoticeData[state.currentNoticeType].valueData = { id: row.templateId, name: row.name };
		};

		const changeReturnType = (v) => {};

		//获取可退回的节点
		const loadGraphNodes = (id: any) => {
			var arr = [] as any[];
			var graphData = props.lf.getGraphData();
			const nodes = graphData.nodes.filter((item) => item.id !== id);
			nodes.forEach((node) => {
				if (node.type === 'StartNode' || node.type === 'ApprovalNode') {
					let txt = node.properties.name;
					if (node.text && node.text.value) {
						txt = node.text.value;
					}

					arr.push({
						id: node.id,
						type: node.type,
						name: node.properties.name,
						text: txt,
					});
				}
			});
			return arr;
		};

		onMounted(() => {
			const { properties, text, type, id } = props.nodeData;
			var graphData = props.lf.getGraphData();
			const nodeTypes = ['ApprovalNode', 'StartNode'];

			if (type === 'StartNode') {
				state.notificationOptions = [
					{
						value: 1,
						label: '系统默认',
					},
					{
						value: 2,
						label: '自定义',
					},
					{
						value: 3,
						label: '关闭',
					},
				];
			}

			if (properties) {
				console.log(properties);
				state.form = Object.assign({}, state.form, properties);

				//设置审批人下拉框选中
				if (properties.approvalData) {
					for (const type in state.form.approvalData) {
						state.approvalValue[type] = state.form.approvalData[type].map((item) => item.id);
					}
				}

				//设置节点通知的选中
				if (properties.approveNoticeData) {
					console.log('properties.approveNoticeData', properties.approveNoticeData);
					for (const type in state.form.approveNoticeData) {
						state.approveNoticeValue[type] = state.form.approveNoticeData[type].value;
						if (state.form.approveNoticeData[type].valueData) {
							state.templateValue[type] = state.form.approveNoticeData[type].valueData.id;
						}
						if (state.form.approveNoticeData[type].separateSendType) {
							state.noticeSeparateSendValue[type] = state.form.approveNoticeData[type].separateSendType;
						}
					}
				}
			}
			if (text && text.value) {
				state.form.displayName = text.value;
			}
			state.type = type;

			workflowCrmApi.GetActivityTypes({}).then((rs) => {
				let options = rs.data;
				let defaultOption = '默认';
				options.unshift(defaultOption);
				let optionsArray = options.map((x: string) => (x === '默认' ? { label: x, value: '' } : { label: x, value: x }));
				state.activityTypeOptions = optionsArray;
			});

			state.nodeOptions = loadGraphNodes(id);
			//console.log("state.manualActivitys",state.manualActivitys);

			changeApprovalType();

			getEmailOptions();

			console.log(state.form);
		});

		return {
			approverDlgRef,
			selectTempDlgRef,
			mailSettingsRef,
			recipientRef,
			selectRef,
			badgeValue,
			onVisible,
			fetchSelectData,
			onSubmit,
			onClosed,
			onRemoveTag,
			onNoticeTypeChange,
			onEmailTagClose,
			changeApprovalType,
			changeReturnType,
			emailNotices,
			setNoticeAppend,
			fetchEmailSelectData,
			onTemplateChange,
			onSeparateSendChange,
			onSetting,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.approval-setting {
	.form-lable {
		font-weight: bold;
	}
	.per-cell {
		width: 100%;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-bottom: 10px;
	}
	.form-select {
		width: 100%;
	}
	.form-item_card {
		width: 100%;
	}

	.text-gray-600 {
		--un-text-opacity: 1;
		color: rgba(75, 85, 99, var(--un-text-opacity));
	}
	.items-center {
		align-items: center;
	}
	.inline-flex {
		display: inline-flex;
	}
	.w-35 {
		width: 8.75rem;
	}
	.ml-3 {
		margin-left: 0.75rem;
	}

	.email-card {
		width: 100%;
	}
}
</style>
