<template>
	<div class="system-edit-category-container">
		<el-dialog :title="state.title" v-model="state.isShowDialog" width="800px" :close-on-click-modal="false"
			draggable destory-on-close>
			<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" label-width="150px"
				label-position="top">
				<el-row :gutter="35">
					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.project_Company')" prop="project_Company">
							<el-tree-select v-model="state.ruleForm.project_Company"
								:placeholder="$t('message.page.selectKeyPlaceholder')" :data="state.customerOpts"
								:default-expand-all="true" checkStrictly style="width: 100%" clearable
								:props="{ value: 'orgId', label: 'name' }" filterable>
								<template #default="{ data }">
									<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
									<span>{{ data.name }}</span>
								</template>
							</el-tree-select>
						</el-form-item>
					</el-col>

					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.Project_Id')" prop="projectId">
							<el-input v-model="state.ruleForm.projectId" clearable />
						</el-form-item>
					</el-col>

					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.project_Name')" prop="project_Name">
							<el-input v-model="state.ruleForm.project_Name" clearable />
						</el-form-item>
					</el-col>

					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.project_Description')"
							prop="project_Description">
							<!-- <el-input v-model="state.ruleForm.project_Description" clearable /> -->
							<el-select v-model="state.ruleForm.project_Description" filterable clearable
								:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%">
								<el-option v-for="item in state.projectDescriptionData" :label="item.itemValue"
									:value="item.itemValue" :key="item.itemId" :description="item.description" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.project_Status')"
							prop="project_Status_Value">
							<el-select v-model="state.ruleForm.project_Status_Value"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100">
								<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
								<el-option :label="$t('message.userFields.Inactive')" :value="1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>



					<el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" class="mb6">
						<el-form-item :label="$t('message.cmProjectsFields.businessUnit')">
							<el-select v-model="state.ruleForm.businessUnit" filterable clearable
								:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%">
								<el-option v-for="item in state.businessUnitData"
									:label="item.description ? `${item.itemValue} - ${item.description}` : item.itemValue"
									:value="item.itemValue" :key="item.itemId" :description="item.description" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">

						<el-table :data="state.tableCategoryData" style="width: 100%" max-height="340px"
							scrollbar-always-on v-if="state.showTableCategoryData">
							<el-table-column prop="category" label="Category" class="w100">
								<template #default="scope">
									<el-select v-model="scope.row.category" filterable clearable
										:placeholder="$t('message.page.selectKeyPlaceholder')">
										<el-option v-for="item in state.CategoryData"
											:label="item.itemValue + ' - ' + item.description" :value="item.itemValue"
											:key="item.itemId" :description="item.description" />
									</el-select>
								</template>
							</el-table-column>

							<el-table-column prop="assignee" label="Default Assignee" class="w100">
								<template #default="scope">
									<el-tree-select :placeholder="$t('message.page.selectKeyPlaceholder')"
										ref="localTree" v-model="scope.row.assignee" :data="state.assigneeData"
										:render-after-expand="false" node-key="value" show-checkbox check-strictly
										check-on-click-node :auto-expand-parent="true" filterable clearable />
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="Operations" min-width="120">
								<template #default="scope">
									<el-button link type="primary" size="small"
										@click.prevent="onAddItem(scope.$index)">
										Add Item
									</el-button>

									<!-- v-if="scope.$index > 0" -->
									<el-button link type="danger" size="small" @click.prevent="deleteRow(scope.$index)">
										Remove
									</el-button>
								</template>
							</el-table-column>
						</el-table>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button :loading="state.loading" type="primary" @click="onSubmit" size="small">{{
						$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { reactive, toRefs, getCurrentInstance, ref, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import dictItemApi from '/@/api/dictItem/index';
import cmProjectsApi from '/@/api/cmProjects/index';
import organizationApi from '/@/api/organization/index';

interface DialogParams {
	action: string;
	id: number;
	companyId: number;
}

const emit = defineEmits(['fetchData']);

const { t } = useI18n();

const { proxy } = getCurrentInstance() as any;

const state = reactive({
	loading: false,
	title: t('message.cmProjectsButtons.createProject'),
	isShowDialog: false,
	DialogParams: {
		action: '',
		id: -1,
		companyId: -1,
	},
	ruleForm: {
		projectId: '',
		project_Name: '',
		project_Description: '',
		project_Status: 0,
		project_Status_Value: 0,
		project_Company: '',
		ActionType: '',
		oldProject_Company: 0,
		businessUnit: '' as any,
	} as any,
	rules: {
		projectId: [
			{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' },
			{
						validator: (rule: any, value: any, callback: any) => {
							if (/^.{1,}-.{3,}$/.test(value) == false ) {
								callback(ElMessage.error("It's required at least 3 characters after dash."));
							} else {
								callback();
							}
						},
						trigger:  'blur',
					},
		],
		project_Name: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
		project_Description: [{ required: true, message: t('message.page.searchKeyPlaceholder'), trigger: 'blur' }],
		project_Status_Value: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'change' }],
		project_Company: [{ required: true, message: t('message.page.selectKeyPlaceholder'), trigger: 'change' }],
	},
	customerOpts: [],
	oldProject_Company: 0,
	businessUnitData: [] as any,
	tableCategoryData: ref([]) as any,
	CategoryData: [] as any,
	assigneeData: [] as any,
	showTableCategoryData: ref(false),
	projectDescriptionData: [] as any,
});

const openDialog = async (params: DialogParams) => {
	await onInitForm();

	state.DialogParams = params;

	var dictArr = ['BusinessUnit', 'ProjectDescription'];
	dictItemApi.Many(dictArr).then((rs: any) => {
		state.businessUnitData = rs.data?.find((a: any) => {
			return a.dictValue === 'BusinessUnit';
		})?.items;

		state.projectDescriptionData = rs.data?.find((a: any) => {
			return a.dictValue === 'ProjectDescription';
		})?.items;
	});

	if (params.action == 'Create') {
		state.title = t('message.cmProjectsButtons.createProject');
	} else if (params.action == 'Edit') {
		state.title = t('message.cmProjectsButtons.editProject');

		getData(params.id, params.companyId, params.action);
	} else {
		ElMessage.error('Parameter action cannot be empty.');
	}
};

const getData = async (id: any, companyId: any, actionType: any) => {
	await cmProjectsApi.DetailV2(id, 'projectId').then((rs) => {
		state.oldProject_Company = rs.data.project_Company;

		state.ruleForm = Object.assign({}, rs.data);

		state.ruleForm.ActionType = actionType;

		state.ruleForm.oldProject_Company = state.oldProject_Company;

		processResultData(rs);

		state.showTableCategoryData = true;
	});
};

const onInitForm = async () => {
	state.isShowDialog = true;
	state.showTableCategoryData = false;
	state.tableCategoryData = []

	if (proxy.$refs.ruleFormRef) {
		proxy.$refs.ruleFormRef.resetFields();
	}

	state.ruleForm = {
		projectId: '',
		project_Name: '',
		project_Description: '',
		project_Status: 0,
		project_Status_Value: 0,
		project_Company: '',
		ActionType: '',
		oldProject_Company: 0,
		businessUnit: '',
	};

	await organizationApi.Tree({}).then((rs) => {
		state.customerOpts = rs.data;
	});
};

const onCancel = () => {
	closeDialog();
};

const closeDialog = () => {
	// onInitForm();
	state.isShowDialog = false;
};

const onSubmit = () => {
	proxy.$refs.ruleFormRef.validate((valid: any) => {
		if (!valid) {
			return;
		}

		var obj = state.ruleForm;

		obj.TableCategoryData = state.tableCategoryData

		obj.project_Status = obj.project_Status_Value;

		state.loading = true;

		cmProjectsApi
			.Save(obj)
			.then(() => {
				ElMessage.success(t('message.page.saveSuccess'));
				emit('fetchData');
				closeDialog();
			})
			.catch((rs) => {
				ElMessage.error(rs.resultMsg || rs.toString());
			})
			.finally(() => {
				state.loading = false;
			});
	});
};

const deleteRow = (index: number) => {
	// if (state.tableCategoryData.length > 1) {
	state.tableCategoryData.splice(index, 1)
	// } else {
	// 	ElMessage({
	// 		message: 'At least one row must be present.',
	// 		type: 'warning',
	// 	})
	// }
}

const onAddItem = (index: number) => {
	state.tableCategoryData.splice(index + 1, 0, {
		category: '',
		assignee: '',
	})
}

const onAddItemFirst = () => {
	state.tableCategoryData.push({
		category: '',
		assignee: '',
	})
}

watch(() => state.ruleForm.project_Company, async (newVal, oldVal) => {
	if (!state.DialogParams.id) {
		if (newVal) {
			const node = getNodeByOrgId(newVal);
			console.log("node:",node.code);
			
			state.ruleForm.businessUnit = node.businessUnit
		
			await cmProjectsApi.DetailV2(newVal, 'orgId').then((rs) => {
				processResultData(rs);

				state.showTableCategoryData = true;

			});

			await cmProjectsApi.GetProjectID(node.code).then((rs) => {
				state.ruleForm.projectId=rs.data;
			});
	
		} else {
			state.showTableCategoryData = false;
		}
	}
});

const getNodeByOrgId = (orgId: any) => {
	return findNode(state.customerOpts, orgId);
};

const findNode = (nodes: any, orgId: any): any => {
	for (const node of nodes) {
		if (node.orgId === orgId) {
			return node;
		}
		if (node.children && node.children.length > 0) {
			const found = findNode(node.children, orgId);
			if (found) return found;
		}
	}
	return null;
};

var processResultData = (rs: any) => {
	state.CategoryData = rs.data?.dictItemList.find((a: any) => {
		return a.dictValue === 'Category';
	})?.items;

	state.assigneeData = rs.data?.dictItemList.find((a: any) => {
		return a.dictValue === 'Assignee';
	})?.objectItems.groupList;

	const foundCompanyCategoryAssigneeItem = rs.data?.dictItemList.find((a: any) => {
		return a.dictValue === 'CompanyCategoryAssignee';
	});

	if (foundCompanyCategoryAssigneeItem && foundCompanyCategoryAssigneeItem.objectItems && foundCompanyCategoryAssigneeItem.objectItems.length > 0) {
		state.tableCategoryData = foundCompanyCategoryAssigneeItem.objectItems;
	} else {
		state.tableCategoryData = [
			{
				category: '',
				assignee: '',
			},
			{
				category: '',
				assignee: '',
			},
			{
				category: '',
				assignee: '',
			},
		];
	}
}

defineExpose({
	openDialog,
});
</script>
