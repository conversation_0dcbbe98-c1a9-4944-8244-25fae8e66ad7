import type { ElForm } from 'element-plus';
import { ElMessage } from 'element-plus';
import appSettings from '/@/config/index.js';

export const resetForm = (formEl: InstanceType<typeof ElForm> | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

/**
 * 全局错误处理函数
 * 如果错误消息为空或无效，则不显示错误提示
 * @param error 错误对象或错误消息
 * @param defaultMessage 默认错误消息（可选）
 */
export const handleError = (error: any, defaultMessage?: string) => {
	let message = '';

	// 提取错误消息
	if (typeof error === 'string') {
		message = error;
	} else if (error && typeof error === 'object') {
		// 尝试从不同的属性中获取错误消息
		// message = error.resultMsg || error.message || error.msg || '';
		message = error.resultMsg || '';
	}

	// 清理消息内容，去除空白字符
	message = typeof message === 'string' ? message.trim() : '';

	// 如果消息为空且有默认消息，使用默认消息
	if (!message && defaultMessage) {
		message = defaultMessage;
	}

	// 只有在消息不为空时才显示错误提示
	if (message) {
		ElMessage.error(message);
	}

	// 在控制台记录完整的错误信息（用于调试）
	if (appSettings.LOGGING_ENABLED) {
		console.error('Error caught:', error);
	}
};

/**
 * 取数组的最后一个元素
 * @param {Array} data 数组[]
 * @param {any}  defValue 默认值
 */
export const getElcascaderSingle = (data: any, defValue: any) => {
	if (defValue == undefined) defValue = '';
	if (!data || data.length == 0) {
		return defValue;
	}
	return data[data.length - 1];
};

export const getElcascaderMultiple = (data: any, defValue: any) => {
	if (defValue == undefined) defValue = [];
	if (!data || data.length == 0) {
		return defValue;
	}

	let arr = [];

	data.forEach((item) => {
		if (item instanceof Array) {
			arr.push(item[item.length - 1]);
		} else {
			arr.push(item);
		}
	});

	return arr;
};

export const isNullOrWhiteSpace = (value: string | null | undefined): boolean => {
	return !value || !value.trim();
};

/**
 * 判断对象不是空的
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export const isNotEmptyOrNull = (obj: any) => {
	if (!obj) {
		return false;
	}
	if (obj == null || obj == undefined) {
		return false;
	}
	if (obj && obj.length == 0) {
		return false;
	}

	return true;
};
/**
 * 判断是否大于0
 */
export const parseThanZero = (val: any) => {
	if (!isNotEmptyOrNull(val)) return false;

	if (parseInt(val) <= 0) return false;

	return true;
};
/**
 * 获取菜单的父节点，返回数组
 */
export const getCascaderMenuParent = (data: any, mid: any) => {
	var temp: any[] = [];
	var forFn = function (arr: string | any[], mid: any) {
		for (var i = 0; i < arr.length; i++) {
			var item = arr[i];
			if (item.id === mid) {
				temp.push(item.id);
				forFn(data, item.parentId);
				break;
			} else {
				if (item.children) {
					forFn(item.children, mid);
				}
			}
		}
	};
	forFn(data, mid);
	// 数组倒序
	if (temp.length > 0) {
		return temp.reverse();
	}

	if (temp.length == 0) {
		temp.push(mid);
	}

	return temp;
};

/**
 * 获取菜单的父节点，返回数组
 */
export const getCascaderIdustryParent = (data: any, mid: any) => {
	var temp: any[] = [];
	var forFn = function (arr: string | any[], mid: any) {
		for (var i = 0; i < arr.length; i++) {
			var item = arr[i];
			if (item.industryId === mid) {
				temp.push(item.industryId);
				forFn(data, item.parentId);
				break;
			} else {
				if (item.children) {
					forFn(item.children, mid);
				}
			}
		}
	};
	forFn(data, mid);
	// 数组倒序
	if (temp.length > 0) {
		return temp.reverse();
	}

	if (temp.length == 0) {
		temp.push(mid);
	}

	return temp;
};

export const toThousandFilter = (num: any) => {
	return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
};

export const newGuid = () => {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = (Math.random() * 16) | 0,
			v = c == 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};



/**
 * 打印日志
 *
 */
export const debugLog = (message: any, optionalParams?: any) => {
	if (appSettings && appSettings.LOGGING_ENABLED) {
		if (optionalParams) {
			window.console.log(message, optionalParams);
		} else {
			window.console.log(message);
		}
	}
};

/**
 * 比较两个字符串是否相等，默认忽略大小写
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @param {boolean} [caseSensitive=false] - 是否区分大小写
 * @returns {boolean} - 如果两个字符串相等则返回 true，否则返回 false
 */
export const equals = (str1: string, str2: string, caseSensitive: boolean = false): boolean => {
	if (caseSensitive) {
		return str1 === str2;
	} else {
		return str1.toLowerCase() === str2.toLowerCase();
	}
};

/**
 * 提取年份的函数
 */
export const getYearFromDate = (dateString: string) => {
	if (!dateString) return ''; // 如果日期为空，返回空字符串
	const date = new Date(dateString); // 转换为 Date 对象
	if (isNaN(date.getTime())) return ''; // 如果日期无效，返回空字符串
	return date.getFullYear().toString(); // 提取年份并转换为字符串
  };

/**
 * 对任意内容进行编码，使其在URL中不那么明显
 * @param content - 原始内容（支持任意字符串、数字等）
 * @returns 编码后的字符串
 */
export const encodeContent = (content: any): string => {
	if (content === null || content === undefined) return '';

	const contentStr = typeof content === 'string' ? content : String(content);
	if (!contentStr) return '';

	try {
		// 使用Base64编码内容
		const base64Content = btoa(encodeURIComponent(contentStr).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));

		// 添加简单的字符替换混淆
		const obfuscated = base64Content
			.replace(/\+/g, '-')
			.replace(/\//g, '_')
			.replace(/=/g, '');

		// 添加前缀和校验码
		const prefix = 'enc';
		// 生成简单的校验码
		const checksum = contentStr.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 97;
		const suffix = 'x' + checksum.toString(36);

		return prefix + obfuscated + suffix;
	} catch (error) {
		return '';
	}
};

/**
 * 对编码的内容进行解码
 * @param encodedContent - 编码后的内容
 * @returns 原始内容，如果解码失败返回空字符串
 */
export const decodeContent = (encodedContent: string): string => {
	if (!encodedContent || typeof encodedContent !== 'string') return '';

	// 检查前缀
	if (!encodedContent.startsWith('enc')) return '';

	// 移除前缀
	const withoutPrefix = encodedContent.substring(3);

	// 找到最后一个'x'的位置来分离主体和后缀
	const lastXIndex = withoutPrefix.lastIndexOf('x');
	if (lastXIndex === -1) return '';

	const obfuscatedPart = withoutPrefix.substring(0, lastXIndex);
	const suffixPart = withoutPrefix.substring(lastXIndex + 1);

	try {
		// 还原Base64字符
		const base64Content = obfuscatedPart
			.replace(/-/g, '+')
			.replace(/_/g, '/');

		// 补充Base64填充字符
		const padding = '='.repeat((4 - base64Content.length % 4) % 4);
		const fullBase64 = base64Content + padding;

		// 解码Base64
		const decodedContent = decodeURIComponent(atob(fullBase64).split('').map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));

		// 验证校验码
		const expectedChecksum = decodedContent.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 97;
		const expectedSuffix = expectedChecksum.toString(36);
		if (suffixPart !== expectedSuffix) return '';

		return decodedContent;
	} catch (error) {
		return '';
	}
};

// 为了向后兼容，保留原有的函数名
export const encodeTicketNumber = encodeContent;
export const decodeTicketNumber = (encodedTicketNumber: string): number => {
	const decoded = decodeContent(encodedTicketNumber);
	const num = parseFloat(decoded);
	return isNaN(num) ? 0 : num;
};