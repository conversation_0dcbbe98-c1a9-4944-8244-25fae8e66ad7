﻿<template>
	<div class="organization-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上级公司">
							<el-tree-select
								v-model="ruleForm.parentId"
								:data="parentTree"
								:default-expand-all="true"
								checkStrictly
								style="width: 100%"
								:props="{ value: 'orgId', label: 'name' }"
							>
								<template #default="{ data }">
									<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
									<span>{{ data.name }}</span>
								</template>
							</el-tree-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="组织名称" prop="name">
							<el-input v-model="ruleForm.name" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="组织编码">
							<el-input v-model="ruleForm.code" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="邮编" prop="postCode">
							<el-input v-model="ruleForm.postCode" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="地址" prop="address">
							<el-input v-model="ruleForm.address" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="电话" prop="phone">
							<el-input v-model="ruleForm.phone" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="邮箱" prop="email">
							<el-input v-model="ruleForm.email" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="传真" prop="fax">
							<el-input v-model="ruleForm.fax" placeholder="Please input " />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="联系人">
							<el-input v-model="ruleForm.linkTel" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属区域" prop="region">
							<el-input v-model="ruleForm.region" placeholder="Please input " />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="排序" prop="sort">
							<el-input-number v-model="ruleForm.sort" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注" prop="description">
							<el-input v-model="ruleForm.description" type="textarea" placeholder="请输入备注" maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
					<el-button v-if="dialogParams.action === 'Create'" @click="onInitForm" size="small" type="danger">Reset </el-button>
					<el-button v-if="dialogParams.action === 'Edit'" @click="onDelete" type="danger" size="small">Delete </el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, watch } from 'vue';
import { getElcascaderSingle, isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import organizationApi from '/@/api/organization';

interface DialogParams {
	action: string;
	orgId: number;
}

export default defineComponent({
	name: 'organizationCreateOrEdit',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create Organization',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			parentTree: [],
			parentTreeArr: [],
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				orgId: 0, //
				code: '', //
				name: '', //
				parentId: '', //
				postCode: '', //
				address: '', //
				phone: '', //
				email: '', //
				fax: '', //
				linkTel: '', //
				logo: '', //
				region: '', //
				regions: '', //
				sort: 0, //
				status: 0, //
				description: '', //
				isDeleted: false, //
				createAt: new Date(), //
				updateAt: new Date(), //
			},
			rules: {
				code: [{ required: true, message: 'Please input', trigger: 'blur' }],
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				linkTel: [{ required: true, message: 'Please input', trigger: 'blur' }],
				sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			//onInitForm();

			if (parmas.action == 'Create') {
				state.title = '创建组织';
			} else if (parmas.action == 'Edit') {
				state.title = '编辑组织';
				getData(parmas.orgId);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}
			state.isShowDialog = true;
			organizationApi.Tree().then((rs) => {
				state.parentTree = rs.data;
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				if (obj.parentId.length == 0) {
					obj.parentId = 0;
				}
				state.saveLoading = true;

				organizationApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		//根据id获取完整的信息
		const getData = (id: any) => {
			organizationApi.GetByKey(id).then((rs) => {
				state.ruleForm = rs.data || {};
				// state.parentTreeArr.push(state.ruleForm.parentId);
				//console.log("state.parentTreeArr", state.parentTreeArr)
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(`Are you sure to delete this?`, 'Tips', {
				confirmButtonText: 'OK',
				cancelButtonText: 'No,Thanks',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				state.deleteLoading = true;
				organizationApi
					.DeleteByKey(state.ruleForm.orgId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.parentTreeArr = [];
			state.ruleForm = {
				orgId: 0, //
				code: '', //
				name: '', //
				parentId: '', //
				postCode: '', //
				address: '', //
				phone: '', //
				email: '', //
				fax: '', //
				linkTel: '', //
				logo: '', //
				region: '', //
				regions: '', //
				sort: 0, //
				status: 0, //
				description: '', //
				isDeleted: false, //
				createAt: new Date(), //
				updateAt: new Date(), //
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
});
</script>
