import { Local, Session } from '/@/utils/storage';
import loginApi from '/@/api/login';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import aTestApi from '/@/api/ATest';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

export function startRefreshingToken() {
	setInterval(
		() => {
			let refreshToken = Local.get('refreshToken');
			let userName = userInfos.value.userName;
			if (!refreshToken || !userName) return;

			loginApi
				.RefreshToken({ token: refreshToken, userName: userName })
				.then((rs) => {
					if (!rs.data) {
						Session.clear(); // 清除缓存/token等
						Local.clearToken();
						aTestApi.VueLog({ message: '11' });
						window.location.href = ''; // 去登录页

						return;
					}

					const accessToken = rs.data.token;
					const accessTokenExp = rs.data.exp;
					const refreshToken = rs.data.refreshToken;

					Local.set('token', accessToken);
					Local.set('tokenExp', accessTokenExp);
					Local.set('refreshToken', refreshToken);
				})
				.catch((rs) => {
					//console.log('tokenCheck', rs.resultMsg);
				});
		},
		1000 * 60 * 5
	); // 5分钟前端自动刷新一次token以及检测是否有用户1个小时候未使用系统，因为后台设置Token的过期时间是1小时
}
