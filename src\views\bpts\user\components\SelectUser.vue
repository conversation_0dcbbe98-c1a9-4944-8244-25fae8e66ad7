﻿<template>
	<div class="user-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px" close-on-click-modal="false">
			<el-row :gutter="35">
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
					<div class="mb10">
						<el-input v-model="searchKey" placeholder="Please input name 、email or mobile" class="input-with-select">
							<template #append>
								<el-button icon="ele-Search" />
							</template>
						</el-input>
					</div>

					<el-tabs v-model="activeName" type="border-card" style="height: 330px">
						<el-tab-pane label="All" name="All">
							<el-scrollbar height="290px">
								<el-tree
									:data="orgTreeData"
									:props="{ value: 'id', label: 'itemName' }"
									:default-expand-all="false"
									@node-click="handleNodeClick"
									class="dep-tree"
								>
									<template #default="{ data }">
										<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 1"></SvgIcon>
										<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 2"></SvgIcon>
										<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 3"></SvgIcon>
										<span>{{ data.itemName }}</span>
									</template>
								</el-tree>
							</el-scrollbar>
						</el-tab-pane>
						<el-tab-pane label="Department" name="Department"> </el-tab-pane>
						<el-tab-pane label="Subordinates" name="Subordinates"> </el-tab-pane>
					</el-tabs>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
					<el-card class="box-card" shadow="never" style="height: 390px">
						<template #header>
							<div class="card-header">
								<span style="float: left">Selected</span>
								<el-link type="danger">Clear</el-link>
							</div>
						</template>
						<div class="selected-item">
							<el-tree
								ref="menu"
								class="menu"
								node-key="id"
								:data="selectist"
								:props="{ value: 'id', label: 'itemName' }"
								draggable
								highlight-current
								:expand-on-click-node="false"
								check-strictly
							>
								<template #default="{ data }">
									<span class="custom-tree-node el-tree-node__label">
										<span class="label">
											{{ data.itemName }}
										</span>
										<span class="do">
											<el-icon @click.stop="onDelete(data)"><ele-Delete /></el-icon>
										</span>
									</span>
								</template>
							</el-tree>
						</div>
					</el-card>
				</el-col>
			</el-row>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, computed, onMounted } from 'vue';
// import { useStore } from '/@/store/index';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';

import organizationApi from '/@/api/organization';

interface DialogParams {
	multiple: boolean;
	initData: any;
	onlyKey: boolean;
}

interface UserInfo {
	orgId: number; //组织架构id
	orgTypeId: number; //部门id
	parentId: number; //上级id
	name: string;
	itemType: number;
}

export default defineComponent({
	name: 'SelectData',
	components: {},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		// const store = useStore();
		const state = reactive({
			title: 'Select User',
			isShowDialog: false,
			searchKey: '',
			activeName: 'All',
			currentData: [], //当前组织
			orgTreeData: [], //所有
			subData: [], //我的下属
			dialogParams: {} as DialogParams,
			selectist: [] as UserInfo[],
			selectedIds: [] as number[],
		});
		// 获取用户信息 vuex
		// const currentUser = computed(() => {
		// 	return store.state.userInfos.userInfos;
		// });

		const findNodes = (treeData: any[], itemIds: number[]) => {
			let result: any[] = [];
			treeData.forEach((node) => {
				if (node.itemType === 3 && itemIds.includes(node.itemId)) {
					result.push(node);
				}
				if (node.children) {
					result = result.concat(findNodes(node.children, itemIds));
				}
			});
			return result;
		};

		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			state.isShowDialog = true;
			state.selectist = [];

			organizationApi.UserTree({ parentId: 0 }).then((rs) => {
				state.orgTreeData = rs.data;

				if (parmas.initData) {
					const nodes = findNodes(state.orgTreeData, parmas.initData);
					state.selectist = nodes.map((node) => ({ itemId: node.itemId, itemName: node.itemName }));
				}
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		const handleNodeClick = (node: UserInfo) => {
			if (!state.dialogParams.multiple && state.selectist.length >= 1) {
				ElMessage.info('Select at most one item');
				return;
			}
			if (node.itemType === 3) {
				//表示用户
				if (!state.selectist.some((item) => item.itemId === node.itemId)) {
					state.selectist.push(node);
				}
			}
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			//只需要id ？
			state.selectedIds = state.selectist.map((item) => {
				return item.orgTypeId;
			});
			//返回完整的实体
			if (state.dialogParams.onlyKey) context.emit('fetchData', state.selectedIds);
			else context.emit('fetchData', state.selectist);

			onCancel();
		};
		//删除纪录
		const onDelete = (row: any) => {
			state.selectist = state.selectist.filter((obj) => obj !== row);
		};
		// 页面加载时
		onMounted(async () => {});

		return {
			// currentUser,
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			handleNodeClick,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 5px 0px;
}
.el-card__header {
	padding: 15px 20px;
}
.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 24px;
	height: 100%;
	width: 250px;
}
</style>
