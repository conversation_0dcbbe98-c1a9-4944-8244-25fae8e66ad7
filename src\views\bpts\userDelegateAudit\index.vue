﻿<template>
	<el-container class="userDelegateAudit-index-container tablelist" style="height: 100%">
		<el-dialog title="Delegate History" width="1280px" v-model="isShowDialog" :close-on-click-modal="false"
			draggable>
			<el-main class="nopadding" ref="printMain" style="height: 100%; border: #e7e7e7 solid 1px">
				<div class="scTable" style="height: 600px" ref="scTableMain">
					<div class="scTable-table">
						<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)"  stripe border>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>

							<el-table-column label="Approver Type" :min-width="230" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.approverType }}</span>
								</template>
							</el-table-column>

							<el-table-column label="Delegate to" :min-width="140" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.userDelegateName }}</span>
								</template>
							</el-table-column>

							<el-table-column label="Username" :min-width="140" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.userTrusteeName }}</span>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.userFields.projects')" prop="projectNames"
								:min-width="250" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.projectNames }}</span>
								</template>
							</el-table-column>
							<el-table-column label="Start Date" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.dateStart) }}</span>
								</template>
							</el-table-column>

							<el-table-column label="End Date" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.dateEnd) }}</span>
								</template>
							</el-table-column>

							<el-table-column label="Can Email" width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.isEmail" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="!row.isEmail" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>
							<el-table-column label="On/Off" width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<el-icon v-if="row.isApprove" style="color: #67c23a">
										<ele-SuccessFilled />
									</el-icon>
									<el-icon v-if="!row.isApprove" style="color: red">
										<ele-SuccessFilled />
									</el-icon>
								</template>
							</el-table-column>
							<el-table-column label="Create By " width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.createdByName }}</span>
								</template>
							</el-table-column>
							<el-table-column label="Create Time" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.createdAt) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="Modified By " width="200" show-overflow-tooltip>
								<template #default="{ row }">
									<span>{{ row.modifiedByName }}</span>
								</template>
							</el-table-column>
							<el-table-column label="Modified Time" width="200">
								<template #default="{ row }">
									<span>{{ formatStrDate(row.modifiedAt) }}</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="scTable-page">
						<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
							:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
							v-model:page-size="tableData.param.pageSize"
							layout="total, sizes, prev, pager, next, jumper" :total="tableData.total" small>
						</el-pagination>
					</div>
				</div>
			</el-main>
		</el-dialog>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, ref, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';
import userDelegateAuditApi from '/@/api/userDelegateAudit/index';

interface DialogParams {
	action: string;
	userTrusteeId: number;
	id: number;
}
export default defineComponent({
	name: 'userDelegateAuditIndex',
	components: {},
	setup() {
		//const router = useRouter();
		const printMain = ref(null);
		const state = reactive({
			isShowDialog: false,
			dialogParams: {
				action: '',
				userTrusteeId: -1,
				id: -1,
			},
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'auditId',
					sort: 'desc', // asc or desc
					userTrusteeId: 0, //
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		//初始化
		const onInit = () => {
			state.tableData.param.userTrusteeId = state.dialogParams.userTrusteeId;
			state.tableData.loading = true;

			userDelegateAuditApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
					state.tableData.total = rs.totalCount;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			state.isShowDialog = true;
			onInit();
		};
		return {
			printMain,
			formatStrDate,
			onInit,
			onSizechange,
			onCurrentChange,
			onSearch,
			openDialog,
			...toRefs(state),
		};
	},
});
</script>
