﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class cmTicketContentsApi extends BaseApi {
    QueryCard(data: any) {
        return request({
            url: this.baseurl + 'querycard',
            method: 'get',
            data,
        });
    };
    IsReadMsg(data: any) {
        return request({
            url: this.baseurl + 'IsReadMsg',
            method: 'get',
            data,
        });
    };
    UpdateIsRead(data: any) {
        return request({
            url: this.baseurl + 'UpdateIsRead/'+data,
            method: 'post',
            data,
        });
    };
}

export default new cmTicketContentsApi('/api/cmTicketContents/','id');




                        
        
        