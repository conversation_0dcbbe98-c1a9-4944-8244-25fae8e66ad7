<template>
	<div class="layout-navbars-breadcrumb-user-news">
		<div class="head-box">
			<div class="head-box-title">New</div>
			<div class="head-box-btn" v-if="newsList.length > 0" @click="onAllReadClick"></div>
		</div>
		<div class="content-box">
			<template v-if="newsList.length > 0">
				<div class="content-box-item" v-for="(v, k) in newsList" :key="k"
					@click="navigateToTicket(v.ticket_Number, v.contentId)">
					<div class="title">New comment for ticket {{ v.ticket_Number }} View</div>
					<div class="content-box-msg" style="word-break: normal; text-align: left" v-html="v.content"></div>
					<div class="content-box-time">
						<el-icon style="vertical-align: middle">
							<ele-Clock />
						</el-icon>
						{{ v.createdTime }}
						<el-icon style="vertical-align:middle">
							<ele-User />
						</el-icon>
						{{ v.userName }}
					</div>
				</div>
			</template>
			<el-empty :description="$t('message.user.newDesc')" v-else></el-empty>
		</div>
		<div class="foot-box" @click="onGoToGiteeClick" v-if="false">{{ $t('message.user.newGo') }}</div>
	</div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router';
import { reactive, toRefs, defineComponent, onMounted, computed } from 'vue';
import cmTicketContentApi from '/@/api/cmTicketContents/index';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import mittBus from '/@/utils/mitt';
import { encodeTicketNumber } from '/@/utils';
export default defineComponent({
	name: 'layoutBreadcrumbUserNews',
	setup() {
		const state = reactive({
			newsList: [],
		});
		const router = useRouter();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		// 获取用户信息 vuex
		const getUserInfos = computed(() => {
			return <any>userInfos.value;
		});
		// 全部已读点击
		const onAllReadClick = () => {
			state.newsList = [];
		};
		// 前往通知中心点击
		const onGoToGiteeClick = () => {
			window.open('https://gitee.com/lyt-top/vue-next-admin');
		};

		const navigateToTicket = (tiketNum: number, contentId: number) => {
			mittBus.emit('SendTiketNum', Object.assign({}, { tiketNum: tiketNum }));
			cmTicketContentApi
				.UpdateIsRead(contentId)
				.then((rs) => {

				})
				.catch((rs) => { })
				.finally();

			// cmTicketContentApi
			// .QueryCard({ createdById: getUserInfos.value.userId, order: 'CreatedAt', sort: 'desc' })
			// .then((rs) => {
			// 	state.newsList = rs.data;
			// })
			// .catch((rs) => { })
			// .finally();
			const encodedTicketNumber = encodeTicketNumber(tiketNum);
			router.push('/dashboard/editTicket/' + encodedTicketNumber);
		};

		onMounted(() => {

			cmTicketContentApi
				.QueryCard({ createdById: getUserInfos.value.userId, order: 'CreatedAt', sort: 'desc' })
				.then((rs) => {
					state.newsList = rs.data;
				})
				.catch((rs) => { })
				.finally();
		});


		return {
			navigateToTicket,
			onAllReadClick,
			onGoToGiteeClick,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user-news {
	.head-box {
		display: flex;
		border-bottom: 1px solid var(--el-border-color-lighter);
		box-sizing: border-box;
		color: var(--el-text-color-primary);
		justify-content: space-between;
		height: 35px;
		align-items: center;

		.head-box-btn {
			color: var(--el-color-primary);
			font-size: 13px;
			cursor: pointer;
			opacity: 0.8;

			&:hover {
				opacity: 1;
			}
		}
	}

	.content-box {
		font-size: 13px;

		.content-box-item {
			border-bottom: 1px solid var(--el-border-color-lighter);
			padding: 10px;
			border-radius: 5px;
			margin-top: 5px;

			.title {
				font-weight: bold;
			}

			&:last-of-type {
				padding-bottom: 12px;
				border-bottom-width: 0px;
			}

			.content-box-msg {
				color: var(--el-text-color-secondary);
				margin-top: 5px;
				margin-bottom: 5px;
			}

			.content-box-time {
				color: var(--el-text-color-secondary);
			}

			.ele-icon {
				vertical-align: middle;
				margin-right: 5px;
			}
		}

		.content-box-item:hover {
			background-color: #ebeef5;
			cursor: pointer;
		}
	}

	.foot-box {
		height: 35px;
		color: var(--el-color-primary);
		font-size: 13px;
		cursor: pointer;
		opacity: 0.8;
		display: flex;
		align-items: center;
		justify-content: center;
		border-top: 1px solid var(--el-border-color-lighter);

		&:hover {
			opacity: 1;
		}
	}

	::v-deep(.el-empty__description p) {
		font-size: 13px;
	}
}
</style>
