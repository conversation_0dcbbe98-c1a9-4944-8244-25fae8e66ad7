<template>
	<div class="profile-container">
		<div class="lpx-content">
			<el-row :gutter="15" class="profile-row">
				<el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
					<el-card class="mb10">
						<div class="card-body text-center">
							<el-avatar :size="90" :src="squareUrl" />
							<!-- <img class="lpx-avatar-img-lg mb-3" src="/img/user-07.png" alt="avatar" v-if="false" /> -->
							<div class="border-bottom pb-3 mb-3">
								<h3 class="card-title mb3">{{ fullName }}</h3>
								<small class="text-muted" v-if="false">{{ userData.userName }}</small>
								<h5 class="fw-normal mt-2 mb3">{{ email }}</h5>
								<small class="text-muted" v-if="shortLocation">
									<el-icon>
										<ele-LocationFilled />
									</el-icon>
									{{ shortLocation }}</small
								>
							</div>

							<div class="border-bottom pb-3 mb-3 menu_btn_wrapper">
								<div class="button_container">
									<el-button color="#626aef" :dark="isDark" :plain="isPlain('Profile')" class="menu_btn" @click="changeComponent('Profile')">{{
										$t('message.userFields.profile')
									}}</el-button>
								</div>
								<div class="button_container">
									<el-button color="#626aef" :dark="isDark" :plain="isPlain('Email')" class="menu_btn" @click="changeComponent('Email')">{{
										$t('message.userFields.emailChange')
									}}</el-button>
								</div>
								<div class="button_container">
									<el-button color="#626aef" :dark="isDark" :plain="isPlain('Password')" class="menu_btn" @click="changeComponent('Password')">{{
										$t('message.userFields.password')
									}}</el-button>
								</div>

								<div class="button_container">
									<el-button
										color="#626aef"
										:dark="isDark"
										:plain="isPlain('DelegateList')"
										class="menu_btn"
										@click="changeComponent('DelegateList')"
										>{{ $t('message.userFields.delegateSettings') }}</el-button
									>
								</div>
								<div class="button_container">
									<el-button color="#626aef" :dark="isDark" :plain="isPlain('Ticket')" class="menu_btn" @click="changeComponent('Ticket')">
										{{ $t('message.userFields.ticket') }}</el-button
									>
								</div>
								<div class="button_container">
									<el-button
										color="#626aef"
										:dark="isDark"
										:plain="isPlain('SwitchLanguage')"
										class="menu_btn"
										@click="changeComponent('SwitchLanguage')"
										>{{ $t('message.userFields.switchLanguage') }}</el-button
									>
								</div>
							</div>
							<div>
								<small class="text-muted"> {{ $t('message.userFields.creationDate') }}</small>
								<h5 class="mt-0 h5">{{ formatDay(userData.createTime, 'MM/dd/YYYY') }}</h5>
							</div>
						</div>
					</el-card>
				</el-col>
				<el-col :xs="24" :sm="24" :md="16" :lg="18" :xl="18">
					<component :is="currentComponent" v-bind:info="userData" @fetchData="refreshProfile" />
				</el-col>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts">
import { toRefs, onMounted, reactive } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { formatDay } from '/@/utils/formatTime';
import DelegateList from '/@/views/bpts/userDelegate/delegateList.vue';
import Ticket from '/@/views/bpts/ticket/list.vue';
import Profile from './components/profile.vue';
import Email from './components/email.vue';
import Password from './components/password.vue';
import SwitchLanguage from './components/switchLanguage.vue';
import userApi from '/@/api/user';

export default {
	name: 'layoutBreadcrumbUser',
	components: { Profile, Email, Password, SwitchLanguage, DelegateList, Ticket },
	setup() {
		const state = reactive({
			currentComponentTitle: 'User Profile',
			currentComponent: 'Profile' as any,
			userData: {} as any,
			fullName: '',
			email: '',
			shortLocation: '',
			squareUrl: '/img/avatar.png',
		});
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);

		const changeComponent = (component: any) => {
			state.currentComponent = component;
		};

		const isPlain = (component: any) => {
			return state.currentComponent !== component;
		};

		const refreshProfile = () => {
			userApi.Detail(userInfos.value.userId).then((rs) => {
				state.userData = rs.data;
				const { realName, lastName, email } = state.userData;
				//console.log('state.userData:', state.userData);
				state.fullName = `${realName} ${lastName}`;
				state.email = email;

				//123 Main Street, London, England, UK, 12345 最好是能规定完整地址格式
				let fullLocation = state.userData.location;
				if (fullLocation) {
					let locationParts = fullLocation.split(',');
					if (locationParts.length >= 3) {
						// 选择城市和国家作为简短的地理位置
						state.shortLocation = locationParts[1].trim() + ', ' + locationParts[2].trim();
					}
				}
			});
		};
		// 页面加载时
		onMounted(() => {
			refreshProfile();
		});
		return {
			isPlain,
			formatDay,
			refreshProfile,
			changeComponent,
			...toRefs(state),
		};
	},
};
</script>

<style scoped>
.lpx-content {
	/* max-width: 1280px !important; */
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}
.mb-0 {
	margin-bottom: 0 !important;
}
.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}
h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}
</style>
