﻿export default {
	menuSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
	},
	menuButtons: {
		//非按钮
		createMenu: 'Create Menu',
		createButton: 'Create Button',
	},
	menuFields: {
		//table 列名
		id: 'Id',
		parentId: 'ParentId',
		path: 'Route Path',
		name: 'Menu Name',
		component: 'Component Path',
		redirect: 'Redirect',
		sort: 'Sort',
		menuType: 'MenuType',
		title: 'Title',
		isLink: 'Is Link',
		isHide: 'Is Hide',
		isKeepAlive: 'IsKeepAlive',
		isAffix: 'IsAffix',
		isIframe: 'IsIframe',
		icon: 'Icon',
		createAt: 'CreateAt',
		identification: 'Identification',
        PrivilegeOperation:'Privilege Operation',
		linkUrl:'Link Url',
		pageCache:'Page Cache',
		bindApi:'API Binding'
	},
	menuEditFields:{
		parentMenu:'Parent Menu',
		routerName:'Router Name',
	},
	menuCommonFields:{
		PrivilegeBelongsTo:'Privilege Belongs To',
		PermissionName:'Permission Name',
		DisplayName:'Display Name',
		createPermission:'Create Permission',
		editPermission:'Edit Permission',
	}
};
