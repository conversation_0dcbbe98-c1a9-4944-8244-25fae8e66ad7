﻿export default {
	menuSearch: {
		//查询区域
		searchKeyPlaceholder: '请输入关键字',
	},
	menuButtons: {
		//非按钮
		createMenu: '创建菜单',
		createButton: '创建按钮',
	},
	menuFields: {
		//table 列名
		id: ' ',
		parentId: ' ',
		path: '路由路径',
		name: '菜单名称',
		component: '组件路径',
		redirect: '重定向',
		sort: '菜单排序',
		menuType: ' ',
		title: ' ',
		isLink: '是否外链',
		isHide: '是否隐藏',
		isKeepAlive: ' ',
		isAffix: '是否固定',
		isIframe: '是否内嵌',
		icon: '菜单图标',
		createAt: ' ',
		identification: '权限标识',
        PrivilegeOperation:'权限操作',
		linkUrl:'链接地址',
		pageCache:'页面缓存',
		bindApi:'Api绑定'
	},
	menuEditFields:{
		parentMenu:'上级菜单',
		routerName:'路由名称',
	},
	menuCommonFields:{
		PrivilegeBelongsTo:'权限所属',
		PermissionName:'权限名称',
		DisplayName:'显示名称',
		createPermission:'创建权限',
		editPermission:'修改权限',
	}
};
