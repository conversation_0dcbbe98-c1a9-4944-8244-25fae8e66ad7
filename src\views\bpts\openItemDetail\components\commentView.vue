<template>
	<el-dialog :title="title" v-model="state.isShowDialog" :close-on-click-modal="false" @close="closeDialog" :destroy-on-close="true" width="800">
		<div class="ticket-info_layout" :style="{ height: 'calc(90vh - 250px)' }">
			<el-container style="height: 100%">
				<el-container class="tk-container">
					<el-main class="tk-main new-tk-main" v-if="state.openItemDetailId > 0 ">
						<el-timeline>
							<el-timeline-item v-for="(item,index) in state.formData" :timestamp="item.createdAt" placement="top"
								:key="item.ticketContentId">
								<div class="tl-item">
									<div class="timeline-avatar">
										<el-avatar :size="50"
											src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
									</div>
									<div class="timeline-content">
										<div class="timeline-conte_name">
											<h5>{{ item.createdBy }}</h5>
										</div>
										<!-- <p v-html="item.comment" v-if="index==0"
											class="internal-content"></p> -->
										<p v-html="item.comment"></p>

										<el-divider />
									</div>
								</div>
							</el-timeline-item>
						</el-timeline>
					</el-main>
				</el-container>
			</el-container>
		</div>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="closeDialog" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted, defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';

import cmOpenItemDetailCommentApi from '/@/api/cmOpenItemDetailComment/index';
import { useI18n } from 'vue-i18n';
import { formData } from '/@/views/pages/dynamicForm/mock';

defineProps({
  title: {
    type: String,
    default: '',
  },
});

const { t } = useI18n();
const { proxy } = getCurrentInstance() as any;
const state = reactive({
	title: t('message.roleFields.createRole'),
	isShowDialog: false,
	openItemDetailId: 0,
    formData: [] as any,
});
// 打开弹窗
const openDialog = (parmas: any) => {
	let formParmas = cloneDeep(parmas);
    state.openItemDetailId=formParmas.id;
	getOpenItemDetailComments(formParmas, () => {
		state.isShowDialog = true;
    });
};
const getOpenItemDetailComments = async (row: any, callback: () => void) => {
  if(row.id>0){    
    var obj={
      openItemDetailId: row.id,
      pageIndex: 1,
      pageSize: 100000,
      sort: 'desc',
      order: 'id',
    }
    await cmOpenItemDetailCommentApi.Query(obj).then((rs:any) => {
      if(rs.data!=null){
        console.log(rs.data);
        state.formData=rs.data;
      }
    });
  }
  callback(); //执行回调函数
};
// 关闭弹窗
const closeDialog = () => {
	state.isShowDialog = false;
};
onMounted(async () => {
});

defineExpose({
	openDialog,
});
</script>

<style>
.list-page-layout .el-main,
.tk-main,
.new-tk-main {
	text-align: left;
}

.uploader-icon {
	width: 24px;
	height: 24px;
	font-size: 20px;
}

.card-footer {
	height: 200px;
	width: 100%;
	overflow-y: auto;
}

.el-collapse-item__header .comment-footer {
	font-weight: 700;
	color: #303133;
}

.ticket-info_layout .tk-footer.new-footer-default {
	height: 50px;
}

.ticket-info_layout .tk-footer.new-footer-default.new-footer-expand {
	height: 250px;
}
</style>
