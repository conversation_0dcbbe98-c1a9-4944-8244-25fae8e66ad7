﻿<template>
	<div class="dictItem-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="600px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.dictFields.Category')">
							<el-select :placeholder="$t('message.page.selectKeyPlaceholder')" v-model="ruleForm.dictId"
								class="w100">
								<el-option v-for="item in dictData" :label="item.name" :value="item.dictId"
									:key="item.dictId"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.dictFields.name')" prop="name">
							<el-input v-model="ruleForm.name" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.dictFields.value')" prop="value">
							<el-input v-model="ruleForm.value" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.dictFields.sort')" prop="sort">
							<el-input-number v-model="ruleForm.sort" :min="0" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
						<el-form-item :label="$t('message.dictFields.enable')" prop="enable">
							<el-switch v-model="ruleForm.enable" inline-prompt></el-switch>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.dictFields.description')" prop="description">
							<el-input v-model="ruleForm.description" type="textarea"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">{{ $t('message.page.buttonCancel') }}</el-button>
					<el-button @click="onInitForm" size="small" type="danger">{{ $t('message.page.buttonReset')
						}}</el-button>
					<el-button v-if="ruleForm.action === 'Edit'" @click="onDelete" type="danger" size="small">{{
						$t('message.page.buttonDelete') }} </el-button>
					<el-button :loading="saveLoading" :disabled="saveLoading" type="primary" @click="onSubmit"
						size="small">{{
							$t('message.page.buttonSave') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import cmProjectsApi from '/@/api/cmProjects/index';
import dictApi from '/@/api/dict/index';
import dictItemApi from '/@/api/dictItem';
import companyApi from '/@/api/company/index';
import cmCategoryApi from '/@/api/cmCategory/index';
import mittBus from '/@/utils/mitt';
import { debounce } from 'lodash';

interface DialogParams {
	action: string;
	itemId?: number;
	dictTypeId?: number;
	dctTypeName: string;
}

export default {
	name: 'dictItemCreateOrEdit',
	components: {},
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			title: 'Create DictItem',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dictData: [],
			dialogParams: {
				action: '',
				itemId: -1,
				dictTypeId: -1,
				dctTypeName: '',
			},
			ruleForm: {
				itemId: 0, //
				dictId: 0, //
				parentId: 0, //
				name: '', //
				value: '', //
				valueOld: '', //
				sort: 0, //
				enable: true, //
				description: '', //
				descriptionOld: '',
				createdAt: new Date(),
			},
			rules: {
				itemId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				dictId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				value: [{ required: true, message: 'Please input', trigger: 'blur' }],
				sort: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;

			onInitForm();

			if (parmas.action == 'Create') {
				state.title = 'Create DictItem';
				state.ruleForm.dictId = state.dialogParams.dictTypeId;
			} else if (parmas.action == 'Edit') {
				state.title = 'Edit DictItem';
				getData(parmas.itemId);
			} else {
				ElMessage.error('Parameter action cannot be empty.');
			}

			//获取类别列表
			dictApi.GetList().then((rs) => {
				state.dictData = rs.data;
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = debounce(() => {
			if (state.saveLoading) return;

			state.saveLoading = true;

			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					state.saveLoading = false;
					return;
				}

				var obj = state.ruleForm;

				dictItemApi
					.Save(obj)
					.then((rs) => {
						if (!rs.data && rs.data <= 0) {
							ElMessage.error(rs.resultMsg);
							return;
						}

						if (state.ruleForm.valueOld != state.ruleForm.value) {

							if (state.dialogParams.action == 'Edit') {
								var projectObj = {
									valOld: state.ruleForm.valueOld,
									valNew: state.ruleForm.value,
									valNew2: state.ruleForm.description,
								};

								//更新 Business Unit
								if (state.dialogParams.dctTypeName == 'Business Unit') {
									cmProjectsApi
										.UpdateBusinessUnit(projectObj)
										.then(() => {
											//ElMessage.success(t('message.page.saveSuccess'));
										})
										.catch((rs) => {
											ElMessage.error(rs.resultMsg || rs.toString());
										})
										.finally(() => {

										});
								}
								//更新 Company Type/Industry
								if (state.dialogParams.dctTypeName == 'Industry') {
									companyApi
										.UpdateCompanyIndustry(projectObj)
										.then(() => {


										})
										.catch((rs) => {
											ElMessage.error(rs.resultMsg || rs.toString());
										})
										.finally(() => {

										});
								}
								//更新 Category
								if (state.dialogParams.dctTypeName == 'Category') {
									cmCategoryApi
										.UpdateCmCategoryCode(projectObj)
										.then(() => {
											//刷新页面
											//mittBus.emit('RefreshTicketList', {});
										})
										.catch((rs) => {
											ElMessage.error(rs.resultMsg || rs.toString());
										})
										.finally(() => {

										});
								}
							}
						}

						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		}, 500);

		//根据id获取完整的信息
		const getData = (id: any) => {
			dictItemApi.GetByKey(id).then((rs) => {
				state.ruleForm = Object.assign({}, rs.data);
				state.ruleForm.valueOld = state.ruleForm.value;
			});
		};
		//删除纪录
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {

				state.deleteLoading = true;
				dictItemApi
					.DeleteByKey(state.ruleForm.itemId)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.deleteLoading = false;
					});
			});
		};
		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.ruleForm = {
				itemId: 0, //
				dictId: 0, //
				parentId: 0, //
				name: '', //
				value: '', //
				sort: 0, //
				enable: true, //
				description: '', //
				createdAt: new Date(),
			};
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			...toRefs(state),
		};
	},
};
</script>
