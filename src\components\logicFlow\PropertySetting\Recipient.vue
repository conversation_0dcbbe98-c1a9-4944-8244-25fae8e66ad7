<template>
	<div class="recipient-container">
		<el-dialog :title="title" v-model="isShowDialog" width="769px">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-form ref="tableRulesRef" :model="tableData" size="small">
						<el-table ref="tableRef" border :data="tableData.data" style="height: 300px">
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
							</template>

							<el-table-column label="数据来源" show-overflow-tooltip>
								<template #default="{ row }">
									<el-form-item>
										<el-select placeholder="请选择" v-model="row.source" @change="(val) => onChange(val, row)">
											<el-option v-for="sel in sourceOption" :key="sel.value" :label="sel.label" :value="sel.value" />
										</el-select>
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column label="选择/设置数据" show-overflow-tooltip>
								<template #default="{ row, $index }">
									<el-form-item>
										<el-select
											:ref="
												(el) => {
													selectRefs[$index] = el;
												}
											"
											:id="row.source"
											v-if="row.sourceType === 'select'"
											:multiple="row.source === '1'"
											collapse-tags
											placeholder="请选择"
											v-model="row.sourceData"
											@click="onVisible(row, $index)"
										>
											<el-option v-for="sel in row.sourceOption" :key="sel.value" :label="sel.label" :value="sel.value" />
										</el-select>
										<el-input v-else-if="row.sourceType === 'input'" v-model="row.sourceData" placeholder="请输入内容" />
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column label="附加条件" show-overflow-tooltip>
								<template #default="{ row }">
									<el-select v-if="row.source === '10'" placeholder="请选择" v-model="row.sourceCondition" :multiple="true" collapse-tags>
										<el-option v-for="sel in row.sourceOption" :key="sel.value" :label="sel.label" :value="sel.value" />
									</el-select>
								</template>
							</el-table-column>
							<el-table-column fixed="right" align="left" :label="$t('message.page.actions')" width="100">
								<template #default="{ row }">
									<el-button size="mini" type="text" style="color: #ff3a3a" @click="onDelete(row)">删除</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-form>
				</div>
			</div>

			<el-row class="flex mt15">
				<div class="flex-margin">
					<el-button size="small" type="primary" @click="onAddRow">新增一行</el-button>
					<el-button size="small" type="success" @click="onValidate(tableRulesRef)">保存</el-button>
				</div>
			</el-row>
		</el-dialog>
		<ApproverDlg ref="approverDlgRef" @fetchData="fetchSelectData" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, nextTick, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import emailTemplateApi from '/@/api/emailTemplate/index';
import type { FormInstance } from 'element-plus';

import workflowCrmApi from '/@/api/workflowCrm/index';
import ApproverDlg from './ApproverDlg.vue';

interface DialogParams {
	action: string;
	className: string;
	nodeOptions: any;
}

interface FormModel {
	templateId: string;
	setType: string; //设置类型 TO：表示主接收人；CC:表示抄送
	source: string; //1：指定成员;2:发起人本人;3:部门主管;4:表单变量;5:调用接口;6:流程发起人;7:上一个节点的审批人;8:指定节点的相关人;9:流程的所有人;14:节点代理人
	sourceType: string;
	sourceExtend: string; //拓展参数 如当 source=8时，{lastApprover:表示最后的审批人}
	sourceData: any; //已选择
	sourceCondition: any; //条件
	sourceOption: any[]; //选项
}

export default defineComponent({
	name: 'Recipient',
	components: { ApproverDlg },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		// 定义变量内容
		const tableRulesRef = ref<FormInstance>();
		const tableRef = ref();
		const selectRef = ref([]);
		const selectRefs = reactive([]);
		const approverDlgRef = ref();
		const state = reactive({
			title: '收件人设置',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
				dataType: '',
				className: '',
				appendType: '',
			} as any,
			ruleForm: {} as FormModel,
			rules: {
				vendorId: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},

			emailOptions: [] as any[], //邮件可选择的模板
			tableData: {
				data: [] as any[],
				header: [
					{
						prop: 'setType',
						width: '',
						label: '设置类型',
						isRequired: true,
						type: 'select',
						isMultiple: false,
						dsFromOption: true,
						option: [
							{ value: 'TO', label: '收件人' },
							{ value: 'CC', label: '抄送' },
						],
					},
					{
						prop: 'source',
						width: '200',
						label: '数据来源',
						isRequired: true,
						type: 'select',
						isMultiple: false,
						dsFromOption: true,
						option: [
							{ value: '1', label: '指定成员' },
							{ value: '2', label: '节点发起人' },
							{ value: '3', label: '节点审批人' },
							{ value: '4', label: '直系主管' },
							{ value: '5', label: '部门主管' },
							{ value: '6', label: '表单变量' },
							{ value: '7', label: '调用接口' },
							{ value: '8', label: '上一个节点的审批人' },
							{ value: '9', label: '指定节点的相关人' },
							{ value: '10', label: '流程的所有人' },
							{ value: '11', label: '自定义邮箱' },
							{ value: '14', label: '节点代理人' },
						],
					},
					{
						prop: 'sourceData',
						width: '230',
						label: '设置数据',
						isRequired: false,
						type: 'select',
						isMultiple: true,
						dsFromOption: false,
						option: [
							{ value: '选项1', label: '黄金糕' },
							{ value: '选项2', label: '双皮奶' },
							{ value: '选项3', label: '蚵仔煎' },
						],
					},
					{
						prop: 'send',
						width: '60',
						label: '覆盖',
						isRequired: false,
						type: 'checkbox',
					},
				],

				option: [
					{ value: '选项1', label: '黄金糕' },
					{ value: '选项2', label: '双皮奶' },
					{ value: '选项3', label: '蚵仔煎' },
				],
			},
			rowIndex: -1,
			currentRow: {} as any,
			userOption: [],
			sourceOption: [
				{ value: '1', label: '指定成员', type: 'select' },
				{ value: '3', label: '当前节点审批人' },
				{ value: '4', label: '当前节点审批人的主管' },
				{ value: '5', label: '上一个节点的审批人' },
				{ value: '6', label: '上一个节点的审批人的主管' },
				{ value: '7', label: '指定节点的审批人', type: 'select' },
				{ value: '8', label: '指定节点的审批人的主管', type: 'select' },
				{ value: '9', label: '表单变量', type: 'select' },
				{ value: '10', label: '调用接口', type: 'input' },
				{ value: '13', label: '流程发起人' },
				{ value: '11', label: '流程的所有人' },
				{ value: '12', label: '自定义邮箱', type: 'input' },
				{ value: '14', label: '当前节点代理人', type: 'input' },
			],
			sourceTypeOption: [],
			entityOption: [],
			nodeOptions: [],
		});

		const fetchSelectData = (dataType: any, items: any) => {
			const data = items.map((a) => {
				return { value: a.id, label: a.name };
			});

			state.currentRow['sourceOption'] = data;
			state.currentRow['sourceData'] = data.map((a) => {
				return a.value;
			});
		};

		const onVisible = (row: any, index: number) => {
			state.currentRow = row;
			if (row.source === '1') {
				//使选择器的输入框失去焦点，并隐藏下拉框
				selectRefs[index].blur();
				approverDlgRef.value.setDialogParams({ multiple: true, dataType: 'User', initData: [] });
			}
		};

		const onChange = (val: any, row: any) => {
			row['sourceData'] = [];
			const option = state.sourceOption.find((a) => a.value == val);
			row.sourceType = option.type;
			if (row.source === '7' || row.source === '8') {
				row['sourceOption'] = state.nodeOptions;
			}
			if (row.source === '9' || row.source === '10') {
				row['sourceOption'] = state.entityOption;
			}
		};

		const onSelect = () => {
			//approverDlgRef.value.setDialogParams({ multiple: true, dataType:'User', initData: [] });
		};

		// 表格验证
		const onValidate = (formEl: FormInstance | undefined) => {
			//if (state.tableData.data.length <= 0) return ElMessage.warning('请先点击增加一行');
			if (!formEl) return;
			formEl.validate((valid) => {
				if (!valid) return ElMessage.warning('表格项必填未填');
				//ElMessage.success('全部验证通过');
				context.emit('fethData', state.dialogParams.dataType, state.tableData.data, state.dialogParams.appendType);
				closeDialog();
			});

			//console.log('state.tableData.data', state.tableData.data);
		};
		// 新增一行
		const onAddRow = () => {
			var item = {} as FormModel;
			state.tableData.data.push(item);
			tableRef.value.doLayout();
			nextTick(() => {
				tableRef.value.setScrollTop(1000000);
			});
		};
		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			onInitForm();
			state.dialogParams = parmas;
			if (!parmas.noticeData[parmas.appendType]) {
				parmas.noticeData[parmas.appendType] = [];
			}
			const data = parmas.noticeData[parmas.appendType];
			state.tableData.data = [...data];

			const nodeData = parmas.nodeOptions.map((a) => {
				return { value: a.id, label: a.text };
			});
			state.nodeOptions = nodeData;
			console.log('parmas.nodeOptions', state.nodeOptions);

			state.tableData.data.forEach((item) => {
				if (item.source === '7' || item.source === '8') {
					item.sourceOption = nodeData;
				}
			});

			workflowCrmApi.GetEntityProperties({ className: state.dialogParams.className }).then((rs) => {
				const data = rs.data.map((a) => {
					return { value: a.name, label: a.name };
				});
				state.entityOption = data;
				state.tableData.data.forEach((item) => {
					if (item.source === '9') {
						item.sourceOption = data;
					}
				});
			});
		};

		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};

		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			//console.log('state.tableData.data', state.tableData.data);
		};

		//删除纪录
		const onDelete = (row: any) => {
			state.tableData.data = state.tableData.data.filter((item) => item !== row);
		};

		// 重置表单
		const onInitForm = () => {
			state.isShowDialog = true;
			state.tableData.data = [];
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
		};
		return {
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onDelete,
			onInitForm,
			onValidate,
			onAddRow,
			onVisible,
			tableRulesRef,
			tableRef,
			approverDlgRef,
			selectRef,
			selectRefs,
			fetchSelectData,
			onSelect,
			onChange,
			...toRefs(state),
		};
	},
});
</script>
