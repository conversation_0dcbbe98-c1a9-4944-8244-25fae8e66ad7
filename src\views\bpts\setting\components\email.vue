<template>
	<div class="config-index-container">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px" class="mt35 mb35">
			<el-row :gutter="35">
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.popServer')">
						<el-input v-model="ruleForm.poP3Server" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.popServerPort')">
						<el-input-number v-model="ruleForm.poP3Port" :min="0" :max="99999" controls-position="right"
							class="w100" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.smtpServer')">
						<el-input v-model="ruleForm.smtpServer" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.smtpServerPort')">
						<el-input-number v-model="ruleForm.smtpPort" :min="0" :max="99999" controls-position="right"
							class="w100" />
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.emailDisplayName')">
						<el-input v-model="ruleForm.display" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.emailAccount')">
						<el-input v-model="ruleForm.account" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<el-form-item :label="$t('message.emailCommon.emailPassword')" prop="password">
						<el-input v-model="ruleForm.password" clearable type="password" autocomplete="new-password"
							@mousedown="clearPassword"></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb6">
					<!-- :active-text="$t('message.emailCommon.emailSSLEnabled')" :inactive-text="$t('message.emailCommon.emailSSLDisabled')" -->

					<el-form-item :label="$t('message.emailCommon.emailSSL')">
						<el-switch v-model="ruleForm.isSSL" inline-prompt></el-switch>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
					<el-form-item>
						<el-button v-auth="'systemSetting.Edit'" type="primary" @click="onSave" size="small">{{
							$t('message.page.buttonSave') }}</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';

export default defineComponent({
	name: 'tokenCom',
	components: {},
	props: {
		emailObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			ruleForm: {} as any,
			rules: {
				tokenExp: [{ required: true, message: 'Please input', trigger: 'blur' }],
				password: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Email: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then(() => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.emailObj,
			() => {
				state.ruleForm = props.emailObj || {};
			}
		);
		// 页面加载时
		onMounted(() => {
			// console.log(state.ruleForm)
		});

		const clearPassword = () => {
			state.ruleForm.password = '';
		};

		return {
			onSave,
			clearPassword,
			...toRefs(state),
		};
	},
});
</script>
<style scoped>
/* ::v-deep .el-switch__core {
	width: 200px;
	height: 24px;
	border-radius: 100px;
	border: none;
} */
</style>
