﻿<template>
	<div class="workflow-edit-container">
		<el-dialog :title="title" v-model="isShowDialog" width="509px">
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="审批人员" prop="users">
							<el-select
								v-model="ruleForm.nextApprovalValue"
								multiple
								ref="userRef"
								placeholder="Select"
								style="width: 100%"
								@visible-change="onVisible"
							>
								<el-option v-for="item in userData" :key="item.itemId" :label="item.itemName" :value="item.itemId" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="转审备注" prop="msg">
							<el-input v-model="ruleForm.msg" type="textarea" placeholder="请输入转审备注" maxlength="150"> </el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">取消</el-button>
					<el-button :loading="saveLoading" type="primary" @click="onSubmit" size="small">确定</el-button>
				</span>
			</template>
		</el-dialog>
		<SelectUser ref="selectUserRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, getCurrentInstance, defineComponent, ref } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import SelectUser from '/@/views/bpts/user/components/SelectUser.vue';
import workflowCrmApi from '/@/api/workflowCrm/index';

export default defineComponent({
	name: 'workflowCreateOrEdit',
	components: { SelectUser },
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const selectUserRef = ref();
		const userRef = ref();
		const state = reactive({
			title: 'Create Workflow',
			isShowDialog: false,
			saveLoading: false,
			deleteLoading: false,
			dialogParams: {
				action: '',
				id: -1,
			},
			ruleForm: {
				crmWorkflowId: 0,
				elsaInstancesId: '',
				executionType: '',
				nextApprovalType: '0',
				nextApprovalValue: [],
				msg: '',
			},
			permissions: [],
			rules: {
				msg: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
				nextApprovalValue: [{ required: true, message: '请选择转办人', trigger: 'blur' }],
			},
			userData: [], //部门主管 数据源
		});
		// 打开弹窗
		const openDialog = (obj: any, permissions: any, formData: any) => {
			state.ruleForm = { ...obj };
			state.isShowDialog = true;
			state.title = '转办';
			state.permissions = permissions;
			state.ruleForm.Data = formData;
		};
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}

				var obj = state.ruleForm;
				console.log('state.permissions', state.permissions);
				console.log('obj', obj);
				var permission = state.permissions.find((item) => {
					return item.permissionType === 'AssignTo';
				});
				obj.taskId = permission.taskId;
				obj.activityId = permission.activityId;
				obj.executionType = permission.permissionType;
				state.saveLoading = true;
				workflowCrmApi
					.Workflowexecution(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		const onVisible = () => {
			userRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
			selectUserRef.value.openDialog({ multiple: false, dataType: 'User', initData: state.ruleForm.nextApprovalValue });
		};
		const setSelectItem = (items: any) => {
			const newItem = [...items]; //复制数组
			state.userData = items;
			state.ruleForm.nextApprovalValue = newItem.map((a) => {
				return a.itemId;
			});
		};
		return {
			selectUserRef,
			userRef,
			openDialog,
			closeDialog,
			onCancel,
			onSubmit,
			onVisible,
			setSelectItem,
			...toRefs(state),
		};
	},
});
</script>
