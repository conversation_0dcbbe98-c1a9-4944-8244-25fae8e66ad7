# 线上环境
ENV = 'production'

# .8服务器发布的webapi后端访问地址
#  VITE_API_URL = 'http://192.168.1.8:7008/'

# 本地发布的webapi后端访问地址
# VITE_API_URL = 'http://127.0.0.1:7008/'

# 日本服务器发布的webapi后端访问地址，直接运行exe，然后用nginx做反向代理
# VITE_API_URL = 'https://demo.docxtract.com:10099/'

# 日本服务器发布的webapi后端访问地址，用IIS部署的后台,已废弃
# VITE_API_URL = 'https://demo.docxtract.com/'

# 日本服务器发布的webapi后端访问地址，用IIS部署的后台
# VITE_API_URL = 'https://cmapi.docxtract.com/'

# 日本服务器发布的配置虚拟目录，二级目录
# VITE_PUBLIC_PATH = /TicketDemo/

# AP服务器发布的webapi后端访问地址，用IIS部署的后台
# VITE_API_URL = 'https://ticketmgmtapi.docxtract.com/'

# 如果发布的不是虚拟目录,要开启这个清空
VITE_PUBLIC_PATH = 