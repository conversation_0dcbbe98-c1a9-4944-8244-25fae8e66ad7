﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class companyApi extends BaseApi {
	DetailByOrgId(orgId?: any) {
		return request({
			url: this.baseurl + 'DetailByOrgId/' + orgId,
			method: 'get',
		});
	};

	UpdateCompanyIndustry(data?: any) {
		return request({
			url: this.baseurl + 'UpdateCompanyIndustry',
			method: 'post',
			data,
		});
	}

	DetailV2(key: any, key1: any) {
		return request({
			url: this.baseurl + 'Detail/' + key + '/' + key1,
			method: 'get',
			params: {},
		});
	}
}

export default new companyApi('/api/company/', 'companyId');
