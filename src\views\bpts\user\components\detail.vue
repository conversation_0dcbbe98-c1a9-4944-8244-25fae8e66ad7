﻿<template>
	<el-main style="padding: 0 20px">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="用户名称">{{ info.userName }}</el-descriptions-item>
			<el-descriptions-item label="真实姓名">{{ info.realName }}</el-descriptions-item>
			<el-descriptions-item label="所属部门">{{ info.orgName }}</el-descriptions-item>
			<el-descriptions-item label="性别">
				<label v-if="info.sex === 0">保密</label>
				<label v-if="info.sex === 1">男</label>
				<label v-if="info.sex === 2">女</label>
			</el-descriptions-item>
			<el-descriptions-item label="年龄">{{ info.age }}</el-descriptions-item>
			<el-descriptions-item label="生日">{{ formatStrDate(info.birth, 'YYYY-mm-dd') }}</el-descriptions-item>
			<el-descriptions-item label="地址">{{ info.address }}</el-descriptions-item>
			<el-descriptions-item label="状态">{{ info.status }}</el-descriptions-item>
			<el-descriptions-item label="备注">{{ info.remark }}</el-descriptions-item>
			<el-descriptions-item label="创建时间">{{ formatStrDate(info.createTime) }}</el-descriptions-item>
			<el-descriptions-item label="最后修改时间">{{ formatStrDate(info.updateTime) }}</el-descriptions-item>
			<el-descriptions-item label="最后出错时间">{{ formatStrDate(info.lastErrorTime) }}</el-descriptions-item>
			<el-descriptions-item label="出错次数">{{ info.errorCount }}</el-descriptions-item>
		</el-descriptions>
	</el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
	name: 'apiDetail',
	props: {
		info: Object,
	},
	setup(props) {
		const state = reactive({
			isShowDialog: false,
			obj: {},
		});
		// 页面加载时
		onMounted(() => {
			window.console.log('props', props.info);
		});
		return {
			formatStrDate,
			...toRefs(state),
		};
	},
});
</script>


                        
        
        