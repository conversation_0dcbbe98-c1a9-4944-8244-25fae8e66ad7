﻿<template>
	<el-container class="userDelegate-index-container tablelist" style="height: 100%">
		<el-dialog title="Delegate List" width="1280px" v-model="isShowDialog" :close-on-click-modal="false" draggable
			destroy-on-close>
			<DelegateList :info="dialogParams" />
		</el-dialog>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, ref, defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';

import DelegateList from '/@/views/bpts/userDelegate/delegateList.vue';
interface DialogParams {
	action: string;
	id: number;
}
export default defineComponent({
	name: 'userDelegateIndex',
	components: { DelegateList },
	setup() {
		const { t } = useI18n();
		const state = reactive({
			isShowDialog: false,
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					userDelegateName: '',
					userTrusteeName: '',
					dateStart: '',
					dateEnd: '',
					ticket_Customer_List: [],
					approverType: '',
				},
			},
			dialogParams: {
				action: '',
				id: -1,
			}
		});
		// 关闭弹窗
		const closeDialog = () => {
			state.isShowDialog = false;
		};

		// 打开弹窗
		const openDialog = (parmas: DialogParams) => {
			state.dialogParams = parmas;
			state.isShowDialog = true;
		};

		return {
			closeDialog,
			openDialog,
			...toRefs(state),
		};
	},
});
</script>
