﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class menuApi extends BaseApi {
	Tree(params?: any) {
		return request({
			url: this.baseurl + 'Tree',
			method: 'get',
			params,
		});
	}

	GetMenuApi(menuId: number) {
		return request({
			url: '/api/menuApi/Get',
			method: 'get',
			params: {
				menuId,
			},
		});
	}

	SynRoute(data: any) {
		return request({
			url: '/api/menu/SynRoute',
			method: 'post',
			data,
		});
	}
	PermissionTree(params?: any) {
		return request({
			url: '/api/menu/PermissionTree',
			method: 'get',
			params,
		});
	}
}

export default new menuApi('/api/menu/', 'id');
