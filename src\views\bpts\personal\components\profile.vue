<template>
	<div class="profile-content">
		<el-card>
			<template #header>My Profile</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="top" label-width="130px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.userName')" prop="title" class="form-control">
							<el-input v-model="ruleForm.userName" :readonly="true" class="readonly" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.realName')" prop="realName" class="form-control">
							<el-input v-model="ruleForm.realName" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.mobile')" prop="mobile" class="form-control">
							<el-input v-model="ruleForm.mobile" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.lastName')" prop="lastName" class="form-control">
							<el-input v-model="ruleForm.lastName" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.email')" prop="email" class="form-control">
							<el-input v-model="ruleForm.email" :readonly="true" class="readonly" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.roleAssign')" class="form-control">
							<el-select v-model="ruleForm.roles" clearable multiple :disabled="true"
								:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%">
								<el-option v-for="item in roleOptions" :key="item.id" :label="item.name"
									:value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.timeZone')" class="form-control">
							<el-select v-model="ruleForm.timeZone"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable style="width: 100%">
								<el-option v-for="item in timeZoneOptions" :key="item.itemName" :label="item.itemName"
									:value="item.itemName" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.jobTitle')" clearable prop="jobTitle"
							class="form-control">
							<el-input v-model="ruleForm.jobTitle" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.userFields.orgName')"  clearable prop="orgName" class="form-control">
							<el-select v-model="ruleForm.orgName" :placeholder="$t('message.page.selectKeyPlaceholder')"
								clearable disabled="disabled" >
								<el-option v-for="item in ruleForm.orgNameData" :label="item.label" :value="item.value"
									:key="item.orgId"></el-option></el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">

						<el-form-item :label="$t('message.userFields.userType')" prop="userType">
							<el-select v-model="ruleForm.userType"
								:placeholder="$t('message.page.selectKeyPlaceholder')" clearable disabled="disabled"
								 class="form-control">
								<el-option v-for="item in ruleForm.userTypeData" :label="item.itemName"
									:value="item.itemName" :key="item.itemId"></el-option></el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">

						<el-form-item :label="$t('message.userFields.groups')" prop="groups" class="form-control">
							<el-select v-model="ruleForm.groups" multiple
								:placeholder="$t('message.page.selectKeyPlaceholder')" disabled="disabled"
								style="width: 100%">
								<el-option v-for="item in ruleForm.groupsData" :key="item.id" :label="item.group_Name"
									:value="item.id" />
							</el-select>
						</el-form-item>

					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item :label="$t('message.userFields.IncidentEmailNotfications')" clearable
							prop="Default" class="form-control">
							<el-button type="text"><el-switch v-model="ruleForm.isEmail" class="ml-2"
									:label="$t('message.userFields.IncidentEmailNotfications')" @change="onSubmitDefalutEmail"/></el-button>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_msg">
							<ul class="message-list">
								<li v-for="message in messages" :key="message.msg"
									:class="`message-item ${message.type === 'error' ? 'error' : ''}`">
									<span class="dot"></span> {{ message.msg }}
								</li>
							</ul>
						</el-form-item>
					</el-col>


					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item class="el-form-item_btn">
							<el-button :loading="saveLoading" color="#626aef" type="primary" @click="onSubmit">{{
								$t('message.profileButtons.SaveProfileChanges')
							}}</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent, watch, getCurrentInstance, ref } from 'vue';
import { ElMessageBox, ElMessage, ElTreeSelect } from 'element-plus';

import userApi from '/@/api/user';
import roleApi from '/@/api/role';
import dictItemApi from '/@/api/dictItem/index';
//import wWERemitToProjectApi from '/@/api/wWERemitToProject/index';

//import wWEProjectApi from '/@/api/wWEProject/index';
//import wWERemitToApi from '/@/api/wWERemitTo/index';
import groupsApi from '/@/api/cmGroups/index';
import { isNotEmptyOrNull } from '/@/utils';
import { IApiResultMessage } from '/@/types/models';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
export default defineComponent({
	name: 'Profile',
	props: {
		info: Object,
	},
	setup(props, context) {
		const { proxy } = getCurrentInstance() as any;
		const treeRef = ref<InstanceType<typeof ElTreeSelect>>();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		const state = reactive({
			messages: [] as IApiResultMessage[], // 初始为空数组
			saveLoading: false,
			roleOptions: [],
			remitToOptions: [],
			projectsOptions: [],
			timeZoneOptions: [],
			selectedRemitToProject: [],
			remitToProjectData: [],
			ruleForm: {
				userName: '',
				firstName: '',
				lastName: '',
				mobile: '',
				jobTitle: '.NET Developer',
				email: '',
				location: '',
			} as any,
			rules: {
				firstName: [{ required: true, message: 'Please enter your first name.', trigger: 'blur' }],
				lastName: [{ required: true, message: 'Please enter your last name.', trigger: 'blur' }],
				mobile: [
					{ required: false, message: 'Please input', trigger: 'blur' },
					{
						validator: (rule, value, callback) => {
							if (/^(?:[0-9]\d*)$/.test(value) == false && isNotEmptyOrNull(value)) {
								callback(new Error('Phone number must be numeric and may not contain special characters or letters.'));
							} else {
								callback();
							}
						},
						trigger: 'blur',
					},
				],
			},
		});

		const changeRemitTo = (wwE_RemitTo_Id: any) => {
			state.projectsOptions = [];
			if (!wwE_RemitTo_Id) {
				return;
			}

			const params = {
				wwE_RemitTo_Id,
			};

			// wWEProjectApi.GetList(params).then((rs) => {
			// 	state.projectsOptions = rs.data.map((item: any) => {
			// 		return {
			// 			...item,
			// 			display: `${item.project_Code} - ${item.project_Name}`,
			// 		};
			// 	});
			// });
		};

		const addMessage = (msg: string, type: 'success' | 'error' | 'info' = 'error') => {
			state.messages.push({ msg, type });
		};

		const onSubmit = () => {
			state.messages = [];
			proxy.$refs.ruleFormRef.validate((valid: any, invalidFields: any) => {
				if (!valid) {
					Object.keys(invalidFields).forEach((field) => {
						console.log('invalidFields', invalidFields);
						invalidFields[field].forEach((error: any) => {
							let msg = error.message;
							const errorMessage = msg || `${field} is invalid`;
							addMessage(errorMessage, 'error');
						});
					});
					return;
				}

				var obj = {
					...Object.assign({}, state.ruleForm),
				};

				console.log(obj);

				state.saveLoading = true;

				userApi
					.SaveProfileChanges(obj)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData', obj.id);
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};

		const loadNode = () => {
			// wWERemitToProjectApi.GetRemitToProjectTreeList().then((rs) => {
			// 	state.remitToProjectData = rs.data;
			// 	state.remitToProjectData.forEach((item: any) => {
			// 		if (item.parentId) {
			// 			item.id = `${item.parentId}_${item.nodeId}`;
			// 		} else {
			// 			item.id = item.nodeId;
			// 		}
			// 		if (item.children) {
			// 			item.children.forEach((child: any) => {
			// 				child.id = `${child.parentId}_${child.nodeId}`;
			// 			});
			// 		}
			// 	});
			// });
		};

		const handleCheckChange = () => {
			console.log('treeRef', treeRef.value);
			const data = treeRef.value!.getCheckedNodes();
			console.log('data', data);
		};

		watch(
			() => props.info,
			(newVal, oldVal) => {
				state.ruleForm = newVal;
			}
		);
		watch(
			() => state.ruleForm,
			(newRuleForm, oldRuleForm) => {
				state.messages = [];
			},
			{ deep: true }
		);
		const onSubmitDefalutEmail = (isEmail: boolean) => {
			var obj = {
				id: userInfos.value.userId,
				isEmail: isEmail,
			};
			userApi
				.SetUserDefalutEmail(obj)
				.then((rs) => {
					ElMessage.success('Succeed');
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => { });
		};
		// 页面加载时
		onMounted(() => {
			if (props.info) {
				state.ruleForm = props.info;
				console.log('props.info:', state.ruleForm);
			}

			roleApi.GetList().then((rs) => {
				state.roleOptions = rs.data;
			});

			// wWERemitToApi.GetList().then((rs) => {
			// 	state.remitToOptions = rs.data.map((item: any) => {
			// 		return {
			// 			...item,
			// 			display: `${item.remitTo_Code} - ${item.remitTo_Name}`,
			// 		};
			// 	});
			// });

			var dictArr = ['Time Zone'];
			dictItemApi.Many(dictArr).then((rs: any) => {
				state.timeZoneOptions = rs.data?.find((a: any) => {
					return a.dictValue === 'Time Zone';
				})?.items;
			});

			groupsApi.GetList().then((rs) => {
				state.ruleForm.groupsData = rs.data;
			});
		});
		return {
			treeRef,
			handleCheckChange,
			onSubmitDefalutEmail,
			onSubmit,
			loadNode,
			changeRemitTo,
			...toRefs(state),
		};
	},
});
</script>


<style scoped>
.lpx-content {
	max-width: 1280px !important;
	padding: 1.25em 2em 3em;
	margin: 0 auto;
}

.lpx-avatar-img-lg {
	height: 144px;
	width: 144px;
	border-radius: 144px;
}

.mb-0 {
	margin-bottom: 0 !important;
}

.mb-3 {
	margin-bottom: 1.5rem !important;
}

.mt-2 {
	margin-top: 0.75rem !important;
}

.pb-3 {
	padding-bottom: 1.5rem !important;
}

.card .card-body img {
	max-width: 100%;
}

.text-center {
	text-align: center !important;
}

.border-bottom {
	border-bottom: 1px solid #e8eef3;
}

.card .card-title {
	font-size: 1.25em;
	font-weight: 500;
	color: #062a44;
}

.text-muted {
	--bs-text-opacity: 1;
	color: #325168 !important;
	font-size: 12px;
}

.text-muted {
	opacity: 0.8;
}

.fw-normal {
	font-weight: 400 !important;
}

h5,
.h5 {
	font-size: 13px !important;
}

.menu_btn_wrapper {
	display: flex;
	flex-direction: column;
}

.menu_btn {
	width: 100% !important;
}

.button_container {
	margin-bottom: 10px;
}
</style>