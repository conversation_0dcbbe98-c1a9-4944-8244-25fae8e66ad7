﻿<template>
	<div class="role-edit-container">
		<el-dialog :title="title" v-model="state.isShowDialog" width="400px" :close-on-click-modal="false" draggable>
			<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="state.rules" label-width="90px" label-position="top">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.roleFields.name')" prop="name">
							<el-input v-model="state.ruleForm.name" :placeholder="$t('message.page.searchKeyPlaceholder')" />
						</el-form-item>
					</el-col>
					
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item prop="customer">
							<template #label>
								<span>{{ $t('message.ticketFields.customer') }}</span>
							</template>
							<el-tree-select ref="localCompanyTree"
								:disabled=" (state.ticketId > 0)"
								v-model="state.ruleForm.customer" :data="state.customerOpts"
								:render-after-expand="false" node-key="value" show-checkbox
								:check-strictly="true" check-on-click-node style="width: 100%"
								:auto-expand-parent="true" :default-expand-all="true" filterable
								@check="checkChangeCustomer" @clear="clearCustomer" clearable
								:placeholder="$t('message.page.selectKeyPlaceholder')" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item prop="project">
							<template #label>
								<span>{{ $t('message.ticketFields.project') }}</span>
							</template>
							<el-tree-select ref="localProjectTree"
								:disabled=" (state.ticketId > 0)"
								v-model="state.ruleForm.project" :data="state.projectOpts"
								:render-after-expand="false" node-key="value" show-checkbox
								:check-strictly="true" check-on-click-node style="width: 100%"
								:auto-expand-parent="true" :default-expand-all="true" multiple filterable
								@current-change="checkChangeProject" @clear="clearProject" clearable
								:placeholder="$t('message.page.selectKeyPlaceholder')" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">
						{{ $t('message.page.buttonCancel') }}
					</el-button>
					<el-button :loading="state.saveLoading" type="primary" @click="onSubmit" size="small">
						{{ $t('message.page.buttonSave') }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted, defineComponent } from 'vue';
import { isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessageBox, ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';

import organizationApi from '/@/api/organization/index';
import openItemApi from '/@/api/cmOpenItem/index';
import { useI18n } from 'vue-i18n';

defineProps({
  title: {
    type: String,
    default: '',
  },
});
const localProjectTree = ref();
const localCompanyTree = ref();

const { t } = useI18n();
const emit = defineEmits(['refreshData']);
const { proxy } = getCurrentInstance() as any;
const state = reactive({
	title: t('message.roleFields.createRole'),
	isShowDialog: false,
	saveLoading: false,
	deleteLoading: false,
	ruleForm: {
		id: 0,
		name: '', //
		customer: '', //
		project: [] as any, //
	},
	rules: {
		name: [{ required: true, message: 'Please input', trigger: 'blur' }],
		customer: [{ required: true, message: 'Please select', trigger: 'blur' }],
		project: [{ required: true, message: 'Please select', trigger: 'blur' }],
	},
	ticketId: 0,
	customerOpts: [] as any,
	projectOpts: [] as any,
});

const checkChangeCustomer = (data: any) => {
	if (data.disabled) {
		return;
	}

	updateProjectOptions(data.value);
	localCompanyTree.value.blur();
};

const clearCustomer = () => {
	state.projectOpts = [];
	localProjectTree?.value?.setCheckedKeys([]);
	state.ruleForm.project = [];
};

function updateProjectOptions(dataValue: string) {
	state.projectOpts = [];
	state.ruleForm.project = [];
	const { currentNode, parentNode, siblingNodes } = findParentAndSiblings(state.customerOpts, dataValue);

	if (currentNode) {
		if (currentNode.nodeType === "ProjectNode") {
			state.projectOpts = siblingNodes;
			state.ruleForm.customer = parentNode ? parentNode.value : '';
			state.ruleForm.project.push(currentNode.value);
		} else {
			state.projectOpts = siblingNodes.filter((item: any) => {
				return item.nodeType === "ProjectNode"
			});

			state.ruleForm.customer = currentNode.value;
		}
	}

	// if (state.projectOpts.length > 0) {
	// 	state.showTicketProject = true
	// } else {
	// 	state.showTicketProject = false
	// }
}

const findParentAndSiblings = (nodes: any[], value: string): any => {
	let result = { currentNode: null, parentNode: null, siblingNodes: [] };

	for (const node of nodes) {
		if (node.value == value) {
			result = { currentNode: node, parentNode: null, siblingNodes: node.children };
		}

		if (node.children) {
			const foundIndex = node.children.findIndex((child: { value: string; }) => child.value === value);

			if (foundIndex > -1) {
				var findNode = node.children[foundIndex]

				if (findNode.nodeType == "ProjectNode") {
					result = {
						currentNode: findNode,
						parentNode: node,
						siblingNodes: node.children
					}
				} else {
					result = {
						currentNode: findNode,
						parentNode: node,
						siblingNodes: findNode.children
					}
				}
			} else {
				const tempResult = findParentAndSiblings(node.children, value);
				if (tempResult.currentNode) {
					result = tempResult;
				}
			}
		}
	}

	return result;
};

const checkChangeProject = (data: any) => {
	if (data.disabled) {
		return;
	}
	// state.ruleForm.project.push(data.value);

	// localProjectTree.value.blur();
};

const clearProject = () => {
	state.ruleForm.project = [];
};
// 打开弹窗
const openDialog = (parmas: any) => {
	onInitForm();
	let formData = cloneDeep(parmas);
	getOpenItemMappings(formData, () => {
		state.isShowDialog = true;
    });
};
const getOpenItemMappings = async (row: any, callback: () => void) => {
  if(row.value){
    await openItemApi.Detail(row.value).then((rs:any) => {
      if(rs.data!=null){
        console.log(rs.data);
		state.ruleForm.id = rs.data.id;	
		state.ruleForm.name = rs.data.name;		
	    updateProjectOptions(rs.data.customer);
		for(var item of rs.data.itemProject){
		   state.ruleForm.project.push(item.project);
		}
      }
    });
  }
  callback(); //执行回调函数
};
// 关闭弹窗
const closeDialog = () => {
	onInitForm();
	state.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 保存
const onSubmit = () => {
	proxy.$refs.ruleFormRef.validate((valid: any) => {
		if (!valid) {
			return;
		}
        
		var obj = { ...state.ruleForm };
		obj.project = [];
		for(var item of state.projectOpts){
			if(state.ruleForm.project.includes(item.value)){
				obj.project.push({project: item.value, projectName: item.label});
			}
		}
		state.saveLoading = true;

		openItemApi
		.Save(obj)
		.then((rs) => {
			ElMessage.success('Succeed');
			emit('refreshData',rs.data);
			closeDialog();
		})
		.catch((rs) => {
			ElMessage.error(rs.resultMsg || rs.toString());
		})
		.finally(() => {
			state.saveLoading = false;
		});
	});
};
// 重置表单
const onInitForm = () => {
	if (proxy.$refs.ruleFormRef) {
		proxy.$refs.ruleFormRef.resetFields();
	}
	state.ruleForm = {
		id: 0,
		name: '', //
		customer: '', //
		project: [] as any, //
	};
};
onMounted(async () => {
	await organizationApi.GetCompanyProjectTreeByUserId({ TicketId: 0 }).then((rs) => {
		state.customerOpts = rs.data.treeNodeList;
	});
});

defineExpose({
	openDialog,
});
</script>
