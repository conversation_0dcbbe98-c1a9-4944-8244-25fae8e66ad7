/** 根据输入信息获取邮箱信息 */
export const getEmailData = (value,isChange=true) => {
  value = value.replace(';','');
  //console.log('getEmailData', value)
  // debugger
  const reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
  let data = null

  // 如果数据为空则邮件无信息，做删除处理
  if (value && value !== '' && value.trim() !== '') {
    const inputData = value.trim()
    let name = null
    let email = null

    if (!reg.test(inputData)) {
      // 如果有 < 且有 > 则处理数据，根据结果再判断是否满足邮件格式
      const stNum = inputData.split('<').length - 1
      const etNum = inputData.split('>').length - 1
      const stIndex = inputData.indexOf('<')
      const etIndex = inputData.indexOf('>')
      // < 或者 > 只存在一个且 < 的位置小于 > 的位置
      if (stNum === 1 && etNum === 1 && stIndex < etIndex) {
        const inputName = inputData.substr(0, stIndex)
        const inputEmail = inputData.substr(stIndex + 1).replaceAll('>', '')
        if (reg.test(inputEmail)) {
          name = inputName
          email = inputEmail
        }
      } else {
        email = inputData
      }
    } else {
      email = inputData
      name = email.split('@')[0]
    }
    if (name === '' && email !== '') name = email.split('@')[0]
    data = {
      content: name && email ? `${name}<${email}>` : inputData,
      name: name ? name.trim() : null,
      email: email ? email.trim() : null,
      status: name && email ? 'normal' : 'err',
      isChange
    }
  }
  return data
}

/** 根据接口数据返回邮件用户列表 */
export const getEmailUserList = (list,isChange=true) => {
  const result = []
      list.forEach(item => {
          result.push(getEmailData(item,isChange))
      })
  return result
}
