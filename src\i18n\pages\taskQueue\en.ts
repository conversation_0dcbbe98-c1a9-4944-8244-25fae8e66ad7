﻿export default {
	taskQueueSearch: {
        //查询区域
		searchKeyPlaceholder: 'Please input',
	},
	taskQueueButtons: {
    //非按钮
	},
	taskQueueFields:{
    //table 列名
     id:'Id',
     name:'Name',
     jobGroup:'JobGroup',
     cron:'Cron',
     assemblyName:'AssemblyName',
     className:'ClassName',
     remark:'Remark',
     runTimes:'RunTimes',
     beginTime:'BeginTime',
     endTime:'EndTime',
     triggerType:'TriggerType',
     intervalSecond:'IntervalSecond',
     cycleRunTimes:'CycleRunTimes',
     isStart:'IsStart',
     jobParams:'JobParams',
     isDeleted:'IsDeleted',
     createTime:'CreateTime',
		
	},
    taskCommon:{
        executeClass:'Execute Class',
        TimingRules:'Timing rules',
        Running:'Running',
        deactivated:'Deactivated',
        AddingScheduledTasks:'Adding Scheduled Tasks',
        Taskschedulinglog:'Task scheduling log',

    }
};




                        
        
        