export default {
	groupSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
	},
	groupButtons: {
		//非按钮
		createGroup: 'Create Groups',
	},
	groupFields: {
		//table 列名
		id: 'Id',
		parentId: 'ParentId',
		path: 'Route Path',
		name: 'Groups Name',
		description: 'Description',
		status: 'Status',
		users: 'Assignee',
		Default: 'Set as default group',
		ShowDefault: 'Default group',
		DefaultText: 'Team members will automatically be assigned to this group when they\'re added to BPTS. There can only be on e default group, and it\'s currently.',
		titleCreate: 'Create group',
		titleEdit: 'Edit group'
		
	},
	groupEditFields:{
		parentMenu:'Parent Menu',
		routerName:'Router Name',
	},
	groupCommonFields:{
		PrivilegeBelongsTo:'Privilege Belongs To',
		PermissionName:'Permission Name',
		DisplayName:'Display Name',
		createPermission:'Create Permission',
		editPermission:'Edit Permission',
	}
};
