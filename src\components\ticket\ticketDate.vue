<template>
	<el-date-picker
		ref="myDate"
		v-model="propValue"
		type="date"
		:placeholder="placeholder"
		:format="dynamic_format"
		:size="size"
		value-format="MM/DD/YYYY"
		@keydown="keyup"
		@focus="focus"
		@blur="inputValue"
		@change="select"
		:clearable="true"
		@clear="select"
	>
	</el-date-picker>
</template>
<script lang="ts" setup>
import { watch, defineEmits, defineProps, ref, getCurrentInstance } from 'vue';



const props = defineProps(['input', 'placeholder']);
const emit = defineEmits(['update:input', 'customChange', 'customClear']);

const { proxy } = <any>getCurrentInstance();

const propValue = ref(props.input);

const placeholder = ref('Please input');


watch(() => props.input, (v) => {
	if(v == '' || v == null){
		propValue.value = '';
		currentVal.value = '';
	}else{
		currentVal.value = v;
		inputValue();
	}
	
})

if (props.placeholder) {
	placeholder.value = props.placeholder;
}

const currentVal = ref('');

const dynamic_format = ref('MM/DD/YYYY');

const focus = () => {
	
};

const select = () => {
	if (propValue.value != null) {
		emit('customChange');
		currentVal.value = propValue.value.replace('/', '').replace('/', '');
	} else {
		currentVal.value = '';
	}
};

const keyup = (v) => {
	if (currentVal.value == '' || (propValue.value != null && propValue.value.length > 0)) {
		propValue.value = '';
		currentVal.value = '';
	}
	if (v.key == 'Backspace') {
		if (currentVal.value.length <= 1) {
			currentVal.value = '';
		} else {
			currentVal.value = currentVal.value.substring(0, currentVal.value.length - 1);
		}
	} else if ('0123456789'.includes(v.key)) {
		currentVal.value = currentVal.value + v.key;
	}
};

const inputValue = () => {
	if (currentVal.value != null && currentVal.value.indexOf('/') == -1) {
		if (currentVal.value.length == 6) {
			propValue.value = currentVal.value.substring(0, 2) + '/' + currentVal.value.substring(2, 4) + '/' + '20' + currentVal.value.substring(4);
		} else if (currentVal.value.length == 8) {
			propValue.value = currentVal.value.substring(0, 2) + '/' + currentVal.value.substring(2, 4) + '/' + currentVal.value.substring(4);
		} else {
			if(propValue.value == ''){
				propValue.value = '';
			}
		}
	} else {
		dynamic_format.value = 'MM/DD/YYYY';
		propValue.value = currentVal.value;
	}
	emit('update:input', propValue.value);
	currentVal.value = '';
};


</script>
