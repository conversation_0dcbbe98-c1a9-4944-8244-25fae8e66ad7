// versionUtils.ts
import fs from 'fs';
import path from 'path';

// 递增版本号的方法
export const incrementVersion = (version: string): string => {
  const versionParts = version.split('.').map(Number);

  if (versionParts.length === 3) {
    versionParts[2] += 1;
    if (versionParts[2] >= 10) {
      versionParts[2] = 0;
      versionParts[1] += 1;
      if (versionParts[1] >= 10) {
        versionParts[1] = 0;
        versionParts[0] += 1;
      }
    }
  }

  return versionParts.join('.');
};

// 更新版本号的方法
export const updateVersionJson = (publicPath: string, distPath: string) => {
  const filePath = path.resolve(publicPath, 'version.json');
  if (fs.existsSync(filePath)) {
    const existingData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    let version = existingData.version;
    if (version) {
      version = incrementVersion(version);
    }
    existingData.version = version;

    // 更新 public 文件夹中的版本号 - 使用原子写入避免竞态条件
    const jsonContent = JSON.stringify(existingData, null, 2);
    const tempFilePath = `${filePath}.tmp.${Date.now()}`;
    try {
      fs.writeFileSync(tempFilePath, jsonContent);
      fs.renameSync(tempFilePath, filePath);
      console.log('version.json file in public folder updated successfully!');
    } catch (error) {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      throw error;
    }

    // 同步更新 dist 文件夹中的版本号 - 使用原子写入避免竞态条件
    const distFilePath = path.resolve(distPath, 'version.json');
    const distTempFilePath = `${distFilePath}.tmp.${Date.now()}`;
    try {
      fs.writeFileSync(distTempFilePath, jsonContent);
      fs.renameSync(distTempFilePath, distFilePath);
      console.log('version.json file in dist folder updated successfully!');
    } catch (error) {
      // 清理临时文件
      if (fs.existsSync(distTempFilePath)) {
        fs.unlinkSync(distTempFilePath);
      }
      throw error;
    }
  } else {
    console.log('version.json file does not exist in public folder.');
  }
};
