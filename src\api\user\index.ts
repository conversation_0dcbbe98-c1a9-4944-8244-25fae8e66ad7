﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class userInfo<PERSON><PERSON> extends BaseApi {

    Tree(params?: any) {
        return request({
            url: this.baseurl + 'Tree',
            method: 'get',
            params,
        });
    }

    GetByDepId(depId: number) {
        return request({
            url: this.baseurl + 'GetByDepId/' + depId,
            method: 'get',
            params: {},
        });
    }

    GetSubordinates(userId: number) {
        return request({
            url: this.baseurl + 'GetSubordinates/' + userId,
            method: 'get',
            params: {},
        });
    }

    GetApproverUsers(params?: any) {
        return request({
            url: this.baseurl + 'GetApproverUsers',
            method: 'get',
            params,
        });
    }

    GetSelectUsers(params?: any) {
        return request({
            url: this.baseurl + 'GetSelectUsers',
            method: 'get',
            params,
        });
    }

    //管理员初始化用户密码
    AdminResetPassword(data: any) {
        return request({
            url: this.baseurl + 'AdminResetPassword',
            method: 'post',
            data,
        });
    }

    ForgetPwd(data : any){
        return request({
            url: this.baseurl + 'ForgetEmail',
            method: 'post',
            data,
        })
    }

    //找回密码 找回方式 0.1.2
    Retrievepassword(data: any) {
        return request({
            url: this.baseurl + 'Retrievepassword/',
            method: 'post',
            data,
        });
    }
    UserResetPassword(data: any) {
        return request({
            url: this.baseurl + 'UserResetPassword',
            method: 'post',
            data,
        });
    }
    SetUserDefalutEmail(data: any) {
        return request({
            url: this.baseurl + 'SetUserDefalutEmail',
            method: 'post',
            data,
        });
    }
    UpdateUserEmail(data: any) {
        return request({
            url: this.baseurl + 'UpdateUserEmail',
            method: 'post',
            data,
        });
    }
    
    UpdateUserLanguage(data: any) {
        return request({
            url: this.baseurl + 'UpdateLanguage',
            method: 'post',
            data,
        });
    }

    UpdateUserName(data: any) {
        return request({
            url: this.baseurl + 'UpdateUserName',
            method: 'post',
            data,
        });
    }
    GetListByCategoryAndCustomer(params?: any) {
        return request({
            url: this.baseurl + 'GetListByCategoryAndCustomer',
            method: 'get',
            params,
        });
    }

    GetAllGroupUserList(params?: any) {
        return request({
            url: this.baseurl + 'GetAllGroupUserList',
            method: 'get',
            params,
        });
    }

    GetAllGroupUserListV2(params?: any) {
        return request({
            url: this.baseurl + 'GetAllGroupUserListV2',
            method: 'post',
            data:params,
        });
    }
    GetAllGroupUserListV5(params?: any) {
        return request({
            url: this.baseurl + 'GetAllGroupUserListV5',
            method: 'post',
            data:params,
        });
    }
    // Export(params?: any){
    //     return request({
    //         url: this.baseurl + 'Export',
    //         method: 'get',
    //         params,
    //         responseType: 'blob'
    //     });
    // }

    PermanentlyDelete(id : any){
        return request({
            url: this.baseurl + 'PermanentlyDelete/' + id,
            method: 'post',
            params: {},
        });
    }

    ReserveDelete(id : any){
        return request({
            url: this.baseurl + 'Reserve/' + id,
            method: 'post',
            params: {},
        });
    }

    GetStaffMemberList(params?: any) {
        return request({
            url: this.baseurl + 'GetStaffMemberList',
            method: 'get',
            params,
        });
    }

    UpdateMasterUserType(data : any) {
        return request({
            url: this.baseurl + 'MasterUserType',
            method: 'post',
            data,
        });
    }
    UnLock(id : any) {
        return request({
            url: this.baseurl + 'UserUnLock/'+ id,
            method: 'post',
            params: {},
        });
    }

    SaveProfileChanges(data: any) {
        return request({
            url: this.baseurl + 'SaveProfileChanges',
            method: 'post',
            data,
        });
    }
    ChangePassword(data: any) {
        return request({
            url: this.baseurl + 'ChangePassword',
            method: 'post',
            data,
        });
    }
}

export default new userInfoApi('/api/user/', 'id');






