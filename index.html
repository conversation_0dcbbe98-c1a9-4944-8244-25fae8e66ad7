<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="keywords" content="" />
	<meta name="description" content="CaseX Management" />

	<!-- <meta http-equiv="pragram" content="no-cache" />
		<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="expires" content="0" /> -->

	<link rel="icon" href="/favicon.ico?v=1.0.1" />
	<title>CaseX Management</title>
	<script type="text/javascript">
		document.write("<script src='config.js?" + new Date().getTime() + "'><\/script>");
	</script>
</head>

<body>
	<div id="app"></div>
	<script type="module" src="/src/main.ts"></script>

	<!-- 监控页面标题，检测异常状态并自动刷新 -->
	<script>
		(function() {
			// 监控标题变化的函数
			function monitorTitle() {
				const title = document.title;

				// 检查是否出现异常标题
				if (title === '/ - CaseX Management') {
					window.location.reload();
					return;
				}
			}

			// 使用MutationObserver监控title变化
			const observer = new MutationObserver(function(mutations) {
				mutations.forEach(function(mutation) {
					if (mutation.type === 'childList' && mutation.target.nodeName === 'TITLE') {
						monitorTitle();
					}
				});
			});

			// 监控document.title的变化
			const titleElement = document.querySelector('title');
			if (titleElement) {
				observer.observe(titleElement, {
					childList: true,
					subtree: true
				});
			}

			// 定期检查标题（作为备用方案）
			// setInterval(monitorTitle, 200);

			// 页面加载完成后立即检查一次
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', monitorTitle);
			} else {
				monitorTitle();
			}
		})();
	</script>

</body>

</html>