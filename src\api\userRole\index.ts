﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class userRoleApi extends BaseApi {

    GetRoleUsers(params: any) {
        return request({
            url: this.baseurl + 'GetRoleUsers',
            method: 'get',
            params,
        });
    }

    RemoveRoleUser(data: any) {
        return request({
            url: this.baseurl + 'RemoveRoleUser',
            method: 'post',
            data,
        });
    }

}

export default new userRoleApi('/api/userRole/', 'id');






