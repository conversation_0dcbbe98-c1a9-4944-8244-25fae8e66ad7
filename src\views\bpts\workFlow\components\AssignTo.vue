<template>
	<div>
		<h4>审核结果</h4>
		<p></p>
		<p>执行人员：{{ item.operatorName }}</p>
		<p>执行动作： 转审</p>
		<el-divider />
		<p>转审人员：{{ item.operatorName }}</p>
		<p>转审意见：{{ item.operatorName }}</p>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from 'vue';
export default defineComponent({
	name: 'workflowOperationsApproved',
	props: {
		operationData: Object,
	},
	setup(props) {
		const state = reactive({
			item: props.operationData,
		});

		return {
			...toRefs(state),
		};
	},
});
</script>