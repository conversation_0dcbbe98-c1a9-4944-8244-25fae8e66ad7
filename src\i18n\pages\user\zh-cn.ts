﻿export default {
	userSearch: {
		//查询区域
		searchKeyPlaceholder: '请输入关键字',
	},
	userButtons: {
		//非按钮
		Modify:'立即修改',
		Save:'保存',
		Delegate:'代理',
		buttonNew:'新增',
		buttonAdd:'新增',
		actionHistory: '操作历史',
	},
	userFields: {
		//table 列名
		id: ' ',
		mobile: '手机号码',
		email: '电子邮箱',
		orgName: '所属公司',
		fullName: '全名',
		realName: '名字',
		lastName: '姓氏',
		switchLanguage: '语言切换',
		status: '用户状态',
		departmentId: ' ',
		remark: '备注',
		createTime: '创建时间',
		updateTime: ' ',
		lastErrorTime: ' ',
		errorCount: ' ',
		userName: '用户名',
		sex: '性别',
		age: ' ',
		birth: '生日',
		address: '联系地址',
		isDeleted: ' ',
		positionId: ' ',
		lastLoginTime: '登录时间',
		lastLoginIp: '登录IP',
		roleAssign: '角色分配',
		userType: '用户类型',
		sexSecret:'保密',
		sexMale:'男',
		sexWomen:'女',
		Active:'正常',
		Inactive:'禁用',
		customerAssign:'分配客户',
		SelectTheDataYouWantToExport : '请选择需要导出的数据',
		masterUser : '主用户',
		dlgMasterUser: '确定要操作这一条记录吗？',

		delegateTo : '代理人',
		delegateDate: '代理时间',
		to : '至',
		approverType : '审批类型',
		selectApprover : '审批人',
		startDate : '开始日期',
		endDate : '结束日期',
		canEmail : '能否发送邮件',
		modifiedTime : '修改时间',
		location:'地区',
		timeZone:'时区',
		jobTitle:'职位',
		escalation:'上级',
		projects: '项目',
		delegateBy: '委托人',
		approve:'代理开关',
		lockout:'用户锁定',
		privateTicket:'创建私有工单',
		role:'角色',
		groups:'组',
		creationDate:'创建日期',
		delegateSettings:'代理人设置',
		password:'密码更改',
		emailChange:'邮箱更改',
		profile:'简况',
		ticket:'工单',
	},
	PersonalTitle:{
		PersonalInfo:'Personal info',
		InfoSetting:'Info Setting',
		SecuritySettings:'Security Settings',
		PasswordUpdate:'password update',
	}
};
