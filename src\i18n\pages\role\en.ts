﻿export default {
	roleSearch: {
		//查询区域
		searchKeyPlaceholder: 'Please input',
	},
	roleButtons: {
		//非按钮
	},
	roleFields: {
		//table 列名
		id: 'Id',
		isDeleted: 'Is Deleted',
		name: 'Name',
		description: 'Description',
		orderSort: 'Sort',
		code: 'Code',
		authorityScope: 'Authority Scope',
		enabled: 'Enabled',
		createId: 'CreateId',
		createBy: 'Created By',
		createTime: 'Created At',
		modifyId: 'ModifyId',
		modifyBy: 'ModifyBy',
		permission: 'Permission',
		customRole: 'Custom Role',
		systemRole: 'System Role',
        allRole:'All Role',
		privilegeGroupMember:'Privilege Group Member',
		PrivilegeConfiguration:'Privilege Configuration',
		createRole:'Create Role',
		editRole:'Edit Role'
	},
	permissionTableFields: {
		name: 'Module Name',
		visit: 'Access Page',
		permissions: 'Permissions',
	},
};
