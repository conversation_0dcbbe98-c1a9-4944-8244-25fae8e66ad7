<template>
	<div class="list-page-layout">
		<el-container class="menu-index-container" style="height: 100%">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%" :body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form>
							<el-row :gutter="35">
								<el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4" class="mb6">
									<el-form-item>
										<el-input v-model="menuTable.param.searchKey" clearable> </el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4" class="mb6">
									<el-button size="small" type="primary" class="ml10" @click="onInit">
										<el-icon>
											<ele-Search />
										</el-icon>
										{{ $t('message.page.buttonSearch') }}
									</el-button>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>
			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'systemMenu.Create'" size="small" type="primary" class="ml10" @click="onOpenAddMenu">
						{{ $t('message.menuButtons.createMenu') }}
					</el-button>
					<el-button v-auth="'systemMenu.Create'" size="small" type="primary" class="ml10" @click="onOpenMenuButtons(null)">
						{{ $t('message.menuButtons.createButton') }}
					</el-button>
					<el-button v-if="false" size="small" type="success" class="ml10" @click="onSyn">
						{{ $t('message.page.buttonSyn') }}
					</el-button>
				</div>
			</el-header>
			<el-main style="flex: 1">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table
							:v-loading="menuTable.loading"
							:data="menuTable.data"
							style="width: 100%"
							row-key="id"
							:default-expand-all="false"
							:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
							:row-style="{ height: '40px' }"
							:cell-style="{ padding: '0px' }"
							stripe border
						>
							<el-table-column :label="$t('message.menuFields.name')" width="250" show-overflow-tooltip>
								<template #default="scope">
									<SvgIcon :name="scope.row.icon" />
									<span class="ml10">{{ $t(scope.row.title) }}</span>
								</template>
							</el-table-column>
							<el-table-column v-if="false" label="类型" show-overflow-tooltip width="80">
								<template #default="scope">
									<el-tag type="success" size="small">{{ scope.row.xx }}菜单</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="path" width="300" :label="$t('message.menuFields.path')" show-overflow-tooltip></el-table-column>

							<el-table-column :label="$t('message.menuFields.PrivilegeOperation')" show-overflow-tooltip>
								<template #default="scope">
									<el-button v-for="item in scope.row.permissions" :key="item.id" size="small" @click="onOpenMenuButtons(item)">
										{{ $t(item.title) }}</el-button
									>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.dictFields.sort')" show-overflow-tooltip width="80">
								<template #default="scope">
									{{ scope.row.sort }}
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.page.actions')" show-overflow-tooltip width="140">
								<template #default="scope">
									<el-button v-auth="'systemMenu.Edit'" size="mini" type="text" @click="onOpenEditMenu(scope.row)">
										{{ $t('message.page.actionsEdit') }}
									</el-button>
									<el-button
										v-auth="'systemMenu.Delete'"
										v-if="scope.row.id != 112 && scope.row.id != 957 && scope.row.id != 975"
										size="mini"
										type="text"
										style="color: #ff3a3a"
										@click="onTabelRowDel(scope.row)"
									>
										{{ $t('message.page.actionsDelete') }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</el-main>
		</el-container>
		<EditMenu ref="editMenuRef" @fetchData="onInit" />
		<MenuButtons ref="menuButtonsRef" @fetchData="onInit" />
	</div>
</template>

<script lang="ts">
import { ref, toRefs, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditMenu from '/@/views/bpts/menu/component/createOrEdit.vue';
import MenuButtons from '/@/views/bpts/menu/component/buttonCreateOrEdit.vue';
import { storeToRefs } from 'pinia';
import { useRoutesList } from '/@/stores/routesList';
import { useI18n } from 'vue-i18n';
import menuApi from '/@/api/menu/list';
import Auth from '/@/components/auth/auth.vue';

export default {
	name: 'systemMenu',
	components: { EditMenu, MenuButtons, Auth },
	setup() {
		const { t } = useI18n();
		const menuButtonsRef = ref();
		const editMenuRef = ref();
		const stores = useRoutesList();
		const { routesList } = storeToRefs(stores);
		const state = reactive({
			menuTable: {
				data: [],
				loading: false,
				param: {
					searchKey: '',
					menuType: 0, //只查询菜单 不查询 按钮
				},
			},
		});
		//初始化
		const onInit = () => {
			state.menuTable.loading = true;
			menuApi
				.Tree()
				.then((rs) => {
					state.menuTable.data = rs.data;
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
				})
				.finally(() => (state.menuTable.loading = false));
		};
		const onSyn = () => {
			var r = routesList.value;
			var items = [...r];
			//console.log('item', items);
			//return;
			items.forEach((item) => {
				item.component = '' + item.component;
			});

			menuApi.SynRoute(items).then((rs) => {
				ElMessage.success(rs.resultMsg);
			});
		};
		// 打开新增菜单弹窗
		const onOpenAddMenu = () => {
			editMenuRef.value.openDialog({ action: 'Create' });
		};
		// 打开编辑菜单弹窗
		const onOpenEditMenu = (row: object) => {
			editMenuRef.value.openDialog({ action: 'Edit', id: row.id });
		};
		// 打开编辑菜单弹窗
		const onOpenMenuButtons = (row: object) => {
			menuButtonsRef.value.openDialog(row);
		};
		// 删除当前行 1
		const onTabelRowDel = (row: object) => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal:false,
			})
				.then(() => {
					menuApi.DeleteByKey(row.id).then((rs) => {
						ElMessage.success(t('message.page.deleteSuccess'));
						onInit();
					});
				})
				.catch(() => {});
		};

		// 页面加载时
		onMounted(() => {
			onInit();
		});

		return {
			menuButtonsRef,
			editMenuRef,
			onSyn,
			onOpenAddMenu,
			onOpenEditMenu,
			onOpenMenuButtons,
			onTabelRowDel,
			onInit,
			...toRefs(state),
		};
	},
};
</script>

<style scoped>
.menu-index-container {
	padding: 5px;
}
</style>
