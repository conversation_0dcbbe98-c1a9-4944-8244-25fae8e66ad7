<template>
	<el-dialog v-model="showDialog" title="选择角色" custom-class="transfer-dialog" width="769px">
		<el-row :gutter="35">
			<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
				<div class="mb10">
					<el-input v-model="searchKey" placeholder="输入关键字进行搜索" class="input-with-select">
						<template #append>
							<el-button icon="ele-Search" />
						</template>
					</el-input>
				</div>
				<el-card class="box-card" shadow="never" style="height: 340px">
					<el-scrollbar height="290px">
						<el-tree
							ref="roleTreeRef"
							:data="roles"
							node-key="id"
							:props="{ value: 'id', label: 'name' }"
							:default-expand-all="true"
							@node-click="handleNodeClick"
							class="dep-tree"
						>
							<template #default="{ data }">
								<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 1"></SvgIcon>
								<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 2"></SvgIcon>
								<SvgIcon name="fa fa-sitemap ti" :size="12" v-if="data.itemType == 3"></SvgIcon>
								<span>{{ data.name }}</span>
							</template>
						</el-tree>
					</el-scrollbar>
				</el-card>
			</el-col>
			<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
				<el-card class="box-card" shadow="never" style="height: 390px">
					<template #header>
						<div class="card-header">
							<span style="float: left">Selected</span>
							<el-link type="danger">Clear</el-link>
						</div>
					</template>
					<div class="selected-item">
						<el-tree
							ref="selectedTreeRef"
							class="menu"
							node-key="id"
							:data="selectedData"
							:props="{ value: 'id', label: 'name' }"
							draggable
							highlight-current
							:expand-on-click-node="false"
							check-strictly
						>
							<template #default="{ data }">
								<span class="custom-tree-node el-tree-node__label">
									<span class="label">
										{{ data.name }}
									</span>
									<span class="do">
										<el-icon @click.stop="onDelete(data)"><ele-Delete /></el-icon>
									</span>
								</span>
							</template>
						</el-tree>
					</div>
				</el-card>
			</el-col>
		</el-row>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel" size="small">Cannel</el-button>
				<el-button type="primary" @click="onSave" size="small">Save</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, PropType, reactive, ref, toRefs } from 'vue';
import { ElDialog, ElMessage } from 'element-plus';
import roleApi from '/@/api/role';
interface DialogParams {
	multiple: boolean;
	initData: any;
}

interface RoleInfo {
	id: number; //组织架构id
	name: string;
}

export default defineComponent({
	name: 'RoleSelectDialog',
	components: { ElDialog },
	setup(props, context) {
		const roleTreeRef = ref();
		const selectedTreeRef = ref();
		const roles = ref<RoleInfo[]>([]);
		const state = reactive({
			showDialog: false,
			multiple: false,
			selectedData: [] as RoleInfo[],
			selectedIds: [] as number[],
		});
		const handleNodeClick = (node: RoleInfo) => {
			if (!state.multiple && state.selectedData.length >= 1) {
				ElMessage.info('最多只能选择一项.');
				return;
			}
			if (!state.selectedData.some((item) => item.id === node.id)) {
				state.selectedData.push(node);
			}
		};
		const onDelete = (row: RoleInfo) => {
			state.selectedData = state.selectedData.filter((obj) => obj !== row);
		};
		const onSave = () => {
			//只需要id ？
			state.selectedIds = state.selectedData.map((item) => {
				return item.id;
			});
			//返回完整的实体
			context.emit('fetchData', state.selectedData);
			onCancel();
		};
		const onCancel = () => {
			state.selectedData = [];
			state.showDialog = false;
		};
		const openDialog = (parmas: DialogParams) => {
			state.showDialog = true;
			state.multiple = parmas.multiple;
			state.selectedIds = parmas.initData;

			//获取部门列表
			roleApi.GetList().then(async (rs) => {
				roles.value = rs.data;
				//等待组件挂载完成后
				await nextTick();
				//初始化默认选择的项
				state.selectedData = [];
				const listId = props.initData ?? [];

				listId.forEach((id) => {
					const nodeData = roleTreeRef.value.getNode(id);
					if (nodeData) state.selectedData.push(nodeData.data);
				});
			});
		};
		onMounted(async () => {});

		return {
			roles,
			openDialog,
			handleNodeClick,
			onSave,
			onCancel,
			onDelete,
			selectedTreeRef,
			roleTreeRef,
			...toRefs(state),
		};
	},
});
</script>

<style scoped>
.transfer-filter {
	width: 200px;
	margin-bottom: 10px;
}
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 5px 0px;
}
.el-card__header {
	padding: 15px 20px;
}
.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 24px;
	height: 100%;
	width: 250px;
}
</style>
