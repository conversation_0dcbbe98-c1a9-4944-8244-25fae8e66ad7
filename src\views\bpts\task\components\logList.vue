﻿<template>
	<el-container>
		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table :data="tableData.data" v-loading="tableData.loading">
						<template #empty>
							<el-empty description="暂无数据" :image-size="100"></el-empty>
						</template>
						<el-table-column type="selection" />
						<el-table-column label="执行时间" width="200">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.LogTime) }}</span>
							</template>
						</el-table-column>

						<el-table-column label="执行结果" width="200">
							<template #default="{ row }">
								<el-tag v-if="row.Status == 'Succeeded'" type="success">{{ row.Status }}</el-tag>
								<el-tag v-else-if="row.Status == 'Failed'" type="danger">{{ row.Status }}</el-tag>
								<el-tag v-else type="info">{{ row.Status }}</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="耗时">
							<template #default="{ row }">
								<span>{{ row.TotalSeconds }} s</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination
						@size-change="onSizechange"
						@current-change="onCurrentChange"
						:pager-count="5"
						:page-sizes="[10, 20, 30]"
						v-model:current-page="tableData.param.pageIndex"
						background
						v-model:page-size="tableData.param.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total"
					>
					</el-pagination>
				</div>
			</div>
		</el-main>
	</el-container>
</template>

<script lang="ts">
import { toRefs, reactive, ref, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';
import print from '/@/utils/print.js';

import taskLogApi from '/@/api/logEvent/index';

export default defineComponent({
	name: 'logList',
	components: {},
	setup() {
		//const router = useRouter();
		const createOrEditRef = ref();
		const printMain = ref(null);
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					logType: 'Task',
					dataKey: 0,
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
				},
			},
			infoDrawer: false,
			detailObj: {},
		});

		const onShow = (taskId: number) => {
			state.tableData.param.dataKey = taskId;
			onInit();
		};

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			taskLogApi
				.Query(state.tableData.param)
				.then((rs) => {
					var arr: any[] = [];
					rs.data.map((item: { message: string }) => {
						arr.push(JSON.parse(item.message));
					});
					state.tableData.data = arr;
					state.tableData.total = rs.totalCount;
				})
				.catch(() => {})
				.finally(() => (state.tableData.loading = false));
		};
		//表格选择后回调事件
		const selectionChange = (selection: any) => {
			state.tableData.selection = selection;
		};
		//打印
		const onPrint = () => {
			print(printMain.value);
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		return {
			printMain,
			formatStrDate,
			onPrint,
			onShow,
			createOrEditRef,
			selectionChange,
			onInit,
			onSizechange,
			onCurrentChange,
			...toRefs(state),
		};
	},
});
</script>
