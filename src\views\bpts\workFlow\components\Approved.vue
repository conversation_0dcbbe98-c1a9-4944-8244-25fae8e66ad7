<template>
	<div>
		<h4>{{ nodeName }}</h4>
		<p></p>
		<p>Reviewer：{{ item.operatorName }}</p>
		<p>Review Result： <el-tag type="success">Approved</el-tag></p>
		<p>Review Date：{{ item.operationDate }}</p>
		<p v-if="item.transferMsg">Remark：</p>
		<span v-html="item.transferMsg"></span>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue';
export default defineComponent({
	name: 'workflowOperationsApproved',
	props: {
		nodeData: Object,
	},
	setup(props) {
		const state = reactive({
			nodeName: '',
			item: {},
		});

		onMounted(() => {
			const { nodeName, operationData } = props.nodeData;
			state.nodeName = nodeName;
			state.item = operationData[0];
		});

		return {
			...toRefs(state),
		};
	},
});
</script>
