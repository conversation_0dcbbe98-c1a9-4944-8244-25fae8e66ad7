.profile-container .el-form-item_btn .el-form-item__content {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;

	.el-button {
		min-width: 150px;
	}
}

.profile-container .form-control {
	background-color: #f0f4f7;
	border-radius: 0.5rem;
	transition:
		background-color 0.2s,
		border-color 0.4s;
	border-color: #e8eef3;
	color: #062a44;
	padding: 0.5rem 1.25rem;

	.el-form-item__label {
		margin-bottom: 0px !important;
	}

	.readonly {
		.el-input__wrapper {
			padding: 0px;
			box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
			background: none;
		}
	}

	.el-input__inner {
		font-size: 13px;
		font-weight: bold;
		color: #062a44;
	}
}

.profile-container .el-alert {
	border-width: 0px;
}

/* 消息列表样式 */
.message-list {
	list-style-type: none;
	/* 使用默认的项目符号 */
	margin-left: 20px;
	/* 列表的左边距 */
	padding-left: 0;
	/* 移除默认的内边距 */
}

.message-item {
	color: #000;
	font-size: 14px;
	height: 20px;
	/* 保持你设置的高度 */
	line-height: 20px;
	/* 设置行高与高度相同，以垂直居中文本 */
	padding: 0 5px;
	/* 如果需要，可以添加一些左右内边距 */
}

/* 错误类型的消息样式 */
.message-item.error {
	color: red;
	/* 错误消息的文字颜色设置为红色 */
}

/* 小点样式 */
.dot {
	margin-right: 2px;
	/* 小点与文本之间的间距 */
	margin-bottom: 3px;
	display: inline-block;
	width: 4px;
	/* 小点的宽度 */
	height: 4px;
	/* 小点的高度 */
	background-color: #000;
	/* 小点的颜色 */
	border-radius: 50%;
	/* 圆形小点 */
}

.custom-header {
	.el-checkbox {
		display: flex;
		height: unset;
	}
}

.role-tree-container {
	.el-sub-menu .el-menu {
		width: 100% !important;
	}
}