<template>
	<div class="list-page-layout">
		<el-container style="height: 100%;">
			<el-header style="height: auto">
				<el-card class="list-search-card" shadow="never" style="width: 100%;"
					:body-style="{ paddingTop: '10px' }">
					<div class="list-index-search">
						<el-form label-width="115px" @keyup.enter="onSearch">
							<el-row :gutter="1">
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.project_Name')">
										<el-input v-model="state.tableData.params.project_Name" clearable
											class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.project_Description')">
										<el-input v-model="state.tableData.params.project_Description" clearable
											class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.project_Status')">
										<el-select :placeholder="$t('message.page.selectKeyPlaceholder')"
											v-model="state.tableData.params.project_Status" clearable class="w-20">
											<el-option :label="$t('message.userFields.Active')" :value="0"></el-option>
											<el-option :label="$t('message.userFields.Inactive')"
												:value="1"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.project_Company')">
										<!-- <el-select
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											v-model="state.tableData.params.project_Company"
											clearable
											class="w-20"
										>
											<el-option v-for="item in state.customerOpts" :label="item['name']" :value="item['orgId']" :key="item['orgId']" />
										</el-select> -->

										<el-tree-select v-model="state.tableData.params.project_Company"
											:placeholder="$t('message.page.selectKeyPlaceholder')"
											:data="state.customerOpts" :default-expand-all="true" checkStrictly
											clearable class="w-20" :props="{ value: 'orgId', label: 'name' }"
											filterable>
											<template #default="{ data }">
												<SvgIcon name="fa fa-sitemap ti" :size="12"></SvgIcon>
												<span>{{ data.name }}</span>
											</template>
										</el-tree-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.Project_Id')">
										<el-input v-model="state.tableData.params.projectId" clearable
											class="w-20"></el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.cmProjectsFields.businessUnit')">
										<el-select v-model="state.tableData.params.businessUnit" filterable clearable
											class="w-20" :placeholder="$t('message.page.selectKeyPlaceholder')">
											<el-option v-for="item in state.businessUnitData"
												:label="item.description ? `${item.itemValue} - ${item.description}` : item.itemValue"
												:value="item.itemValue" :key="item.itemId"
												:description="item.description" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item :label="$t('message.companyFields.createAt')" class="date-form-item">
										<div class="date-container">
											<MyDate v-model:input="state.tableData.params.startTime"
												style="width: 130px !important;" />
											<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
											<MyDate v-model:input="state.tableData.params.endTime"
												style="width: 130px !important;" />
										</div>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="10" :lg="8" :xl="6" class="mb6">
									<el-form-item>
										<el-button size="small" type="primary" @click="onSearch"> {{
											$t('message.page.buttonSearch') }} </el-button>
										<el-button size="small" type="danger" @click="onSearchReSet"> {{
											$t('message.page.buttonReset') }} </el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</div>
				</el-card>
			</el-header>

			<el-header class="table_header mt5" style="display: flex">
				<div class="left-panel">
					<el-button v-auth="'ticketProjects.Create'" size="small" type="primary" class="ml10" @click="onAdd">
						{{ $t('message.cmProjectsButtons.createProject') }}
					</el-button>
				</div>
				<div class="right-panel">
					<el-dropdown>
						<el-button v-auth="'ticketProjects.Export'" type="primary" size="small" class="ml10">
							<el-icon>
								<ele-ArrowDownBold />
							</el-icon>
							{{ $t('message.page.buttonExport') }}
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="onExportAllRecord(0)">{{
									$t('message.page.buttonExportEntireList') }}</el-dropdown-item>
								<el-dropdown-item @click="onExportAllRecord(1)">{{
									$t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</el-header>

			<el-main style="flex: 1">
				<div class="scTable" style="height: 100%" ref="scTableMain">
					<div class="scTable-table">
						<el-table :data="state.tableData.data" v-loading="state.tableData.loading" height="calc(100%)"
							table-layout="fixed" row-key="id" lazy highlight-current-row scrollbar-always-on
							@selection-change="selectionChange" @sort-change="onSortChange"
							:row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
							:default-sort="{ prop: 'project_Name', order: 'ascending' }"  stripe border>
							<template #empty>
								<el-empty :description="$t('message.page.emptyDescription')"
									:image-size="100"></el-empty>
							</template>
							<el-table-column type="selection" />

							<el-table-column sortable :label="$t('message.cmProjectsFields.Project_Id')"
								prop="projectId" show-overflow-tooltip :min-width="160"
								:sort-orders="['ascending', 'descending']">
								<template #default="{ row }">
									{{ row.projectId }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.cmProjectsFields.project_Name')"
								prop="project_Name" show-overflow-tooltip :min-width="160"
								:sort-orders="['ascending', 'descending']">
								<template #default="{ row }">
									{{ row.project_Name }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.cmProjectsFields.project_Description')"
								prop="project_Description" show-overflow-tooltip :min-width="220"
								:sort-orders="['ascending', 'descending']">
								<template #default="{ row }">
									{{ row.project_Description }}
								</template>
							</el-table-column>

							<el-table-column sortable :label="$t('message.cmProjectsFields.project_Status')"
								prop="project_Status" show-overflow-tooltip :min-width="170"
								:sort-orders="['ascending', 'descending']">
								<template #default="{ row }">
									<el-tag type="danger" v-if="row.project_Status == '1'">{{
										$t('message.userFields.Inactive') }}</el-tag>
									<el-tag type="success" v-if="row.project_Status == '0'">{{
										$t('message.userFields.Active') }}</el-tag>
								</template>
							</el-table-column>

							<el-table-column :label="$t('message.cmProjectsFields.project_Company')" prop="companyName"
								show-overflow-tooltip :min-width="150">
								<template #default="{ row }">
									{{ row.companyName }}
								</template>
							</el-table-column>


							<el-table-column :label="$t('message.cmProjectsFields.businessUnit')" prop="businessUnit"
								show-overflow-tooltip :min-width="150">
								<template #default="{ row }">
									{{ row.businessUnit }}
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.organizationTable.createdAt')"  prop="createdAt"
							show-overflow-tooltip :min-width="150">
								<template #default="{ row }">
									<span>{{ row.createdAt }}</span>
								</template>
							</el-table-column>
							<el-table-column :label="$t('message.page.actions')" :min-width="120">
								<template #default="scope">
									<el-row>
										<el-button v-auth="'ticketProjects.Edit'" size="mini" type="text"
											@click="onOpenEditMenu(scope.row, 'Edit')">
											{{ $t('message.page.actionsEdit') }}
										</el-button>
										<el-button v-auth="'ticketProjects.Delete'" size="mini" type="text"
											style="color: #ff3a3a" @click="onTabelRowDel(scope.row)">
											{{ $t('message.page.actionsDelete') }}
										</el-button>
									</el-row>
								</template>
							</el-table-column>
						</el-table>

						<div class="scTable-page">
							<el-pagination @size-change="onSizechange" @current-change="onCurrentChange"
								:pager-count="5" :page-sizes="[15, 20, 30]"
								v-model:current-page="state.tableData.params.pageIndex" background
								v-model:page-size="state.tableData.params.pageSize"
								layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total" small>
							</el-pagination>
						</div>
					</div>
				</div>
			</el-main>

			<EditProject ref="editProjectRef" @fetchData="onInit" />
		</el-container>
	</div>
</template>

<script lang="ts" setup name="ticketProjects">
import { nextTick, ref, toRefs, reactive, onMounted, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';

import { useI18n } from 'vue-i18n';
import { formatDateTime } from '/@/utils/formatTime';
import cmProjectsApi from '/@/api/cmProjects/index';
import organizationApi from '/@/api/organization/index';
import dictItemApi from '/@/api/dictItem/index';
import MyDate from '/@/components/ticket/ticketDate.vue';
const EditProject = defineAsyncComponent(() => import('./component/createOrEdit.vue'));

const editProjectRef = ref();

const { t } = useI18n();

const state = reactive({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		selection: [],
		params: {
			pageIndex: 1,
			pageSize: 15,
			searchKey: '',
			order: 'CreatedAt',
			sort: 'desc',
			project_Name: '',
			project_Description: '',
			project_Status: '',
			project_Company: '',
			ids: [],
			ischeckType: 0,
			businessUnit: '',
			projectId: '',
			startTime: '',
			endTime: ''
		},
	},
	customerOpts: [],
	businessUnitData: [],
});

const onInit = () => {
	state.tableData.loading = true;
	var dictArr = ['BusinessUnit'];
	dictItemApi.Many(dictArr).then((rs: any) => {
		state.businessUnitData = rs.data?.find((a: any) => {
			return a.dictValue === 'BusinessUnit';
		})?.items;
	});

	cmProjectsApi
		.Query(state.tableData.params)
		.then((rs: any) => {
			state.tableData.data = rs.data;
			state.tableData.total = rs.totalCount;
		})
		.catch(() => { })
		.finally(() => {
			state.tableData.loading = false;

			organizationApi.Tree().then((rs) => {
				state.customerOpts = rs.data;
			});
		});

};

const initFormData = () => {
	state.tableData.params = {
		pageIndex: 1,
		pageSize: 15,
		searchKey: '',
		order: 'CreatedAt',
		sort: 'desc',
		project_Name: '',
		project_Description: '',
		project_Status: '',
		project_Company: '',
		businessUnit: '',
		projectId: '',
		startTime:'',
		endTime:'',
	} as any;
};

const onSearch = () => {
	onInit();
};

const onSearchReSet = () => {
	initFormData();
	onInit();
};

const onAdd = () => {
	editProjectRef.value.openDialog({ action: 'Create' });
};

const onOpenEditMenu = (row: any, actionType: any) => {
	editProjectRef.value.openDialog({
		action: actionType,
		id: row.id,
		companyId: row.project_Company,
	});
};

const onTabelRowDel = (row: any) => {
	ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
		confirmButtonText: t('message.page.confirm'),
		cancelButtonText: t('message.page.cannel'),
		type: 'warning',
		closeOnClickModal: false,
	})
		.then(() => {
			cmProjectsApi
				.DeleteByKey(row.id)
				.then((rs) => {
					ElMessage.success(t('message.page.deleteSuccess'));
					onInit();
				})
				.catch((rs) => {
					nextTick(() => {
						ElMessageBox.alert(t('message.page.existCanNotDelete'), t('message.page.dlgTip'), {
							type: 'warning',
						});
					});
				});
		})
		.catch(() => { });
};

const selectionChange = (selection: any) => {
	state.tableData.selection = selection;
};

const onSizechange = (val: number) => {
	state.tableData.params.pageSize = val;
	onInit();
};

const onCurrentChange = (val: number) => {
	state.tableData.params.pageIndex = val;
	onInit();
};

const onSortChange = (column: any) => {
	state.tableData.params.order = column.prop;
	state.tableData.params.sort = column.order;
	onInit();
};

const onExportAllRecord = (selectAll: number) => {
	let currPageSize = state.tableData.params.pageSize;
	state.tableData.params.pageSize = 10000;

	var checkSelection = state.tableData.selection;
	let ids_arr = [] as any;

	checkSelection.forEach(function (item, index) {
		let idjson = {} as any;
		idjson = item.id;
		ids_arr.push(idjson);
	});
	state.tableData.params.ischeckType = selectAll;
	if (selectAll == 1 && ids_arr.length == 0) {
		ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
		return;
	}
	if (selectAll == 0) {
		state.tableData.params.ids = [];
	} else {
		state.tableData.params.ids = ids_arr;
	}

	cmProjectsApi
		.Export(state.tableData.params)
		.then((rs) => {
			downloadCallback(rs);
		})
		.catch((rs) => { })
		.finally(() => (state.tableData.loading = false));

	state.tableData.params.pageSize = currPageSize;
};

const downloadCallback = (rs) => {
	let data = rs;
	var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
	var anchor = document.createElement('a');
	anchor.download = 'Projects_' + formatDateTime() + '.xlsx';
	anchor.href = window.URL.createObjectURL(newBlob);
	anchor.click();
};

onMounted(() => {
	onInit();
});
</script>
