<template>
	<div>
		<h4>{{ nodeName }}</h4>
		<p></p>
		<p>审核人员：{{ item.operatorName }}</p>
		<p>审核结果： <el-tag type="danger">已取消</el-tag></p>
	</div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted } from 'vue';
export default defineComponent({
	name: 'workflowOperationsApproved',
	props: {
		nodeData: Object,
	},
	setup(props) {
		const state = reactive({
			nodeName: '',
			item: {},
		});

		onMounted(() => {
			const { nodeName, operationData } = props.nodeData;
			state.nodeName = nodeName;
			state.item = operationData[0];
		});

		return {
			...toRefs(state),
		};
	},
});
</script>