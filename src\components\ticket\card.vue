<template>
	<div style="height: 100%; background-color: #ffffff">
		<el-row>
			<el-col align="middle" style="margin-top: 10px">
				<h3>Updated to your tickets</h3>
			</el-col>
		</el-row>
		<el-row style="margin-top: 5px; margin-bottom: 5px">
			<el-row type="flex" class="cardContainer" v-for="(item, index) in propData.data" :key="item.id" style="width: 100%">
				<el-col :span="4" align="middle">
					<el-avatar :icon="UserFilled" size="small" />
				</el-col>
				<el-col :span="1"></el-col>
				<el-col :span="19">
					<el-row>
						<el-col style="margin-top: 5px; margin-bottom: 5px">
							<router-link :to="'/dashboard/ticket/create?id=' + item.id"> {{ item.userName }} commented on "{{ item.ticketSubject }}" </router-link>
						</el-col>
						<el-col style="margin-top: 5px; margin-bottom: 5px; font-size: small">
							<div v-html="item.content"></div>
						</el-col>
						<el-col style="font-size: small; margin-top: 5px; margin-bottom: 5px">
							<div style="font-size: smaller; color: #d4cccf">{{ item.createdTime }}</div>
						</el-col>
					</el-row>
				</el-col>
				<el-col>
					<el-divider border-style="double" style="padding: 5px; margin: 0px" />
				</el-col>
			</el-row>
		</el-row>
	</div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { UserFilled } from '@element-plus/icons-vue';
import { useStore } from '/@/store/index';
export default defineComponent({
	props: ['propData'],
	setup(props, context) {
		const store = useStore();

		// 获取用户信息 vuex
		const currentUser = computed(() => {
			return store.state.userInfos.userInfos;
		});

		props['propData'].init(currentUser.value.userId);

		return {
			UserFilled,
		};
	},
});
</script>
<style scoped>
.cardContainer {
	margin-top: 5px;
	margin-bottom: 5px;
	align-items: center;
	justify-content: center;
}
</style>
