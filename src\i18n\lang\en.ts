// 定义内容
export default {
	router: {
		home: 'Home',
		system: 'System',
		systemMenu: 'systemMenu',
		systemRole: 'systemRole',
		systemUser: 'systemUser',
		systemDept: 'systemDept',
		systemDic: 'Dictionary',
		limits: 'limits',
		limitsFrontEnd: 'FrontEnd',
		limitsFrontEndPage: 'FrontEndPage',
		limitsFrontEndBtn: 'FrontEndBtn',
		limitsBackEnd: 'BackEnd',
		limitsBackEndEndPage: 'BackEndEndPage',
		menu: 'Menu',
		menu1: 'menu1',
		menu11: 'menu11',
		menu12: 'menu12',
		menu121: 'menu121',
		menu122: 'menu122',
		menu13: 'menu13',
		menu2: 'menu2',
		funIndex: 'function',
		funTagsView: 'funTagsView',
		funCountup: 'countup',
		funWangEditor: 'wangEditor',
		funCropper: 'cropper',
		funQrcode: 'qrcode',
		funEchartsMap: 'EchartsMap',
		funPrintJs: 'PrintJs',
		funClipboard: 'Copy cut',
		funGridLayout: 'Drag layout',
		funSplitpanes: 'Pane splitter',
		funDragVerify: 'Validator',
		pagesIndex: 'pages',
		pagesFiltering: 'Filtering',
		pagesFilteringDetails: 'FilteringDetails',
		pagesFilteringDetails1: 'FilteringDetails1',
		pagesIocnfont: 'iconfont icon',
		pagesElement: 'element icon',
		pagesAwesome: 'awesome icon',
		pagesFormAdapt: 'FormAdapt',
		pagesTableRules: 'pagesTableRules',
		pagesFormI18n: 'FormI18n',
		pagesFormRules: 'Multi form validation',
		pagesDynamicForm: 'Dynamic complex form',
		pagesWorkflow: 'Workflow',
		pagesListAdapt: 'ListAdapt',
		pagesWaterfall: 'Waterfall',
		pagesSteps: 'Steps',
		pagesPreview: 'Large preview',
		pagesWaves: 'Wave effect',
		pagesTree: 'tree alter table',
		pagesDrag: 'Drag command',
		pagesLazyImg: 'Image lazy loading',
		makeIndex: 'makeIndex',
		makeSelector: 'Icon selector',
		makeNoticeBar: 'notification bar',
		makeSvgDemo: 'Svgicon demo',
		makeTableDemo: 'table demo',
		paramsIndex: 'Routing parameters',
		paramsCommon: 'General routing',
		paramsDynamic: 'Dynamic routing',
		paramsCommonDetails: 'General routing details',
		paramsDynamicDetails: 'Dynamic routing details',
		chartIndex: 'chartIndex',
		visualizingIndex: 'visualizingIndex',
		visualizingLinkDemo1: 'visualizingLinkDemo1',
		visualizingLinkDemo2: 'visualizingLinkDemo2',
		tools: 'tools',
		layoutLinkView: 'LinkView',
		layoutIframeViewOne: 'IframeViewOne',
		layoutIframeViewTwo: 'IframeViewTwo',

		Login: 'Login',
		dashboard: 'Dashboard',
		invoice: 'Invoice',
		indexing: 'Indexing',
		invoiceList: 'Invoice List',
		permission: 'Permission',
		organization: 'Company',
		createCompany: 'Create Company',
		editCompany: 'Edit Company',
		position: 'Position',
		role: 'Role',
		user: 'User',
		systemConfiguration: 'Configuration',
		systemWebApi: 'Web API',
		systemIndustry: 'Industry',
		systemLog: 'Logs',
		systemTask: 'Schedule',
		personal: 'My Account',
		workflow: 'Workflow',
		workflowEngine: 'Workflow Design',
		workflowDefinitions: 'Workflow Definitions',
		workflowInstances: 'Workflow Instances',
		workflowRegistry: 'Workflow registry',
		dataManagement: 'Data Management',
		vendor: 'Vendor Management',
		createVendor: 'Create Vendor',
		emailManagement: 'Email',
		emailList: 'Email List',
		sendEmail: 'New Email',
		emailTemplate: 'Email Template',
		viewEmail: 'View Email',
		ticketManagement: 'Ticket',
		ticketList: 'Ticket List',
		scheduledList: 'Scheduled List',
		createTicket: 'Create Ticket',
		editTicket: 'Edit Ticket',
		categoryList: 'Category',
		CreateCategory: 'Create Category',
		groups: 'User Groups',
		groupsCreate: 'Create Group',
		deleteUserList: 'Deleted Users',
		projectsList: 'Projects',
		importList: 'Import',
		changepassword: 'Change Password',
		delegateList: 'Delegate',
		createScheduleTicket: 'Create Schedule Ticket',
		openItemList: 'Open Item List',
	},
	staticRoutes: {
		signIn: 'signIn',
		notFound: 'notFound',
		noPower: 'noPower',
	},
	user: {
		title0: 'Component size',
		title1: 'Language switching',
		title2: 'Menu search',
		title3: 'Layout configuration',
		title4: 'news',
		title5: 'Full screen on',
		title6: 'Full screen off',
		dropdownLarge: 'large',
		dropdownDefault: 'default',
		dropdownSmall: 'small',
		dropdown1: 'home page',
		dropdown2: 'My Settings',
		dropdown3: '404',
		dropdown4: '401',
		dropdown5: 'Log out',
		dropdown6: 'Code warehouse',
		searchPlaceholder: 'Menu search: support Chinese, routing path',
		newTitle: 'notice',
		newBtn: 'All read',
		newGo: 'Go to the notification center',
		newDesc: 'No Notification',
		logOutTitle: 'Tips',
		logOutMessage: 'This operation will log out. Do you want to continue?',
		logOutConfirm: 'determine',
		logOutCancel: 'cancel',
		logOutExit: 'Exiting',
		welcome: 'Welcome',
	},
	tagsView: {
		refresh: 'refresh',
		close: 'close',
		closeOther: 'closeOther',
		closeAll: 'closeAll',
		fullscreen: 'fullscreen',
		closeFullscreen: 'closeFullscreen',
	},
	notFound: {
		foundTitle: 'Wrong address input, please re-enter the address~',
		foundMsg: 'You can check the web address first, and then re-enter or give us feedback.',
		foundBtn: 'Back to home page',
	},
	noAccess: {
		accessTitle: 'You are not authorized to operate~',
		accessMsg: 'Contact information: add QQ group discussion *********',
		accessBtn: 'Reauthorization',
	},
	layout: {
		configTitle: 'Layout configuration',
		oneTitle: 'Global Themes',
		twoTopTitle: 'top bar set up',
		twoMenuTitle: 'Menu set up',
		twoColumnsTitle: 'Columns set up',
		twoTopBar: 'Top bar background',
		twoTopBarColor: 'Top bar default font color',
		twoIsTopBarColorGradual: 'Top bar gradient',
		twoMenuBar: 'Menu background',
		twoMenuBarColor: 'Menu default font color',
		twoMenuBarActiveColor: 'Menu Highlight Color',
		twoIsMenuBarColorGradual: 'Menu gradient',
		twoColumnsMenuBar: 'Column menu background',
		twoColumnsMenuBarColor: 'Default font color bar menu',
		twoIsColumnsMenuBarColorGradual: 'Column gradient',
		twoIsColumnsMenuHoverPreload: 'Column Menu Hover Preload',
		threeTitle: 'Interface settings',
		threeIsCollapse: 'Menu horizontal collapse',
		threeIsUniqueOpened: 'Menu accordion',
		threeIsFixedHeader: 'Fixed header',
		threeIsClassicSplitMenu: 'Classic layout split menu',
		threeIsLockScreen: 'Open the lock screen',
		threeLockScreenTime: 'screen locking(s/s)',
		fourTitle: 'Interface display',
		fourIsShowLogo: 'Sidebar logo',
		fourIsBreadcrumb: 'Open breadcrumb',
		fourIsBreadcrumbIcon: 'Open breadcrumb icon',
		fourIsTagsview: 'Open tagsview',
		fourIsTagsviewIcon: 'Open tagsview Icon',
		fourIsCacheTagsView: 'Enable tagsview cache',
		fourIsSortableTagsView: 'Enable tagsview drag',
		fourIsShareTagsView: 'Enable tagsview sharing',
		fourIsFooter: 'Open footer',
		fourIsGrayscale: 'Grey model',
		fourIsInvert: 'Color weak mode',
		fourIsDark: 'Dark Mode',
		fourIsWartermark: 'Turn on watermark',
		fourWartermarkText: 'Watermark copy',
		fiveTitle: 'Other settings',
		fiveTagsStyle: 'Tagsview style',
		fiveAnimation: 'page animation',
		fiveColumnsAsideStyle: 'Column style',
		fiveColumnsAsideLayout: 'Column layout',
		sixTitle: 'Layout switch',
		sixDefaults: 'One',
		sixClassic: 'Two',
		sixTransverse: 'Three',
		sixColumns: 'Four',
		tipText: 'Click the button below to copy the layout configuration to `/src/stores/themeConfig.ts` It has been modified in.',
		copyText: 'replication configuration',
		resetText: 'restore default',
		copyTextSuccess: 'Copy succeeded!',
		copyTextError: 'Copy failed!',
	},
	upgrade: {
		title: 'New version',
		msg: 'The new version is available, please update it now! Dont worry, the update is fast!',
		desc: 'Prompt: Update will restore the default configuration',
		btnOne: 'Cruel refusal',
		btnTwo: 'Update now',
		btnTwoLoading: 'Updating',
	},
	page: {
		buttonOk: 'Ok',
		buttonSearch: 'Search',
		buttonSave: 'Save',
		buttonSavePermissions: 'Save Permissions',
		buttonUpdate: 'Update',
		changePwd: 'Change Password',
		buttonCancel: 'Cancel',
		buttonClose: 'Close',
		buttonEdit: 'Edit',
		buttonReset: 'Clear',
		buttonDeleteBatch: 'Delete',
		searchKeyPlaceholder: 'Please input',
		selectKeyPlaceholder: 'Please select',
		buttonVisit: 'View',
		buttonCreate: 'Create',
		buttonAdd: 'Add',
		buttonDelete: 'Delete',
		buttonRefresh: 'Refresh',
		buttonPrint: 'Print',
		buttonExport: 'Export',
		buttonLog: 'Log',
		buttonExportEntireList: 'Entire List',
		buttonExportSelectedRecords: 'Selected Records',
		emptyDescription: 'no data',
		columnSelection: 'Selection',
		columnIndex: 'Index',
		columnExpand: 'Expand',
		actions: 'Actions',
		actionsView: 'view',
		actionsDetail: 'Detail',
		actionsEdit: 'Edit',
		actionsDelete: 'Delete',
		actionsSet: 'Set',
		actionsTest: 'Test',
		actionsCheckAll: 'Check',
		dlgTip: 'Tip',
		dlgDeleteText: 'Are you sure you want to delete this?',
		dlgDeleteFile: 'Are you sure you want to delete this file?',
		dlgReserveText: 'Are you sure you want to restore this record?',
		dlgDeleteSelectText1: 'Are you sure you want to delete the selected',
		dlgDeleteSelectText2: 'item?',
		dlgDeleteSelectText3: 'Are you sure you want to update the selected',
		dlgReSendText: 'Are you sure you want to send it again?',
		dlgExecuteText: 'Are you sure you want to do this immediately?',
		cannel: 'Cancel',
		confirm: 'Yes',
		cancel: 'No',
		reSend: 'Resend',
		buttonSend: 'Send',
		detailTitle: 'Detail',
		buttonPasswordReset: 'Password Reset',
		buttonSyn: 'Syn',
		createTime: 'Create Time',
		createBy: 'Create By',
		createAt: 'Create At',
		sort: 'Sort',
		modifiedAt: 'modified At',
		Action: 'Action',
		uploadFile: 'Upload File',
		executeSuccess: 'Success',
		saveSuccess: 'Save Successfully',
		deleteSuccess: 'Delete Succeed',
		yes: 'Yes',
		no: 'No',
		hidden: 'Hidden',
		nonhidden: 'Non-Hidden',
		cache: 'Cache',
		noCache: 'No Cache',
		affix: 'Fix',
		noAffix: 'Not Fix',
		iframe: 'Embed',
		noIframe: 'Not Embed',
		resetPwd: 'New Password',
		confirmPwd: 'Confirm Password',
		selectLeastAlert: 'Select at least one item',
		selectMoreAlert: 'Select at most one item',
		pwdMatch: 'Please re-enter passwords.	The "new password" and "confirm password" values do not match.',
		uploadLimit: 'Maximum File Size of 20 Mb is allowed',

		existCanNotDelete: "You're deleting a record that has been applied to a Ticket, please click 'Ok' to cancel this action",
		fullScreen: 'Full Screen',
		shrinkScreen: 'Shrink Screen',
		loading: 'loading',
	},
	limits: {
		Visit: 'Visit',
		Save: 'Save',
		Create: 'Create',
		Edit: 'Edit',
		Delete: 'Delete',
		BitchDelete: 'Batch Delete',
		Print: 'Print',
		Export: 'Export',
		AddCompany: 'Add Company',
		SetDelegate: 'Set Delegate',
		AddDelegate: 'Add Delegate',
		BatchDeleteDelegate: 'Batch Delete Delegate',
		DelegateActionHistory: 'Action History',
		Restore: 'Restore',
		OnOff: 'On/Off',
		UnLock: 'UnLock',
		AddItem: 'Add an item',
		EditOpenItemFile: 'Edit Open Item File',
		DeleteOpenItemFile: 'Delete Open Item File'
	},
	validates: {
		placeholder_input: 'Please input',
		required_input: 'Please input',
		email: 'Invalid email format, please input a correct email address.',
		current_password_required: 'Please enter your current password.',
		new_password_required: 'Please enter your new password.',
		password_complexity:
			'Password must contain at least one lowercase letter, one uppercase letter, and one special character, and be between 7 to 20 characters in length.',
		confirm_new_password_required: 'Please confirm your new password.',
		password_mismatch: 'The new password and confirmation password do not match.',
		required: "required"
	},
};
