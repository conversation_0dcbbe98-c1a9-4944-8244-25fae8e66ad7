﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class taskQueueApi extends BaseApi {

    OperationTask(taskId: any, type: any) {
        return request({
            url: this.baseurl + 'OperationTask',
            method: 'post',
            data: {
                id: taskId,
                type
            },
        });
    }

}

export default new taskQueueApi('/api/taskQueue/', 'id');






