import { nextTick } from 'vue';
import * as svg from '@element-plus/icons-vue';

// 获取阿里字体图标
const getAlicdnIconfont = () => {
	return new Promise((resolve, reject) => {
		nextTick(() => {
			const styles: any = document.styleSheets;
			let sheetsList = [];
			let sheetsIconList = [];
			for (let i = 0; i < styles.length; i++) {
				if (styles[i].href && isTrustedAliCdnUrl(styles[i].href)) {
					sheetsList.push(styles[i]);
				}
			}
			for (let i = 0; i < sheetsList.length; i++) {
				for (let j = 0; j < sheetsList[i].cssRules.length; j++) {
					if (sheetsList[i].cssRules[j].selectorText && sheetsList[i].cssRules[j].selectorText.indexOf('.icon-') > -1) {
						sheetsIconList.push(
							`${sheetsList[i].cssRules[j].selectorText.substring(1, sheetsList[i].cssRules[j].selectorText.length).replace(/\:\:before/gi, '')}`
						);
					}
				}
			}
			if (sheetsIconList.length > 0) resolve(sheetsIconList);
			else reject(new Error('未获取到值，请刷新重试'));
		});
	});
};

// 初始化获取 css 样式，获取 element plus 自带 svg 图标，增加了 ele- 前缀，使用时：ele-Aim
const getElementPlusIconfont = () => {
	return new Promise((resolve, reject) => {
		nextTick(() => {
			const icons = svg as any;
			const sheetsIconList = [];
			for (const i in icons) {
				sheetsIconList.push(`ele-${icons[i].name}`);
			}
			if (sheetsIconList.length > 0) resolve(sheetsIconList);
			else reject(new Error('未获取到值，请刷新重试'));
		});
	});
};

// 验证是否为可信的CDN域名
const isTrustedCdnUrl = (url: string): boolean => {
	try {
		const urlObj = new URL(url);
		// 精确匹配可信的CDN域名
		const trustedDomains = [
			'cdn.jsdelivr.net',
			'unpkg.com',
		];
		return trustedDomains.includes(urlObj.hostname) && urlObj.protocol === 'https:';
	} catch {
		// URL解析失败，视为不可信
		return false;
	}
};

// 验证是否为可信的阿里CDN域名
const isTrustedAliCdnUrl = (url: string): boolean => {
	try {
		const urlObj = new URL(url);
		// 精确匹配阿里CDN域名
		return urlObj.hostname === 'at.alicdn.com' && urlObj.protocol === 'https:';
	} catch {
		// URL解析失败，视为不可信
		return false;
	}
};

// 提取图标类名的辅助函数
const extractIconClassName = (selectorText: string): string => {
	return selectorText.substring(1, selectorText.length).replace(/::before/gi, '');
};

// 检查是否为有效的FontAwesome选择器
const isValidFaSelector = (selectorText: string | null): boolean => {
	return Boolean(selectorText) &&
		selectorText!.startsWith('.fa-') &&
		!selectorText!.includes(',') &&
		/::before/.test(selectorText!);
};

// 初始化获取 css 样式，这里使用 fontawesome 的图标
const getAwesomeIconfont = () => {
	return new Promise((resolve, reject) => {
		nextTick(() => {
			const styles: any = document.styleSheets;
			const sheetsList = [];
			const sheetsIconList = [];

			// 筛选可信的样式表
			for (const style of styles) {
				if (style.href && isTrustedCdnUrl(style.href)) {
					sheetsList.push(style);
				}
			}

			// 提取图标类名
			for (const sheet of sheetsList) {
				for (const rule of sheet.cssRules) {
					if (isValidFaSelector(rule.selectorText)) {
						sheetsIconList.push(extractIconClassName(rule.selectorText));
					}
				}
			}

			if (sheetsIconList.length > 0) {
				sheetsIconList.reverse();
				resolve(sheetsIconList);
			} else {
				reject(new Error('未获取到值，请刷新重试'));
			}
		});
	});
};

/**
 * 获取字体图标 `document.styleSheets`
 * @method ali 获取阿里字体图标 `<i class="iconfont 图标类名"></i>`
 * @method ele 获取 element plus 自带图标 `<i class="图标类名"></i>`
 * @method ali 获取 fontawesome 的图标 `<i class="fa 图标类名"></i>`
 */
const initIconfont = {
	// iconfont
	ali: () => {
		return getAlicdnIconfont();
	},
	// element plus
	ele: () => {
		return getElementPlusIconfont();
	},
	// fontawesome
	awe: () => {
		return getAwesomeIconfont();
	},
};

// 导出方法
export default initIconfont;
