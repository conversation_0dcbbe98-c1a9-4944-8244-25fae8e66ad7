<template>
	<div class="config-index-container scrolly layout-padding w100">
		
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules"  label-position="top" class="mt35 mb35">
			<el-row >
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
					<el-form-item>
						<el-button v-auth="'systemSetting.Edit'" type="primary" @click="onSave" size="small">{{
							$t('message.page.buttonSave') }}</el-button>
					</el-form-item>
				</el-col>
				<el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6" class="mb6" >
	
						<el-form-item label="Year" style="width: 200px;padding-left: 10px; margin: 0px;">
							<el-select v-model="ruleForm.holidaYear" filterable clearable @change="onChangeHoliday"
								:placeholder="$t('message.page.selectKeyPlaceholder')">
								<el-option v-for="item in holidayYearData"
									:label="item" :value="item"
									:key="item"  />
						</el-select>
					</el-form-item>
		
				</el-col>
			</el-row>
			<el-row :gutter="5">
				<el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="mb6" >
					<div >
						<el-table :data="ruleForm.tableHolidayData"   stripe border>
							<el-table-column prop="holidayDate" label="Holiday Date" class="w100"   >
								<template #default="scope">
							        <MyDate v-model:input="scope.row.holidayDate" :placeholder="'Pick a day'" class="w100" />
								</template>
							</el-table-column>

							<el-table-column prop="description" label="Holiday Description" class="w100">
								<template #default="scope">
									<el-input v-model="scope.row.description" clearable ></el-input>
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="Actions" min-width="120">
								<template #default="scope">
									<el-button link type="primary" size="small"
										@click.prevent="onAddHolidayItem(scope.$index)">
										Add Item
									</el-button>
									<el-button v-if="scope.$index > 0" link type="danger" size="small"
										@click.prevent="onDeleteHolidayRow(scope.$index)">
										Remove
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				
				</el-col>



			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch,ref } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';
import holidayApi from '/@/api/holiday/index';
import MyDate from '/@/components/ticket/ticketDate.vue';
export default defineComponent({
	name: 'tokenCom',
	components: {MyDate},
	props: {
		emailObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			holidayYearData:[] as any,
			ruleForm: {
				holidaYear:'',
				
				tableHolidayData: ref([]) as any,
			},
			rules: {
				tokenExp: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
                let obj=[]as any;
				state.ruleForm.tableHolidayData.some((item: any) => {
					if (item.holidayDate==''){
						ElMessage.error('Please enter holiday date.');
						return;
					}
					if (item.description=='' ){
						ElMessage.error('Please enter holiday description.');
						return;
					}
		
					obj.push(
						{
							Id:0,
							CreatedById:0,
							HolidayYear:item.holidayYear,
							HolidayDate:item.holidayDate,
							Description:item.description,
						}
					)
				});
				
				state.loading = true;
				holidayApi
					.SaveHoliday(obj)
					.then(() => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.emailObj,
			() => {
				state.ruleForm = props.emailObj || {};
			}
		);
		// 页面加载时
		onMounted(() => {
			// console.log(state.ruleForm)
			state.ruleForm.holidaYear= new Date().getFullYear().toString();
	        state.holidayYearData=getYearsAfter(20);


			holidayApi.GetList({ holidayYear:state.ruleForm.holidaYear}).then((rs) => {
				console.log("rs:",rs);
				state.ruleForm.tableHolidayData  = rs.data;
			});
			// state.ruleForm.tableHolidayData = [
			// 			{
			// 				holidayDate: '',
			// 				description: '',
			// 			},
			// 		];
		});
		const onChangeHoliday=(index:number)=>{
			holidayApi.GetList({ holidayYear:state.ruleForm.holidaYear}).then((rs) => {
				console.log("rs:",rs);
				state.ruleForm.tableHolidayData  = rs.data;
			});
		}
		const onAddHolidayItem=(index: number)=>{
			state.ruleForm.tableHolidayData.splice(index + 1, 0, {
				holidayYear:parseInt(state.ruleForm.holidaYear),
				holidayDate: '',
				description: '',
			})
		};
		const onDeleteHolidayRow=(index: number)=>{
			if (state.ruleForm.tableHolidayData.length > 1) {
				state.ruleForm.tableHolidayData.splice(index, 1)
			} else {
				ElMessage({
					message: 'At least one row must be present.',
					type: 'warning',
				})
			}
		};

        //获取N年后的所有年份。
		function getYearsAfter(yearsAfter: number): number[] {
			const currentYear = new Date().getFullYear();
			return Array.from({ length: yearsAfter }, (_, i) => currentYear + i);
		}
	



		return {
			onSave,
			onAddHolidayItem,
			onDeleteHolidayRow,
			onChangeHoliday,
			...toRefs(state),
		};
	},
});
</script>
<style scoped>
/* ::v-deep .el-switch__core {
	width: 200px;
	height: 24px;
	border-radius: 100px;
	border: none;
} */
</style>
