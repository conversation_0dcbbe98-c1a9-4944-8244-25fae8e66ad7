﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class tokenApi extends BaseApi {
	// 创建记录
	Login(params: any) {
		return request({
			url: this.baseurl + 'login/jwttoken3.0',
			method: 'get',
			params,
		});
	}

	getInfoByToken(params: any) {
		return request({
			url: this.baseurl + 'user/getInfoByToken',
			method: 'get',
			params,
		});
	}
}

export default new tokenApi('api/', 'Id');
