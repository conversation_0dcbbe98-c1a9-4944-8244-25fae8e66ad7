<template>
	<el-container class="system-tab-container" style="height: 100%">
		<el-header>
			<div class="left-panel">
				<el-radio-group v-model="dateType" style="margin-right: 15px" size="small" @change="onTimeChange">
					<el-radio-button label="0">today</el-radio-button>
					<el-radio-button label="1">yesterday</el-radio-button>
					<el-radio-button label="2">this week</el-radio-button>
					<el-radio-button label="3">last week</el-radio-button>
					<el-radio-button label="4">this month</el-radio-button>
				</el-radio-group>
				<!-- <el-date-picker
					v-model="logDate"
					type="datetimerange"
					range-separator="to"
					start-placeholder="start date"
					end-placeholder="end date"
					size="small"
				></el-date-picker> -->

				<MyDate size="small" v-model:input="tableData.param.startTime" style="width: 115px" />
				&nbsp;
				<el-text>{{ $t('message.ticketLabels.to') }}</el-text>
				&nbsp;
				<MyDate size="small" v-model:input="tableData.param.endTime" style="width: 115px" />
				&nbsp;
				<el-button size="small" type="primary" @click="onSearch"> {{ $t('message.page.buttonSearch') }}
				</el-button>
			</div>
		</el-header>

		<el-main class="nopadding" ref="printMain">
			<div class="scTable" style="height: 100%" ref="scTableMain">
				<div class="scTable-table">
					<el-table :data="tableData.data" v-loading="tableData.loading" height="calc(100%)"
						@row-click="onDetail"  stripe border>
						<template #empty>
							<el-empty description="No Data" :image-size="100"></el-empty>
						</template>
						<el-table-column type="selection" />
						<el-table-column label="Login Time" width="150">
							<template #default="{ row }">
								<span>{{ formatStrDate(row.LoginTime) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="Logout Time" width="150" >
							<template #default="{ row }">
								<span v-if="formatStrDate(row.LogoutTime) != ''">{{ formatStrDate(row.LogoutTime)
									}}</span>
							</template>
						</el-table-column>
						<el-table-column label="Username" width="150">
							<template #default="{ row }">
								<span>{{ row.UserName }}</span>
							</template>
						</el-table-column>
						<el-table-column label="Login Status" width="150" >
							<template #default="{ row }">
								<span>{{ row.LogStatus }}</span>
							</template>
						</el-table-column>
						<el-table-column label="Client IP" width="150">
							<template #default="{ row }">
								<span>{{ row.ClientIP.replace('::ffff:', '') }}</span>
							</template>
						</el-table-column>
						<el-table-column label="Device" show-overflow-tooltip>
							<template #default="{ row }">
								<span>{{ row.Device }}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="scTable-page">
					<el-pagination @size-change="onSizechange" @current-change="onCurrentChange" :pager-count="5"
						:page-sizes="[10, 20, 30]" v-model:current-page="tableData.param.pageIndex" background
						v-model:page-size="tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
						:total="tableData.total" small>
					</el-pagination>
				</div>
			</div>
			<el-drawer v-model="infoDrawer" title="Information details" :size="600" destroy-on-close>
				<Detail ref="detailRef"></Detail>
			</el-drawer>
		</el-main>
	</el-container>
</template>

<script lang="ts">
//import { useRouter } from 'vue-router';
import { toRefs, reactive, defineComponent, onMounted, ref } from 'vue';
import { formatStrDate, formatTypeTime } from '/@/utils/formatTime';
import Detail from './detail.vue';
import logEventApi from '/@/api/logEvent/index';
import MyDate from '/@/components/ticket/ticketDate.vue';

export default defineComponent({
	name: 'LoginTab',
	components: { Detail, MyDate },
	setup() {
		const detailRef = ref();
		const state = reactive({
			tableData: {
				data: [],
				total: 0,
				loading: false,
				selection: [],
				param: {
					logType: 'Login',
					pageIndex: 1,
					pageSize: 20,
					searchKey: '',
					order: 'id',
					sort: 'desc', // asc or desc
					startTime: '',
					endTime: '',
				},
			},
			infoDrawer: false,
			detailObj: {},
			dateType: '今天',
			logDate: [new Date(), new Date()], //Desc:更新时间
		});

		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			logEventApi
				.Query(state.tableData.param)
				.then((rs) => {
					state.tableData.total = rs.totalCount;

					var jd: any[] = [];

					rs.data.forEach((item: { message: string }) => {
						//	var d = JSON.parse(item.message);
						jd.push(JSON.parse(item.message));
					});

					state.tableData.data = jd;
				})
				.catch((rs) => { })
				.finally(() => (state.tableData.loading = false));
		};

		//搜索
		const onSearch = () => {
			onInit();
		};

		//详细信息
		const onDetail = (row: any) => {
			state.infoDrawer = true;
			setTimeout((a) => {
				detailRef.value.onShow(row, 'Login');
			}, 100);
		};

		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};

		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};

		const onTimeChange = (val: any) => {
			state.logDate = formatTypeTime(val);
			if (state.logDate) {
				state.tableData.param.startTime = state.logDate[0];
				state.tableData.param.endTime = state.logDate[1];
				onInit();
			}
		};

		// 页面加载时
		onMounted(() => {
			// setTimeout((a) => {
			onInit();
			// }, 2000);
		});

		return {
			onTimeChange,
			detailRef,
			formatStrDate,
			onInit,
			onDetail,
			onSizechange,
			onCurrentChange,
			onSearch,
			...toRefs(state),
		};
	},
});
</script>
