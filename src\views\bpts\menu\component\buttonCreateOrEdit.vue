﻿<template>
	<div class="system-menu-buttons-container">
		<el-dialog :title="title" v-model="isShowDialog" width="500px" :close-on-click-modal="false" draggable>
			<div class="modules-index-search mb15">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="60px" label-position="top">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.menuCommonFields.PrivilegeBelongsTo')" prop="name">
								<el-cascader :options="menuData"
									:props="{ checkStrictly: true, value: 'id', label: 'title' }"
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable class="w100"
									v-model="parentData" size="default">
									<template #default="{ node, data }">
										<span>{{ data.title }}</span>
										<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
									</template>
								</el-cascader>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.menuCommonFields.PermissionName')" prop="name">
								<el-input v-model="ruleForm.name" :placeholder="$t('message.page.selectKeyPlaceholder')"
									clearable size="default"></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.menuCommonFields.DisplayName')" prop="title">
								<el-input v-model="ruleForm.title"
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
									size="default"></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.identification')" prop="identification">
								<el-input v-model="ruleForm.identification"
									:placeholder="$t('message.page.selectKeyPlaceholder')" clearable
									size="default"></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
							<el-form-item :label="$t('message.menuFields.bindApi')">
								<el-select v-model="apiSelectData" multiple ref="apiSelectRef"
									:placeholder="$t('message.page.selectKeyPlaceholder')" style="width: 100%"
									@visible-change="onVisible">
									<el-option v-for="item in apiData" :key="item.apiId" :label="item.apiName"
										:value="item.apiId" />
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button v-auth="'systemMenu.Edit'" size="small" type="success" class="ml10" @click="onSubmit">
						{{ $t('message.page.buttonSave') }}
					</el-button>
					<Auth :value="ruleForm.id > 0 ? 'systemMenu.Delete' : ''">
						<el-button
							v-if="ruleForm.id > 0 && ruleForm.id != 1025 && ruleForm.id != 1036 && ruleForm.id != 1037"
							@click="onDelete" type="danger" size="small">
							{{ $t('message.page.buttonDelete') }}
						</el-button>
					</Auth>
					<el-button @click="onCancel" size="small">
						{{ $t('message.page.buttonCancel') }}
					</el-button>
				</span>
			</template>
		</el-dialog>
		<ApiTree ref="apiTreeRef" @fetchData="setSelectItem" />
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, getCurrentInstance, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { i18n } from '/@/i18n/index';
import ApiTree from '/@/views/bpts/api/components/tree.vue';
import { getCascaderMenuParent, getElcascaderSingle } from '/@/utils';

import menuApi from '/@/api/menu/list';
import menuApiApi from '/@/api/menuApi/index';
import { useI18n } from 'vue-i18n';
import Auth from '/@/components/auth/auth.vue';

export default {
	name: 'buttonCreateOrEdit',
	components: { ApiTree, Auth },
	setup(props, context) {
		const { t } = useI18n();
		const { proxy } = getCurrentInstance() as any;
		const apiTreeRef = ref();
		const apiSelectRef = ref();
		const state: any = reactive({
			isShowDialog: false,
			ruleForm: { name: '', code: '' },
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				title: [{ required: true, message: 'Please input', trigger: 'blur' }],
				identification: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
			title: '',
			menuData: [],
			parentData: [],
			apiData: [],
			apiSelectData: [],
		});

		const openDialog = (row: any) => {
			state.isShowDialog = true;
			state.ruleForm = Object.assign({}, row);

			state.title = t('message.menuCommonFields.createPermission');
			if (state.ruleForm.id) {
				state.title = t('message.menuCommonFields.editPermission');
			}

			//读取上级菜单
			menuApi.Tree().then((rs) => {
				state.menuData = getMenuData(rs.data);
				//解析上级id
				state.parentData = getCascaderMenuParent(state.menuData, state.ruleForm.parentId);
			});

			if (!state.ruleForm.id) {
				return;
			}

			//根据菜单id 获取对应的 Api
			menuApiApi.GetList({ menuId: state.ruleForm.id }).then((rs) => {
				state.apiData = rs.data;
				const arr: any[] = [];
				state.apiSelectData = rs.data.map((a: { apiId: any }) => {
					if (arr.indexOf(a.apiId) < 0) {
						arr.push(a.apiId);
					}
				});
				state.apiSelectData = arr;
			});
		};
		// 获取菜单
		const getMenuData = (routes: any) => {
			const arr: any = [];
			routes.map((val: any) => {
				val['title'] = i18n.global.t(val.title);
				arr.push({ ...val });
				if (val.children) getMenuData(val.children);
			});
			return arr;
		};

		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				obj.menuType = 3;
				obj.apis = state.apiSelectData;
				obj.parentId = getElcascaderSingle(state.parentData, 0);

				state.saveLoading = true;
				menuApi
					.Save(obj)
					.then(() => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						onInitForm();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		// 删除
		const onDelete = () => {
			ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				menuApi
					.DeleteByKey(state.ruleForm.id)
					.then((rs) => {
						ElMessage.success('Succeed');
						context.emit('fetchData');
						closeDialog();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};
		// 关闭弹窗
		const closeDialog = () => {
			onInitForm();
		};
		// 取消
		const onCancel = () => {
			closeDialog();
		};
		const onVisible = (v) => {
			if (v) {
				apiSelectRef.value.blur(); //使选择器的输入框失去焦点，并隐藏下拉框
				apiTreeRef.value.openDialog(state.apiSelectData, state.ruleForm.identification);
			}
		};
		const setSelectItem = (items: any) => {
			const newItem = [...items]; //复制数组
			state.apiData = newItem.map((a) => {
				return { apiId: a.id, apiName: a.name };
			});

			state.apiSelectData = newItem.map((a) => {
				return a.id;
			});
		};
		const onInitForm = () => {
			if (proxy.$refs.ruleFormRef) {
				proxy.$refs.ruleFormRef.resetFields();
			}
			state.isShowDialog = false;
			state.ruleForm = {};
			state.apiData = [];
			state.apiSelectData = [];
		};
		return {
			apiTreeRef,
			apiSelectRef,
			onVisible,
			setSelectItem,
			onSubmit,
			onCancel,
			onDelete,
			openDialog,
			...toRefs(state),
		};
	},
};
</script>

<style scoped></style>
