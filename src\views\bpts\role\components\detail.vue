﻿<template>
    <el-main style="padding:0 20px;">
        <el-descriptions :column="1" border size="small">
           
 <el-descriptions-item label="id">{{ info.id }}</el-descriptions-item>
 <el-descriptions-item label="isDeleted">{{ info.isDeleted }}</el-descriptions-item>
 <el-descriptions-item label="name">{{ info.name }}</el-descriptions-item>
 <el-descriptions-item label="description">{{ info.description }}</el-descriptions-item>
 <el-descriptions-item label="orderSort">{{ info.orderSort }}</el-descriptions-item>
 <el-descriptions-item label="dids">{{ info.dids }}</el-descriptions-item>
 <el-descriptions-item label="authorityScope">{{ info.authorityScope }}</el-descriptions-item>
 <el-descriptions-item label="enabled">{{ info.enabled }}</el-descriptions-item>
 <el-descriptions-item label="createId">{{ info.createId }}</el-descriptions-item>
 <el-descriptions-item label="createBy">{{ info.createBy }}</el-descriptions-item>
 <el-descriptions-item label="createTime">{{ info.createTime }}</el-descriptions-item>
 <el-descriptions-item label="modifyId">{{ info.modifyId }}</el-descriptions-item>
 <el-descriptions-item label="modifyBy">{{ info.modifyBy }}</el-descriptions-item>
 <el-descriptions-item label="modifyTime">{{ info.modifyTime }}</el-descriptions-item>
        </el-descriptions>
    </el-main>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, defineComponent } from 'vue';
import { formatStrDate } from '/@/utils/formatTime';

export default defineComponent({
    name: 'apiDetail',
    props: {
        info: Object
    },
    setup(props) {
        const state = reactive({
            isShowDialog: false,
            obj: {},
        });
        // 页面加载时
        onMounted(() => {
           // console.log("props", props.info)
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>


                        
        
        