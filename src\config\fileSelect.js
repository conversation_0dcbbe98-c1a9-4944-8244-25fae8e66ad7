import request from '/@/utils/request';

//文件选择器配置

export default {
	apiObj: {
		url: `${import.meta.env.VITE_API_URL}Api/Files/Upload`,
		name: '文件上传',
		post: async function (data, config = {}) {
			// return await http.post(this.url, data, config);
			return request({
				url: `${import.meta.env.VITE_API_URL}Api/Files/Upload`,
				method: 'post',
				data,
			});
		},
	}, // 上传请求API对象,
	menuApiObj: {
		get: async function (data, config = {}) {
			return request({
				url: `http://192.168.1.107:2800/api/file/menu?_=1650720133871`,
				method: 'get',
				data,
			});
		},
	}, //查看源文件
	listApiObj: {
		get: async function (data, config = {}) {
			return request({
				url: `http://192.168.1.107:2800/api/file/list?groupId=&page=1&pageSize=20&_=1650720133871`,
				method: 'get',
				data,
			});
		},
	},
	successCode: 200,
	maxSize: 30,
	max: 99,
	uploadParseData: function (res) {
        //console.log("re1s",res);
		return {
			id: res.data[0]?.fileId,
			fileName: res.data[0]?.name,
			url: res.data[0]?.fileUrl,
		};
	},
	listParseData: function (res) {
		return {
			rows: res.data.rows,
			total: res.data.total,
			msg: res.message,
			code: res.code,
		};
	},
	request: {
		page: 'page',
		pageSize: 'pageSize',
		keyword: 'keyword',
		menuKey: 'groupId',
	},
	menuProps: {
		key: 'id',
		label: 'label',
		children: 'children',
	},
	fileProps: {
		key: 'id',
		fileName: 'fileName',
		url: 'url',
	},
	files: {
		doc: {
			icon: 'sc-icon-file-word-2-fill',
			color: '#409eff',
		},
		docx: {
			icon: 'sc-icon-file-word-2-fill',
			color: '#409eff',
		},
		xls: {
			icon: 'sc-icon-file-excel-2-fill',
			color: '#67C23A',
		},
		xlsx: {
			icon: 'sc-icon-file-excel-2-fill',
			color: '#67C23A',
		},
		ppt: {
			icon: 'sc-icon-file-ppt-2-fill',
			color: '#F56C6C',
		},
		pptx: {
			icon: 'sc-icon-file-ppt-2-fill',
			color: '#F56C6C',
		},
	},
};
