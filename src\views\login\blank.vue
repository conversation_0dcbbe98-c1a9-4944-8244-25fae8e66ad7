<template>
	<el-dialog v-model="changePwdRef" :title="$t('message.page.changePwd')" :width="500" :close-on-press-escape="false"
		:close-on-click-modal="false" :show-close="false" @close="handleClose1(changePwdFormRef)"
		style="width: 500px !important">
		<el-form ref="changePwdFormRef" :rules="changeRules" :model="form" label-position="left" label-width="140px"
			style="height: 100px">
			<el-row>
				<el-col>
					<el-form-item :label="$t('message.page.resetPwd')" prop="pwd">
						<el-input v-model="form.pwd" :type="isShowPwd ? 'text' : 'password'"
							:placeholder="$t('message.page.searchKeyPlaceholder')" autocomplete="off"
							style="width: 300px" @keyup.enter="onSubmitChangePwd">
							<template #suffix>
								<el-icon @click="isShowPwd = !isShowPwd">
									<template v-if="isShowPwd">
										<img src="/img/show.svg" />
									</template>
									<template v-else>
										<img src="/img/hide.svg" />
									</template>
								</el-icon>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col style="margin-top: 20px">
					<el-form-item :label="$t('message.page.confirmPwd')" prop="confirmPwd">
						<el-input v-model="form.confirmPwd" :type="isShowConfirmPwd ? 'text' : 'password'"
							:placeholder="$t('message.page.searchKeyPlaceholder')" style="width: 300px"
							@keyup.enter="onSubmitChangePwd">
							<template #suffix>
								<el-icon @click="isShowConfirmPwd = !isShowConfirmPwd">
									<template v-if="isShowConfirmPwd">
										<img src="/img/show.svg" />
									</template>
									<template v-else>
										<img src="/img/hide.svg" />
									</template>
								</el-icon>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div style="color: red; size: 12; margin-top: 20px">
			<p>Must be a minimum of 8 characters in length and contain a combination of at least two of the following:
			</p>
			<p>1. A mix of uppercase and lowercase letters</p>
			<p>2. Special character (~ ! @ # $ % ^ & * ( ) _ +)</p>
			<p>3. Number (numeric value)</p>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button type="primary" @click="onSubmitChangePwd">
					{{ $t('message.page.changePwd') }}
				</el-button>
			</span>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { toRefs, reactive, onMounted, ref, defineComponent, computed, onBeforeMount, getCurrentInstance, onUnmounted, watch } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { Session, Local } from '/@/utils/storage';
import loginApi from '/@/api/login';
import userApi from '/@/api/user';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import mittBus from '/@/utils/mitt';
import { isNotEmptyOrNull } from '/@/utils';
import aTestApi from '/@/api/ATest';

const { proxy } = <any>getCurrentInstance();
const { t } = useI18n();
const changePwdRef = ref(true);
const changePwdFormRef = ref<FormInstance>();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const router = useRouter();

const route = useRoute();

const isShowPwd = ref(false);
const isShowConfirmPwd = ref(false);

onMounted(() => {
	loginApi.GetMyInfo().then((rs) => {
		if (rs.data) {
			Local.set('needToChangePwd', rs.data.needToChangePwd);
			if (!rs.data.needToChangePwd) {
				Session.remove('tagsViewList');
				router.push('/login');
			}
		}
	});
});

// 获取用户信息 vuex
const currentUser = computed(() => {
	return userInfos.value;
});

const changeRules = {
	pwd: [
		{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
		{
			pattern:
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
			message: 'Password is Invalid.',
			trigger: ['blur'],
		},
	],
	confirmPwd: [
		{ required: true, message: t('message.ticketValidates.required'), trigger: 'blur' },
		{
			pattern:
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*(\d|[~!@#$%^&*()_+,]))[A-Za-z\d~!@#$%^&*()_+,]{7,20}$|^(?=.*\d)(?=.*[~!@#$%^&*()_+,])[A-Za-z\d~!@#$%^&*()_+,]{7,20}$/,
			message: 'Password is Invalid.',
			trigger: ['blur'],
		},
	],
};

const form = reactive({
	userId: '',
	pwd: '',
	confirmPwd: '',
});

const handleClose1 = (formEl) => {
	formEl.resetFields();

	changePwdRef.value = false;
};

const onSubmitChangePwd = () => {
	if (form.pwd != form.confirmPwd) {
		ElMessage.error(t('message.page.pwdMatch'));
		return;
	}

	proxy.$refs.changePwdFormRef.validate((valid: any) => {
		if (!valid) {
			return;
		}

		var obj = { userId: currentUser.value.userId, pwd: form.confirmPwd };
		userApi
			.UserResetPassword(obj)
			.then((rs: any) => {
				if (!rs.data) {
					ElMessage.error(rs.resultMsg);
					return;
				}
				ElMessage.success('Succeed');
				changePwdRef.value = false;

				// mittBus.emit('onCurrentContextmenuClick', {
				// 	contextMenuClickId: 1,
				// 	path: '/change',
				// 	meta: {},
				// 	name: '',
				// });

				// mittBus.emit('onCurrentContextmenuClick', {
				// 	contextMenuClickId: 0,
				// 	path: '/dashboard',
				// 	meta: {},
				// 	name: '',
				// });

				mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));

				Local.set('needToChangePwd', false);
				const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;
				if (isNotEmptyOrNull(webSystemUrl)) {
					window.location.href = webSystemUrl;
				} else {
					aTestApi.VueLog({ message: '12' });
					window.location.href = '/';
				}
			})
			.catch((rs) => {
				ElMessage.error(rs.resultMsg || rs.toString());
			})
			.finally(() => { });
	});
};
</script>

<style scoped lang="scss">
.el-form-item ::v-deep(.el-form-item__error) {
	display: block;
}

.el-icon img {
	height: 1em;
	width: 1em;
	cursor: pointer;
}
</style>
