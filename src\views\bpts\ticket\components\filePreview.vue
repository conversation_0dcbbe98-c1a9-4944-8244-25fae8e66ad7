<template>
    <el-dialog v-model="isShowDialog" width="60vw" :close-on-click-modal="false" draggable :destroy-on-close="true" @click="handleClose">
        <vue-office-docx
            v-if="['doc', 'docx'].includes(fileType)"
            :src="fileUrl"
            style="height: 85vh;"
            @rendered="renderedHandler('doc')"
            @error="errorHandler('doc')"
        />
        <vue-office-excel v-else-if="['xls', 'xlsx'].includes(fileType)"
            :src="fileUrl"
            style="height: 85vh;"
            @rendered="renderedHandler('xls')"
            @error="errorHandler('xls')"
        />
        <vue-office-pptx v-else-if="['ppt', 'pptx'].includes(fileType)"
            :src="fileUrl"
            style="height: 85vh;"
            @rendered="renderedHandler('ppt')"
            @error="errorHandler('ppt')"
        />
        <object
            v-else-if="['pdf', 'txt','mp3','mp4'].includes(fileType)"
            type="application/pdf"
            :data="fileUrl"
            style="height: 85vh;width: 100%;"
            ></object>        
        <div slot="footer" class="footer-container">
            <el-button type="primary" round @click="handleClose">{{ $t('message.page.buttonClose') }}</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { toRefs, inject , reactive, ref, computed, getCurrentInstance } from 'vue';
//引入VueOfficeDocx组件
import VueOfficeDocx from "@vue-office/docx";
//引入相关样式
import "@vue-office/docx/lib/index.css";
//引入VueOfficeExcel组件;
import VueOfficeExcel from "@vue-office/excel";
//引入相关样式
import "@vue-office/excel/lib/index.css";
//引入VueOfficePptx 组件
import VueOfficePptx from '@vue-office/pptx'
//引入VueOfficePdf组件
// import VueOfficePdf from "@vue-office/pdf";

export default {
    components: {
        VueOfficeDocx,
        VueOfficeExcel,
        VueOfficePptx,
        // VueOfficePdf,
    },
	setup() {
		const { proxy } = getCurrentInstance() as any;

        const BASE_URL = computed(() => {
            const appSettings = proxy?.$appSettings;
            return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
        });
        const state = reactive({
			isShowDialog: false,
            fileUrl:'',
            fileType:'',
            item:{},
		});
        const openDialog = (row:any) => {
            console.log(row);
			state.isShowDialog = true;
            state.fileUrl=`${BASE_URL.value}`+row.attachment_File_Name;
            state.fileType=row.attachment_File_Ext;
            // item.popoverShow=false;
            // state.item=item;
		};

        const renderedHandler = (type:string) =>  {
            console.log("渲染完成")
        };
        const errorHandler = (type:string) =>  {
            console.log("渲染失败")
        }
        // const UpdateVisible = inject('UpdateVisible');
        const handleClose= () =>   {
            state.fileUrl = "";
            // UpdateVisible(state.item,true);
			state.isShowDialog = false;
            // state.fileType = "";
        }

        return {
            openDialog,
            renderedHandler,
            errorHandler,
			...toRefs(state),
            handleClose,
        };
    },
}
</script>

<style >
.pptx-preview-wrapper{
    width: 100% !important;
    height: 100% !important;
}
.footer-container {
        margin-top: 10px; /* 添加上边距为 20px，您可以根据需要调整 */
        display: flex;
        justify-content: flex-end;
    }
</style>