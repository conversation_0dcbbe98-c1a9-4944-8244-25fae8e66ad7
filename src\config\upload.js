import request from '/@/utils/request';
import appSettings from '/@/config/index.js';
// 上传配置

export default {
	apiObj: {
		url: `${appSettings.BASE_URL}Api/Files/Upload`,
		name: '文件上传',
		post: async function (data, config = {}) {
			return request({
				url: `${appSettings.BASE_URL}Api/Files/Upload`,
				method: 'post',
				headers: { 'Content-Type': 'text/plain' },
				data,
			});
		},
	}, // 上传请求API对象
	successCode: 200, // 请求完成代码
	maxSize: 20, // 最大文件大小 默认10MB
	parseData: function (res) {
		let fileUrl = res.data[0].fileUrl;
		if (!fileUrl.startsWith('http') && !fileUrl.startsWith('https')) {
			if (fileUrl.startsWith('/')) {
				fileUrl = fileUrl.substring(1);
			}
			//fileUrl = `${import.meta.env.VITE_FILE_URL}${fileUrl}`;
			fileUrl = `${appSettings.BASE_URL}${fileUrl}`;
		}
		return {
			code: res.resultCode, // 分析状态字段结构
			src: fileUrl, // 分析图片远程地址结构
			fileId: res.data[0]?.fileId,
			msg: res.resultMsg, // 分析描述字段结构
		};
	},
	maxSizeFile: 20, //最大文件大小 默认10MB
	apiObjFile: {
		url: `${appSettings.BASE_URL}Api/Files/UpLoadFrom`,
		name: '文件上传',
		post: async function (data, config = {}) {
			return request({
				url: this.url,
				method: 'post',
				headers: { 'Content-Type': 'text/plain' },
				data,
			});
		},
	}, // 上传请求API对象
	apiOpenItemDetailAttObj: {
		url: `${appSettings.BASE_URL}Api/CmOpenItemDetailAttachment/UploadV2`,
		name: '文件上传',
		post: async function (data, config = {}) {
			return request({
				url: `${appSettings.BASE_URL}Api/CmOpenItemDetailAttachment/UploadV2`,
				method: 'post',
				headers: { 'Content-Type': 'text/plain' },
				data,
			});
		},
	}, // 上传请求API对象
};
