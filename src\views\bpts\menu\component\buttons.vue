﻿<template>
	<div class="system-menu-buttons-container">
		<el-dialog title="权限配置" v-model="isShowDialog" width="769px" :close-on-click-modal="false" draggable>
			<div class="modules-index-search mb15">
				<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" size="small" label-width="60px">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8" class="mb20">
							<el-form-item label="名称" prop="name">
								<el-input v-model="ruleForm.name" placeholder="请输入名称" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8" class="mb20">
							<el-form-item label="标识" prop="identification">
								<el-input v-model="ruleForm.identification" placeholder="请输入标识" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="8" class="mb20">
							<el-button size="small" type="success" class="ml10" @click="onSubmit">
								<el-icon>
									<ele-FolderAdd />
								</el-icon>
								新增
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<el-table :data="tableData.data" v-loading="tableData.loading" style="width: 100%">
				<el-table-column type="selection" />

				<el-table-column label="name" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.name }}</span>
					</template>
				</el-table-column>

				<el-table-column label="identification" show-overflow-tooltip>
					<template #default="{ row }">
						<span>{{ row.identification }}</span>
					</template>
				</el-table-column>

				<el-table-column align="center" label="操作" width="100">
					<template #default="{ row }">
						<el-button size="mini" type="text" @click="onDelete(row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="small">Cannel</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';

import menuApi from '/@/api/menu/list';

export default {
	name: 'menuButtons',
	components: {},
	setup() {
		const { proxy } = getCurrentInstance() as any;
		const state: any = reactive({
			isShowDialog: false,
			ruleForm: { name: '', code: '' },
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				identification: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
			tableData: {
				data: [],
				total: 0,
				loading: false,
				param: {
					parentId: 0,
					menuType: 3,
				},
			},
		});

		const openDialog = (parmas: any) => {
			state.isShowDialog = true;
			state.tableData.param.parentId = parmas.parentId;
			onInit();
		};
		//初始化
		const onInit = () => {
			state.tableData.loading = true;
			menuApi
				.GetList(state.tableData.param)
				.then((rs) => {
					state.tableData.data = rs.data;
				})
				.catch((rs) => {
					ElMessage.error(rs.resultMsg || rs.toString());
					state.tableData.loading = false;
				})
				.finally(() => (state.tableData.loading = false));
		};
		//搜索
		const onSearch = () => {
			onInit();
		};

		// 保存
		const onSubmit = () => {
			proxy.$refs.ruleFormRef.validate((valid: any) => {
				if (!valid) {
					return;
				}
				var obj = state.ruleForm;
				obj.title = obj.name;
				obj.parentId = state.tableData.param.parentId;
				obj.menuType = 3;

				state.saveLoading = true;
				menuApi
					.Save(obj)
					.then(() => {
						onInit();
					})
					.catch((rs) => {
						ElMessage.error(rs.resultMsg || rs.toString());
					})
					.finally(() => {
						state.saveLoading = false;
					});
			});
		};
		// 删除
		const onDelete = (row: Object) => {
			ElMessageBox.confirm(`此操作将永久当前纪录，是否继续?`, '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
				closeOnClickModal:false,
			}).then(() => {
				menuApi
					.DeleteByKey(row.id)
					.then((rs) => {
						onInit();
					})
					.catch((rs) => {
						ElMessage.error(rs.ResultMsg || rs.toString());
					});
			});
		};
		// 取消
		const onCancel = () => {
			state.isShowDialog = false;
		};
		// pageSize 改变时触发
		const onSizechange = (val: number) => {
			state.tableData.param.pageSize = val;
			onInit();
		};
		// current-change 改变时触发
		const onCurrentChange = (val: number) => {
			state.tableData.param.pageIndex = val;
			onInit();
		};
		// 页面加载时
		onMounted(() => {
			onInit();
		});
		return {
			onInit,
			onSubmit,
			onCancel,
			onDelete,
			onSizechange,
			onCurrentChange,
			onSearch,
			openDialog,
			...toRefs(state),
		};
	},
};
</script>
