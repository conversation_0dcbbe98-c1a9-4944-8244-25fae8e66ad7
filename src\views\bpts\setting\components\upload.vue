<template>
	<div class="config-index-container">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px" class="mt35 mb35 uploadForm">
			<el-row :gutter="35">
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Host">
						<el-input v-model="ruleForm.imgServerHost" placeholder="like img.bpst.com" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Upload directory" prop="filePath">
						<el-input v-model="ruleForm.filePath" placeholder="Please input upload directory" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Save mode" prop="fileSave">
						<el-select v-model="ruleForm.fileSave" placeholder="Please select" clearable class="w100">
							<el-option label="按年月日每天一个目录" :value="1"></el-option>
							<el-option label="按年月/日/存入不同目录" :value="2"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="File extension" prop="fileExtension">
						<el-input v-model="ruleForm.fileExtension" placeholder="Please input" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Video extension">
						<el-input v-model="ruleForm.videoExtension" placeholder="Please input" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Attachment max size">
						<el-input v-model="ruleForm.attachSize" placeholder="Please input" clearable>
							<template #append>KB</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Video max size">
						<el-input v-model="ruleForm.videoSize" placeholder="Please input" clearable>
							<template #append>KB</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Image max size">
						<el-input v-model="ruleForm.imgSize" placeholder="Please input" clearable>
							<template #append>KB</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Thumbnail generation">
						<el-radio-group v-model="ruleForm.thumbnailMode" size="large">
							<el-radio-button label="Cut">中心裁剪</el-radio-button>
							<el-radio-button label="HW">两边补白</el-radio-button>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Image watermark">
						<el-radio-group v-model="ruleForm.watermarkType" size="large">
							<el-radio-button label="1">不加水印</el-radio-button>
							<el-radio-button label="2">文字水印</el-radio-button>
							<el-radio-button label="3">图片水印</el-radio-button>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Watermark image">
						<el-input v-model="ruleForm.watermarkPic" placeholder="Please input" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item label="Watermark text">
						<el-input v-model="ruleForm.watermarkText" placeholder="Please input" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
					<el-form-item>
						<el-button type="primary" @click="onSave" size="small">Save</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, defineComponent, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import configApi from '/@/api/config/index';

export default defineComponent({
	name: 'configIndex',
	components: {},
	props: {
		uploadObj: {
			type: Object,
			default: {},
		},
	},
	setup(props: any) {
		const { proxy } = getCurrentInstance() as any;
		const state = reactive({
			loading: false,
			ruleForm: {},
			rules: {
				name: [{ required: true, message: 'Please input', trigger: 'blur' }],
				version: [{ required: true, message: 'Please input', trigger: 'blur' }],
				company: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
		});

		const onSave = () => {
			proxy.$refs.ruleFormRef.validate((valid: boolean) => {
				if (!valid) {
					return;
				}
				var obj = { Upload: state.ruleForm };
				state.loading = true;
				configApi
					.SaveConfig(obj)
					.then((rs) => {
						ElMessage.success('Saved Successfully');
					})
					.finally(() => {
						state.loading = false;
					});
			});
		};
		watch(
			() => props.uploadObj,
			() => {
				state.ruleForm = props.uploadObj || {};
			}
		);
		// 页面加载时
		onMounted(() => {});
		return {
			onSave,
			...toRefs(state),
		};
	},
});
</script>
