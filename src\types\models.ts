export interface PositionInfo {
	id: number;
	name: string;
	code: string;
	sort: number;
	enable: boolean;
	description: string;
	isDeleted: boolean;
	createAt: string;
}
// 定义提示信息接口
export interface IApiResultMessage {
    msg: string; // 提示信息内容
    type: 'success' | 'error' | 'info'; // 提示信息类型，例如：成功、错误、信息
}

export interface WorkflowExecutionDto {
	/// <summary>
	/// ִ�е����͡��� Consts.WorkflowAction_Started
	/// </summary>
	executionType: string;
	/// <summary>
	/// ������
	/// </summary>
	trigger: string;
	/// <summary>
	/// ��������
	/// </summary>
	workflowType: string;
	/// <summary>
	/// ���̵�crmId
	/// </summary>
	crmWorkflowId: number;
	/// <summary>
	/// ���̵�ʵ��id
	/// </summary>
	elsaInstancesId: string;
	/// <summary>
	/// ִ�б�ע
	/// </summary>
	msg: string;
	/// <summary>
	/// ִ����
	/// </summary>
	userId: number;
	/// <summary>
	///
	/// </summary>
	nextApprovalType: number;
	/// <summary>
	/// ת��������
	/// </summary>
	nextApprovalValue: any[];
	/// <summary>
	/// ��ǰ���̵Ļid
	/// </summary>
	activityId: string;

	/// <summary>
	/// ��ǰ������id
	/// </summary>
	taskId: string;

	/// <summary>
	/// �˻ء����ؽڵ�id
	/// </summary>
	revokeOrReturnActivityId: string;
	/// <summary>
	/// ������Ϣ
	/// </summary>
	Data: any;

	newTransferId: string;

	/// <summary>
	/// ѡ�������֧
	/// </summary>
	action: string;
	/// <summary>
	/// ������
	/// </summary>
	approvers: any[];

	//��֧
	branch: string;
}

export interface DesignNodeProperty {
	/// <summary>
	/// �ڵ�Id
	/// </summary>
	id: string;
	/// <summary>
	/// �ڵ�����
	/// </summary>
	type: string;
	/// <summary>
	/// �ڵ������
	/// </summary>
	properties: string;
	/// <summary>
	/// �ڵ�����
	/// </summary>
	name: string;
	/// <summary>
	/// ��ʾ����
	/// </summary>
	displayName: string;
	/// <summary>
	/// Elsa�Ļ����
	/// </summary>
	activityType: string;

	/// <summary>
	/// ����������
	/// 1��ָ����Ա
	/// 2�������˱���
	/// 3����������
	/// 4����������
	/// </summary>
	approvalType: number;
	/// <summary>
	/// ��approvalType=1ʱ
	/// </summary>
	approvalData: NotificationSettings;

	/// <summary>
	/// ��approvalType=4ʱ,��ʾѡ��ı����ֶ�
	/// </summary>
	approvalDataForm: string;

	/// <summary>
	/// ��approvalType=5ʱ
	/// </summary>
	approvalUrlForm: any[];
	/// <summary>
	/// ��approvalType=5ʱ
	/// </summary>
	approvalUrl: string;

	/// <summary>
	/// WaitAll:��ǩ
	/// WaitAny����ǩ
	/// </summary>
	approvalMethod: string;
	/// <summary>
	/// ������ѡ��
	/// 0:������ѡ��
	/// 1:����ѡ��
	/// </summary>
	approvalFilter: boolean;
	/// <summary>
	/// 1:�˻��ϼ�
	/// 2:��������
	/// 3:�Զ����˻ؽڵ�
	/// </summary>
	returnType: number;
	/// <summary>
	/// ��ReturnType=3 ʱ
	/// ��ʾ�Զ����˻ص�Id
	/// </summary>
	returnNodeId: any[];

	/// <summary>
	/// ����NoticeType�Ķ�Ӧ������
	/// </summary>
	approveNoticeData: NotificationSettings;

	execute: any;

	// public DesignCC CC { get; set; }

	permissions: any[];

	conditionMode: number;

	conditionList: any[];

	/// <summary>
	/// WaitAny:����һ����֧��ɼ���
	/// WaitAll:���з�֧����Ҫ���
	/// </summary>
	joinMode: string;

	/// <summary>
	/// 1:����Ҫѡ���֧
	/// 2������ѡ���֧
	/// </summary>
	forkMode: string;

	/// <summary>
	/// 0:��Ҫ����
	/// 1:�Զ�ͨ��
	/// </summary>
	autoApproval: number;
}

export interface NotificationSettings {
	/// <summary>
	/// ����ͨ����ͬ��
	/// </summary>
	Approve: NotificationType;
	/// <summary>
	/// �����ܾ�
	/// </summary>
	Reject: NotificationType;
	/// <summary>
	/// �˻�
	/// </summary>
	Return: NotificationType;
	/// <summary>
	/// ����
	/// </summary>
	Revoked: NotificationType;
	/// <summary>
	/// ת��
	/// </summary>
	AssignTo: NotificationType;
	/// <summary>
	/// ȡ��
	/// </summary>
	Cancell: NotificationType;
	/// <summary>
	/// �߰�
	/// </summary>
	Escalate: NotificationType;
	/// <summary>
	/// ����
	/// </summary>
	Todo: NotificationType;
	/// <summary>
	/// ���
	/// </summary>
	Finish: NotificationType;
}

export interface NotificationType {
	/// <summary>
	/// 0:ͬ����ʼ����
	/// 1:ϵͳĬ��
	/// 2:�Զ���
	/// 3:�ر�
	/// </summary>
	Value: number;
	/// <summary>
	/// ��� Value ��ValueData ��������Զ����ģ��ID
	/// </summary>
	ValueData: any;
	/// <summary>
	/// �����ʼ�������
	/// </summary>
	AppendTo: any[];
	/// <summary>
	/// �����ʼ�������
	/// </summary>
	AppendCc: any[];
}
